package com.rf.exchange.module.trade.controller.admin.contract.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约委托记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractOrderPageReqVO extends PageParam {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "仓位订单号")
    private String positionRecordNo;

    @Schema(description = "会员id", example = "26477")
    private Long userId;

    @Schema(description = "会员账号", example = "李四")
    private String username;

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "价格类型 0:市价订单 1:限价订单", example = "2")
    private Integer priceType;

    @Schema(description = "多空类型 0:做空 1:做多")
    private Integer shortLong;

    @Schema(description = "委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托", example = "1")
    private Integer orderType;

    @Schema(description = "订单状态 0:委托中 1:已成交", example = "1")
    private Integer orderStatus;

    @Schema(description = "委托金额")
    private BigDecimal orderAmount;

    @Schema(description = "委托金额法币币种")
    private String orderAmountCurrency;

    @Schema(description = "委托币数量")
    private BigDecimal orderVolume;

    @Schema(description = "委托价格", example = "29506")
    private BigDecimal orderPrice;

    @Schema(description = "成交价格", example = "15906")
    private BigDecimal transactionPrice;

    @Schema(description = "手续费")
    private BigDecimal fee;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}