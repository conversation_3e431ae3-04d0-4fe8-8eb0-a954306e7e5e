package com.rf.exchange.module.trade.controller.admin.contract.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约持仓记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractPositionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24570")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "记录编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("记录编号")
    private String recordNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String code;

    @Schema(description = "保证金", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("保证金")
    private BigDecimal margin;

    @Schema(description = "保证金法币币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("保证金法币币种")
    private String marginCurrency;

    @Schema(description = "多空类型 0:做空 1:做多", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("多空类型 0:做空 1:做多")
    private Integer shortLong;

    @Schema(description = "平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平")
    private Integer closeType;

    @Schema(description = "仓位状态 0:持仓中 1:已平仓", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("仓位状态 0:持仓中 1:已平仓")
    private Integer positionStatus;

    @Schema(description = "开仓价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1615")
    @ExcelProperty("开仓价格")
    private BigDecimal openPrice;

    @Schema(description = "平仓价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "13154")
    @ExcelProperty("平仓价格")
    private BigDecimal closePrice;

    @Schema(description = "预估强平价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "27331")
    @ExcelProperty("预估强平价格")
    private BigDecimal estimateLiquidatePrice;

    @Schema(description = "持仓量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("持仓量")
    private BigDecimal volume;

    @Schema(description = "持仓资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("持仓资产")
    private String volumeAsset;

    @Schema(description = "收益", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收益")
    private BigDecimal profitLoss;

    @Schema(description = "收益率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收益率")
    private BigDecimal profitLossRate;

    @Schema(description = "杠杆倍数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("杠杆倍数")
    private Integer leverage;

    @Schema(description = "开仓时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开仓时间")
    private Long openTime;

    @Schema(description = "平仓时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平仓时间")
    private Long closeTime;
}