package com.rf.exchange.module.trade.controller.admin.duration;

import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.framework.tenant.core.service.TenantFrameworkService;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.module.trade.controller.admin.duration.vo.*;
import com.rf.exchange.module.trade.dal.dataobject.duration.TradeDurationDO;
import com.rf.exchange.module.trade.service.duration.TradeDurationService;

@Tag(name = "管理后台 - 订单时长配置")
@RestController
@RequestMapping("/trade/duration")
@Validated
public class TradeDurationController extends TenantBaseController {

    @Resource
    private TradeDurationService durationService;

    @PostMapping("/create")
    @Operation(summary = "创建订单时长配置")
    @PreAuthorize("@ss.hasPermission('trade:duration:create')")
    public CommonResult<Long> createDuration(@Valid @RequestBody TradeDurationSaveReqVO createReqVO) {
        final Long id = TenantUtils.execute(createReqVO.getTenantId(), () -> durationService.createDuration(createReqVO));
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新订单时长配置")
    @PreAuthorize("@ss.hasPermission('trade:duration:update')")
    public CommonResult<Boolean> updateDuration(@Valid @RequestBody TradeDurationSaveReqVO updateReqVO) {
        TenantUtils.execute(updateReqVO.getTenantId(), () -> durationService.updateDuration(updateReqVO));
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单时长配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:duration:delete')")
    public CommonResult<Boolean> deleteDuration(@RequestParam("id") Long id) {
        setupTenantId();
        durationService.deleteDuration(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单时长配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:duration:query')")
    public CommonResult<TradeDurationRespVO> getDuration(@RequestParam("id") Long id) {
        setupTenantId();
        TradeDurationDO duration = durationService.getDuration(id);
        return success(BeanUtils.toBean(duration, TradeDurationRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得订单时长配置分页")
    @PreAuthorize("@ss.hasPermission('trade:duration:query')")
    public CommonResult<PageResult<TradeDurationRespVO>> getDurationPage(@Valid @RequestBody TradeDurationPageReqVO pageReqVO) {
        final PageResult<TradeDurationDO> pageResult = TenantUtils.execute(pageReqVO.getTenantId(), () -> durationService.getDurationPage(pageReqVO));
        return success(BeanUtils.toBean(pageResult, TradeDurationRespVO.class));
    }
}