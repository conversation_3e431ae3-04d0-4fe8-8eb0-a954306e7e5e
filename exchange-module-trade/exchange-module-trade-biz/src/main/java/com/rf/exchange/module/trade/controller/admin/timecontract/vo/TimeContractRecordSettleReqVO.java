package com.rf.exchange.module.trade.controller.admin.timecontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025-05-12
 */
@Schema(description = "管理后台 - 限时合约交易记录结算请求 VO")
@Data
public class TimeContractRecordSettleReqVO {
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单编号不能为空")
    private String orderNo;
}
