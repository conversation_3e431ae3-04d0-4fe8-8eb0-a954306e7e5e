package com.rf.exchange.module.trade.controller.app.tradetotal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
public class AppContractSummaryRespVO {

    @Schema(description = "持仓估值")
    private String contractValue = "0.00";

    @Schema(description = "总盈亏")
    private String totalProfitLoss = "0.00";

    @Schema(description = "原始保证金")
    private String marginTotal = "0";

    @Schema(description = "可用本金总额")
    private String validBalance = "0";
}
