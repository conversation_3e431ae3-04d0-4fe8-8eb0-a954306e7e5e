package com.rf.exchange.module.trade.controller.app.trade;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.ratelimiter.core.annotation.RateLimiter;
import com.rf.exchange.framework.ratelimiter.core.keyresolver.impl.UserRateLimiterKeyResolver;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTimeContractConfigRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractBuyReqVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractOrderDetailRespVO;
import com.rf.exchange.module.trade.controller.app.trade.vo.AppTradeTimeContractPageReqVO;
import com.rf.exchange.module.trade.service.timecontract.TimeContractRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * <AUTHOR>
 * @since 2024-07-12
 */
@Tag(name = "用户APP - 限时合约接口")
@RestController
@RequestMapping("/trade")
public class AppTimeContractController {

    @Resource
    private TimeContractRecordService timeContractRecordService;

    @Operation(summary = "限时合约配置")
    @GetMapping("/config/time-contract")
    @PreAuthenticated
    public CommonResult<List<AppTimeContractConfigRespVO>> timeContractConfig(@RequestParam("code") String tradePairCode) {
        return CommonResult.success(timeContractRecordService.getTimeContractConfig(tradePairCode, getTenantId()));
    }

    @Operation(summary = "限时合约买卖")
    @PostMapping("/time-contract/create")
    @PreAuthenticated
    @RateLimiter(count = 1, keyResolver = UserRateLimiterKeyResolver.class)
    public CommonResult<Long> createTimeContract(@Valid @RequestBody AppTradeTimeContractBuyReqVO reqVO) {
        return success(timeContractRecordService.createTimeContractRecord(reqVO, getLoginUserId(),getTenantId()));
    }

    @Operation(summary = "限时合约订单详情")
    @GetMapping("/time-contract/order-detail")
    @PreAuthenticated
    public CommonResult<AppTradeTimeContractOrderDetailRespVO> getTimeContract(@RequestParam("id") Long id) {
        return success(timeContractRecordService.getTimeContractOrderDetail(id, getLoginUserId(), getTenantId()));
    }

    @Operation(summary = "限时合约订单分页")
    @GetMapping("/order/time-contract/page")
    @PreAuthenticated
    public CommonResult<PageResult<AppTradeTimeContractOrderDetailRespVO>> pageTimeContract(@Valid AppTradeTimeContractPageReqVO reqVO) {
        return success(timeContractRecordService.appGetTimeContractOrderPage(reqVO, getLoginUserId(), getTenantId()));
    }

}
