package com.rf.exchange.module.trade.controller.admin.timecontract;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.module.trade.controller.admin.timecontract.vo.*;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import com.rf.exchange.module.trade.service.timecontract.TimeContractRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

@Tag(name = "管理后台 - 限时合约交易记录")
@RestController
@RequestMapping("/trade/time-contract-record")
@Validated
public class TimeContractRecordController extends TenantBaseController {

    @Resource
    private TimeContractRecordService timeContractRecordService;

    @PutMapping("/update")
    @Operation(summary = "更新限时合约交易记录")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:update')")
    public CommonResult<Boolean> updateTimeContractRecord(@Valid @RequestBody TimeContractRecordSaveReqVO updateReqVO) {
        setupTenantId();
        timeContractRecordService.updateTimeContractRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除限时合约交易记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:delete')")
    public CommonResult<Boolean> deleteTimeContractRecord(@RequestParam("id") Long id) {
        setupTenantId();
        timeContractRecordService.deleteTimeContractRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得限时合约交易记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:query')")
    public CommonResult<TimeContractRecordRespVO> getTimeContractRecord(@RequestParam("id") Long id) {
        setupTenantId();
        TimeContractRecordDO timeContractRecord = timeContractRecordService.getTimeContractRecord(id);
        return success(BeanUtils.toBean(timeContractRecord, TimeContractRecordRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得限时合约交易记录分页")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:query')")
    public CommonResult<PageResult<TimeContractRecordRespVO>> getTimeContractRecordPage(@Valid @RequestBody TimeContractRecordPageReqVO pageReqVO) {
        setupTenantId();
        PageResult<TimeContractRecordDO> pageResult = timeContractRecordService.getTimeContractRecordPage(pageReqVO, getTenantId());
        return success(BeanUtils.toBean(pageResult, TimeContractRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出限时合约交易记录 Excel")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTimeContractRecordExcel(@Valid TimeContractRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        setupTenantId();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TimeContractRecordDO> list = timeContractRecordService.getTimeContractRecordPage(pageReqVO, getTenantId()).getList();
        // 导出 Excel
        ExcelUtils.write(response, "限时合约交易记录.xls", "数据", TimeContractRecordRespVO.class,
                        BeanUtils.toBean(list, TimeContractRecordRespVO.class));
    }

    @PostMapping("/draw-manual")
    @Operation(summary = "限时时合约订单手动开奖")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:update')")
    public CommonResult<TimeContractRecordRespVO> manualDraw(@Valid @RequestBody TimeContractRecordDrawReqVO reqVO) {
        setupTenantId();
        return success(timeContractRecordService.manualDrawTimeContract(reqVO));
    }

    @PostMapping("/settle-again")
    @Operation(summary = "限时合约订单-手动派奖")
    @PreAuthorize("@ss.hasPermission('trade:time-contract-record:settle')")
    public CommonResult<Boolean> settleOrderAgain(@Valid @RequestBody TimeContractRecordSettleReqVO reqVO) {
        setupTenantId();
        timeContractRecordService.settleOrderAgain(reqVO);
        return success(true);
    }
}