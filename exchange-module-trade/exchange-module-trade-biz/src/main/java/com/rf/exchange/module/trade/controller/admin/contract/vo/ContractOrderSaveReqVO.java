package com.rf.exchange.module.trade.controller.admin.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约委托记录新增/修改 Request VO")
@Data
public class ContractOrderSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21452")
    private Long id;

    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean visible = true;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "仓位订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "仓位订单号不能为空")
    private String positionRecordNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26477")
    @NotNull(message = "会员id不能为空")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "会员账号不能为空")
    private String username;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易对代码不能为空")
    private String code;

    @Schema(description = "价格类型 0:市价订单 1:限价订单", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "价格类型 0:市价订单 1:限价订单不能为空")
    private Integer priceType;

    @Schema(description = "多空类型 0:做空 1:做多", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "多空类型 0:做空 1:做多不能为空")
    private Integer shortLong;

    @Schema(description = "委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托不能为空")
    private Integer orderType;

    @Schema(description = "订单状态 0:委托中 1:已成交", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单状态 0:委托中 1:已成交不能为空")
    private Integer orderStatus;

    @Schema(description = "委托金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "委托金额不能为空")
    private BigDecimal orderAmount;

    @Schema(description = "委托金额法币币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "委托金额法币币种不能为空")
    private String orderAmountCurrency;

    @Schema(description = "委托币数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "委托币数量不能为空")
    private BigDecimal orderVolume;

    @Schema(description = "委托价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "29506")
    @NotNull(message = "委托价格不能为空")
    private BigDecimal orderPrice;

    @Schema(description = "成交价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "15906")
    @NotNull(message = "成交价格不能为空")
    private BigDecimal transactionPrice;

    @Schema(description = "手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手续费不能为空")
    private BigDecimal fee;

    @Schema(description = "杠杆倍数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer leverage;

}