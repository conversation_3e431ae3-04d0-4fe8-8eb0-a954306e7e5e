package com.rf.exchange.module.trade.controller.app.tradetotal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@Schema(description = "今日收益合计")
public class AppTradeTotalRespVO {
    @Schema(description = "类型 0:现货,1:期货合约,2杠杆保证金,3:限时", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private int type;
    @Schema(description = "单量", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private int tradeCount;
    @Schema(description = "收益", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private BigDecimal profit;

}
