package com.rf.exchange.module.trade.controller.admin.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约持仓记录新增/修改 Request VO")
@Data
public class ContractPositionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24570")
    private Long id;

    @Schema(description = "记录编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "记录编号不能为空")
    private String recordNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4862")
    @NotNull(message = "会员id不能为空")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "会员账号不能为空")
    private String username;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易对代码不能为空")
    private String code;

    @Schema(description = "资产类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "资产类型不能为空")
    private Integer assetType;

    @Schema(description = "多空类型 0:做空 1:做多", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "多空类型 0:做空 1:做多不能为空")
    private Integer shortLong;

    @Schema(description = "平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平不能为空")
    private Integer closeType;

    @Schema(description = "仓位状态 0:持仓中 1:已平仓", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "仓位状态 0:持仓中 1:已平仓不能为空")
    private Integer positionStatus;

    @Schema(description = "初始保证金", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "初始保证金不能为空")
    private BigDecimal margin;

    @Schema(description = "初始保证金法币币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "初始保证金法币币种不能为空")
    private String marginCurrency;

    @Schema(description = "开仓价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1615")
    @NotNull(message = "开仓价格不能为空")
    private BigDecimal openPrice;

    @Schema(description = "平仓价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "13154")
    @NotNull(message = "平仓价格不能为空")
    private BigDecimal closePrice;

    @Schema(description = "预估强平价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "27331")
    @NotNull(message = "预估强平价格不能为空")
    private BigDecimal estimateLiquidatePrice;

    @Schema(description = "持仓量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "持仓量不能为空")
    private BigDecimal volume;

    @Schema(description = "持仓资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "持仓资产不能为空")
    private String volumeAsset;

    @Schema(description = "持仓量类型 0:计价币种 1:报价币种 2:固定CNY计价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "持仓量类型 0:计价币种 1:报价币种 2:固定CNY计价不能为空")
    private Integer volumeType;

    @Schema(description = "预估收益", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预估收益不能为空")
    private BigDecimal estimatedIncome;

    @Schema(description = "实际收益", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "实际收益不能为空")
    private BigDecimal actualIncome;

    @Schema(description = "实际收益率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "实际收益率不能为空")
    private BigDecimal actualIncomeRate;

    @Schema(description = "杠杆倍数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "杠杆倍数不能为空")
    private Integer leverage;

    @Schema(description = "开仓时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开仓时间不能为空")
    private Long openTime;

    @Schema(description = "平仓时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "平仓时间不能为空")
    private Long closeTime;

}