package com.rf.exchange.module.trade.controller.admin.duration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 订单时长配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradeDurationRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14527")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "时长", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时长")
    private Integer duration;

    @Schema(description = "单位时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位时间")
    private Integer timeUnit;

    @Schema(description = "最小金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最小金额")
    private Integer minAmount;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态 0开启 1停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开启状态")
    private Integer status;

    @Schema(description = "收益率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收益率")
    private BigDecimal profitRate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "租户id")
    @ExcelProperty("租户id")
    private Long tenantId;

}