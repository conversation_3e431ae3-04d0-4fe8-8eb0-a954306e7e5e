package com.rf.exchange.module.trade.controller.app.trade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-07-12
 */
@Schema(description = "限时合约买卖参数")
@Data
public class AppTradeTimeContractBuyReqVO {

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{TRADE_PAIR_NOT_EMPTY}")
    private String code;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{AMOUNT_NOT_EMPTY}")
    private BigDecimal amount;

    @Schema(description = "交易方向 0:做空/买跌 1:做多/买涨", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{TRADE_DIRECT_ERROR}")
    private Integer shortLong;

    @Schema(description = "限时时间配置id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{TRADE_DURATION_ERROR}")
    private Long durationId;

    @Schema(description = "发送时间（时间戳）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{TRADE_SEND_TIME_ERROR}")
    private Long sendTime;

    // 此字段废弃下单接口直接从系统或获取当前交易对的实时价格
    @Schema(description = "买入价格")
    private BigDecimal price;
}
