package com.rf.exchange.module.trade.matchengine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.member.api.balance.MemberBalanceApi;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.system.util.tenantdict.TenantDictUtils;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.dal.redis.ContractLiquidateEngineRedisDAO;
import com.rf.exchange.module.trade.enums.ContractMarginTypeEnum;
import com.rf.exchange.module.trade.enums.ShortLongEnum;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import com.rf.exchange.module.trade.service.contract.ContractWebSocketService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.rf.exchange.module.system.enums.DictTypeConstants.TENANT_CURRENCY_CONFIG;
import static com.rf.exchange.module.system.enums.DictTypeConstants.TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY;

/**
 * <AUTHOR>
 * @since 2024-08-30
 */
@Slf4j
@Component
public class ContractLiquidateEngine {

    @Resource
    private ContractPositionService positionRecordService;
    @Resource
    private ContractLiquidateEngineRedisDAO liquidateRedisDAO;
    @Resource
    private ContractWebSocketService contractWebSocketService;
    @Resource
    @Lazy
    private MemberBalanceApi memberBalanceApi;
    @Resource
    @Lazy
    private CurrencyRateApi currencyRateApi;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;

    /**
     * 持仓记录Map
     * <p>
     * key: 仓位记录编号
     */
    private static final ConcurrentMap<String, ContractPositionDO> positionMap = new ConcurrentHashMap<>();

    /**
     * 交易对代码仓位Map
     * <p>
     * key: 交易对代码
     */
    private static final ConcurrentMap<String, CopyOnWriteArrayList<ContractPositionDO>> codePositionMap = new ConcurrentHashMap<>();

    /**
     * 用户持仓记录Map
     * <p>
     * key: 用户id
     */
    private static final ConcurrentMap<Long, CopyOnWriteArrayList<ContractPositionDO>> userPositionMap = new ConcurrentHashMap<>();

    /**
     * 合约仓位的强平价格 Map
     * <p>
     * key: 仓位记录编号
     */
    private static final ConcurrentMap<String, BigDecimal> liquidatePriceMap = new ConcurrentHashMap<>();

    /**
     * 合约仓位的保证金率 Map
     * <p>
     * key: 仓位编号
     */
    private static final ConcurrentMap<String, BigDecimal> marginRateMap = new ConcurrentHashMap<>();

    /**
     * 合约仓位的浮动盈亏 Map
     * <p>
     * key: 仓位编号
     */
    private static final ConcurrentMap<String, BigDecimal> profitLossMap = new ConcurrentHashMap<>();

    /**
     * 获取持仓的强平价格
     *
     * @param positionNo 仓位编号
     * @return 强平价格
     */
    public BigDecimal getLiquidatePrice(String positionNo) {
        // 因为MatchEngine只有一个服务实例，所以LiquidateEngine并不是每个实例都会有值，所以这里取redis中的值
        return liquidateRedisDAO.getLiquidatePriceByPositionNo(positionNo);
    }

    /**
     * 获取持仓的保证金率
     *
     * @param positionNo 仓位编号
     * @return 保证金率
     */
    public BigDecimal getMarginRate(String positionNo) {
        // 因为MatchEngine只有一个服务实例，所以LiquidateEngine并不是每个实例都会有值，所以这里取redis中的值
        return liquidateRedisDAO.getMarginRateByPositionNo(positionNo);
    }

    /**
     * 获取持仓的盈亏收益
     *
     * @param positionNo 仓位编号
     * @return 盈亏金额
     */
    public BigDecimal getProfitLoss(String positionNo) {
        // 因为MatchEngine只有一个服务实例，所以LiquidateEngine并不是每个实例都会有值，所以这里取redis中的值
        return liquidateRedisDAO.getProfitLossByPositionNo(positionNo);
    }

    /**
     * 重新加载所有需要强平的持仓记录
     *
     * @param positions 持仓记录列表
     */
    public void updateWithAllPositions(List<ContractPositionDO> positions) {
        // 先清除所有数据再重新添加
        positionMap.clear();
        userPositionMap.clear();
        codePositionMap.clear();
        liquidateRedisDAO.removeAll();
        // 再次添加持仓记录
        for (ContractPositionDO position : positions) {
            addOpenedPosition(position);
        }
    }

    /**
     * 添加用户持仓中的持仓信息
     *
     * @param position 持仓信息
     */
    public void addOpenedPosition(ContractPositionDO position) {
        if (null == position) {
            return;
        }
        // 如果已经存在此持仓记录则返回
        if (positionMap.containsKey(position.getRecordNo())) {
            return;
        }

        positionMap.put(position.getRecordNo(), position);

        CopyOnWriteArrayList<ContractPositionDO> positions = userPositionMap.get(position.getUserId());
        if (null == positions) {
            positions = new CopyOnWriteArrayList<>();
            userPositionMap.putIfAbsent(position.getUserId(), positions);
        }
        positions.add(position);

        CopyOnWriteArrayList<ContractPositionDO> codePositions = codePositionMap.get(position.getCode());
        if (null == codePositions) {
            codePositions = new CopyOnWriteArrayList<>();
            codePositionMap.putIfAbsent(position.getCode(), codePositions);
        }
        codePositions.add(position);

        // 如果持仓是逐仓模式则添加之后立即计算强平价格，因为逐仓模式的保证金是固定的，所以计算一次之后就不用根据用户余额变化再次计算了
        if (position.getMarginType().equals(ContractMarginTypeEnum.ISOLATED_MARGIN.getType())) {
            calculateIsolatedMarginLiquidatePrice(position);
        }
    }

    /**
     * 移除全仓持仓信息
     *
     * @param userId           用户id
     * @param code             交易对代码
     * @param positionRecordNo 仓位编号
     */
    public void removeOpenedPosition(long userId, String code, String positionRecordNo) {
        if (StrUtil.isEmpty(positionRecordNo)) {
            return;
        }

        positionMap.remove(positionRecordNo);
        liquidatePriceMap.remove(positionRecordNo);
        marginRateMap.remove(positionRecordNo);
        profitLossMap.remove(positionRecordNo);

        final CopyOnWriteArrayList<ContractPositionDO> positions = userPositionMap.get(userId);
        if (CollUtil.isNotEmpty(positions)) {
            positions.removeIf(positionRecord -> positionRecord.getRecordNo().equals(positionRecordNo));
            // 如果用户没有任何持仓记录了则移除
            if (CollUtil.isEmpty(positions)) {
                userPositionMap.remove(userId);
            }
        }

        final CopyOnWriteArrayList<ContractPositionDO> codePositions = codePositionMap.get(code);
        if (CollUtil.isNotEmpty(codePositions)) {
            codePositions.removeIf(positionRecord -> positionRecord.getRecordNo().equals(positionRecordNo));
            if (CollUtil.isEmpty(codePositions)) {
                codePositionMap.remove(code);
            }
        }

        // 从redis中移除不在需要的订单号信息
        liquidateRedisDAO.removeUnusefulKeyValue(positionRecordNo);
        log.info("[强平引擎] 移除待强平的持仓 编号:[{}] 用户id:[{}] 交易对代码:[{}]", positionRecordNo, userId, code);
    }

    /**
     * 重新计算code下所有持仓的强平价格
     *
     * @param code          交易对代码
     * @param realTimePrice 实时价格
     */
    public void recalculateLiquidationPrice(String code, BigDecimal realTimePrice) {
        TenantContextHolder.setIgnore(true);
        // 获取code对应的所有持仓记录
        final CopyOnWriteArrayList<ContractPositionDO> codePositions = codePositionMap.get(code);
        if (CollUtil.isNotEmpty(codePositions)) {
            for (ContractPositionDO position : codePositions) {
                liquidateCalculateTask(position, realTimePrice);
            }
        }
    }

    @Async("liquidateCalculateExecutor")
    public void liquidateCalculateTask(ContractPositionDO position, BigDecimal realTimePrice) {
        if (position.getMarginType().equals(ContractMarginTypeEnum.ISOLATED_MARGIN.getType())) {
            calculateIsolateProfitAndMarginRate(position, realTimePrice);
        }
    }

    /**
     * 计算逐仓模式的盈亏金额和保证金率
     *
     * @param position      持仓记录
     * @param realTimePrice 实时价格
     */
    private void calculateIsolateProfitAndMarginRate(ContractPositionDO position, BigDecimal realTimePrice) {
        TenantContextHolder.setIgnore(true);
        final TradePairRespDTO tradePairDTO = tradePairApi.getTradePairCachedByCode(position.getCode());

        // 获取租户的默认法币币种配置
        String tenantDefaultCurrency = TenantDictUtils.parseDictDataValue(position.getTenantId(), TENANT_CURRENCY_CONFIG, TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY);
        if (StrUtil.isEmpty(tenantDefaultCurrency)) {
            log.error("租户的默认法币配置为空 无法计算合约持仓的盈亏 租户id:[{}]", position.getTenantId());
            return;
        }

        CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), tradePairDTO.getQuoteAsset(), position.getMarginCurrency());
        if (currencyRateDTO == null) {
            log.error("获取不到汇率数据 租户:[{}] 基础法币:[{}] 报价法币:[{}]", position.getTenantId(), position.getVolumeValueCurrency(), position.getMarginCurrency());
            return;
        }

        // 计算盈亏金额
        BigDecimal profitLoss = BigDecimal.ZERO;
        if (ShortLongEnum.SHORT.getType() == position.getShortLong()) {
            profitLoss = (position.getOpenPrice().subtract(realTimePrice)).multiply(position.getVolume()).multiply(BigDecimal.valueOf(position.getLeverage()));
        } else if (ShortLongEnum.LONG.getType() == position.getShortLong()) {
            profitLoss = (realTimePrice.subtract(position.getOpenPrice())).multiply(position.getVolume()).multiply(BigDecimal.valueOf(position.getLeverage()));
        }
        // 转换一次汇率
        profitLoss = profitLoss.multiply(currencyRateDTO.getRateValue());
        profitLossMap.put(position.getRecordNo(), profitLoss);
        // 换算保证金成USDT
        final BigDecimal marginExchanged = position.getMargin().divide(currencyRateDTO.getRateValue(), 4, RoundingMode.HALF_UP);
        // 计算保证金率
        BigDecimal marginRate;
        if (profitLoss.compareTo(BigDecimal.ZERO) >= 0) {
            marginRate = (marginExchanged.add(profitLoss)).divide(position.getMargin(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        } else {
            marginRate = marginExchanged.divide(profitLoss.abs(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        marginRateMap.put(position.getRecordNo(), marginRate);

        // 更新redis中的缓存
        liquidateRedisDAO.updateProfitLossMap(profitLossMap);
        liquidateRedisDAO.updateMarginRateMap(marginRateMap);

        // 推送持仓信息的ws消息
        contractWebSocketService.sendPositionDataMessage(position);
    }

    /**
     * 计算逐仓模式的强平价格
     *
     * @param position 持仓记录
     */
    private void calculateIsolatedMarginLiquidatePrice(ContractPositionDO position) {
        TenantContextHolder.setIgnore(true);
        final TradePairRespDTO tradePairDTO = tradePairApi.getTradePairCachedByCode(position.getCode());

        // 获取租户的默认法币币种配置
        String tenantDefaultCurrency = TenantDictUtils.parseDictDataValue(position.getTenantId(), TENANT_CURRENCY_CONFIG, TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY);
        if (StrUtil.isEmpty(tenantDefaultCurrency)) {
            log.error("无法计算强平价格,租户的默认法币配置为空 无法计算合约持仓的盈亏 租户id:[{}]", position.getTenantId());
            return;
        }

        CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), tradePairDTO.getQuoteAsset(), position.getMarginCurrency());
        if (currencyRateDTO == null) {
            log.error("无法计算强平价格,获取不到汇率数据 租户:[{}] 基础法币:[{}] 报价法币:[{}]", position.getTenantId(), position.getVolumeValueCurrency(), position.getMarginCurrency());
            return;
        }

        /*
         * 获取换算过汇率之后的保证金
         * 如果用户下单的保证金货币是CNY则换算成USD金额
         */
        final BigDecimal marginExchanged = position.getMargin().divide(currencyRateDTO.getRateValue(), 4, RoundingMode.HALF_UP);
        // 价差
        final BigDecimal priceSpread = marginExchanged.divide(position.getVolume(), 4, RoundingMode.HALF_UP);
        BigDecimal liquidatePrice;
        if (ShortLongEnum.SHORT.getType() == position.getShortLong()) {
            liquidatePrice = position.getOpenPrice().add(priceSpread);
        } else {
            liquidatePrice = position.getOpenPrice().subtract(priceSpread);
        }
        liquidatePriceMap.put(position.getRecordNo(), liquidatePrice);
        // 更新redis中的缓存
        liquidateRedisDAO.updateLiquidatePriceMap(liquidatePriceMap);
    }

    ///**
    // * 计算全仓模式的强平价格
    // *
    // * @param position 持仓记录
    // */
    //private void calculateCrossMarginLiquidatePrice(ContractPositionDO position) {
    //    CopyOnWriteArrayList<ContractPositionDO> userPositions = userPositionMap.get(position.getUserId());
    //    if (CollUtil.isNotEmpty(userPositions)) {
    //        // 获取租户的默认法币币种配置
    //        //String tenantDefaultCurrency = TenantDictUtils.parseDictDataValue(position.getTenantId(), TENANT_CURRENCY_CONFIG, TENANT_CURRENCY_CONFIG_LABEL_BASE_CURRENCY);
    //
    //        // 获取缓存的用户余额
    //        MemberBalanceDTO userBalanceDTO = memberBalanceApi.getMemberBalanceCached(position.getUserId());
    //        // 所有交易对的实时价格
    //        final Map<String, BigDecimal> allTradeRealTimePriceMap = ContractOrderMatchEngine.tmpPriceMap;
    //
    //        BigDecimal totalProfitLoss = BigDecimal.ZERO;
    //        // 浮动盈亏总计
    //        BigDecimal contractBalance = BigDecimal.ZERO;
    //
    //        // 先计算所有持仓的总收益，用于计算用户余额加上浮动盈亏
    //        for (ContractPositionDO userPosition : userPositions) {
    //            // 获取持仓
    //            final BigDecimal positionRealPrice = allTradeRealTimePriceMap.get(userPosition.getCode());
    //            if (null == positionRealPrice) {
    //                continue;
    //            }
    //
    //            // 获取交易对报价资产和委托订单法币之间的汇率, 如果委托订单的金额是CNY，交易对的报价资产为USD或者USDT，则这里的汇率为 USD/CNY
    //            // volumeValueCurrency/marginCurrency
    //            CurrencyRateDTO currencyRateDTO = currencyRateApi.getTenantCurrencyRate(position.getTenantId(), position.getVolumeValueCurrency(), position.getMarginCurrency());
    //            if (currencyRateDTO == null) {
    //                throw exception(TRADE_CONTRACT_CURRENCY_NOT_EXISTS);
    //            }
    //            BigDecimal profitLoss = BigDecimal.ZERO;
    //            if (ShortLongEnum.SHORT.getType() == userPosition.getShortLong()) {
    //                profitLoss = (userPosition.getOpenPrice().subtract(positionRealPrice)).multiply(userPosition.getVolume());
    //            } else if (ShortLongEnum.LONG.getType() == userPosition.getShortLong()) {
    //                profitLoss = (positionRealPrice.subtract(userPosition.getOpenPrice())).multiply(userPosition.getVolume());
    //            }
    //            //
    //            profitLossMap.put(userPosition.getRecordNo(), profitLoss);
    //            //
    //            contractBalance = contractBalance.add(profitLoss).add(userPosition.getMargin());
    //            //
    //            totalProfitLoss = totalProfitLoss.add(profitLoss);
    //
    //            log.info("仓位编号:[{}] userId:[{}] 盈亏:[{}] 保证金:[{}] 合约余额:[{}] 总盈亏:[{}]",
    //                    userPosition.getRecordNo(),
    //                    userPosition.getUserId(),
    //                    profitLoss,
    //                    userPosition.getMargin(),
    //                    contractBalance,
    //                    totalProfitLoss);
    //        }
    //
    //        // 剩余余额=用户的可用余额+用户的合约余额
    //        BigDecimal leftBalance = userBalanceDTO.getUsdtBalance().add(contractBalance);
    //
    //        log.info("仓位编号:[{}] 合约余额:[{}] 总收益:[{}]", position.getRecordNo(), contractBalance, totalProfitLoss);
    //
    //        // 计算保证金率 10%返回10 0.1%返回0.1% 100%返回100
    //        for (ContractPositionDO userPosition : userPositions) {
    //            BigDecimal rate = (leftBalance.add(userPosition.getMargin())).divide(userPosition.getMargin(), 2, RoundingMode.HALF_DOWN).multiply(BigDecimal.valueOf(100));
    //            marginRateMap.put(userPosition.getRecordNo(), rate);
    //        }
    //
    //        if (leftBalance.compareTo(BigDecimal.ZERO) <= 0) {
    //            log.info("用户余额为0，触发所有持仓强平");
    //            // 如果用户剩余余额则强平所有用户的所有持仓
    //            positionRecordService.forceLiquidateAllContractPosition(position, ContractOrderMatchEngine.tmpPriceMap);
    //            liquidateRedisDAO.removeUnusefulKeyValue(position.getRecordNo());
    //        } else {
    //
    //            // 重新计算用户的所有持仓的强平价格
    //            for (ContractPositionDO userPosition : userPositions) {
    //                final BigDecimal currentPL = profitLossMap.get(userPosition.getRecordNo());
    //                if (currentPL == null) {
    //                    continue;
    //                }
    //                final BigDecimal liquidatePrice = internalCalculateLiquidatePrice(userPosition, userBalanceDTO.getUsdtBalance(), currentPL);
    //                liquidatePriceMap.put(userPosition.getRecordNo(), liquidatePrice);
    //                //log.info("重新计算强平价格为:{} 订单号:{}", liquidatePrice, position.getRecordNo());
    //            }
    //
    //            // 更新redis中的缓存
    //            liquidateRedisDAO.updateProfitLossMap(profitLossMap);
    //            liquidateRedisDAO.updateMarginRateMap(marginRateMap);
    //            liquidateRedisDAO.updateLiquidatePriceMap(liquidatePriceMap);
    //
    //            // 推送持仓信息的ws消息
    //            contractWebSocketService.sendUserContractPositionMessage(userPositions);
    //        }
    //    }
    //}

    /**
     * 检查是否持仓触发了强平条件
     *
     * @param code             交易对代码
     * @param currentPrice     当前价格
     * @param positionRecordNo 持仓记录编号
     * @return 返回true表示持仓触发强平价格
     */
    public boolean checkIfLiquidationPriceTriggered(String code, BigDecimal currentPrice, String positionRecordNo) {
        final ContractPositionDO position = positionMap.get(positionRecordNo);
        if (null == position) {
            log.error("持仓记录不存在 仓位记录编号:[{}]", positionRecordNo);
            return false;
        }
        final BigDecimal liquidatePrice = liquidatePriceMap.get(positionRecordNo);
        if (null == liquidatePrice) {
            log.error("持仓强平价格不存在 仓位记录编号:[{}]", positionRecordNo);
            return false;
        }
        // 如果持仓是多仓且当前交易对价格小于等于强平价格时，或者空仓且当前交易对价格大于等于强平价格时，触发强平
        boolean result = (ShortLongEnum.SHORT.getType() == position.getShortLong() && currentPrice.compareTo(liquidatePrice) >= 0) ||
                (ShortLongEnum.LONG.getType() == position.getShortLong() && currentPrice.compareTo(liquidatePrice) <= 0);
        log.info("判断持仓是否超过强平价 交易对:[{}] 仓位编号:[{}] 当前价:[{}] 强平价:[{}] 结果:[{}]", code, positionRecordNo, currentPrice, liquidatePrice, result);
        return result;
    }

    //private static BigDecimal internalCalculateLiquidatePrice(ContractPositionDO position, BigDecimal usdtBalance, BigDecimal profitLoss) {
    //    // 可用余额换算成对应持仓量的价格变化
    //    final BigDecimal actualBalance = usdtBalance.add(position.getMargin()).add(profitLoss);
    //    final BigDecimal priceChange = actualBalance.divide(position.getVolume(), 4, RoundingMode.HALF_UP).abs();
    //    if (ShortLongEnum.SHORT.getType() == position.getShortLong()) {
    //        final BigDecimal result = position.getOpenPrice().add(priceChange);
    //        log.info("仓位编号:[{}] 用户USDT:[{}] 保证金:[{}] 收益:[{}] 计算结果:[{}]", position.getRecordNo(), usdtBalance, position.getMargin(), profitLoss, result);
    //        return result;
    //    } else {
    //        final BigDecimal result = position.getOpenPrice().subtract(priceChange);
    //        log.info("仓位编号:[{}] 用户USDT:[{}] 保证金:[{}] 收益:[{}] 计算结果:[{}]", position.getRecordNo(), usdtBalance, position.getMargin(), profitLoss, result);
    //        return result;
    //    }
    //}
}
