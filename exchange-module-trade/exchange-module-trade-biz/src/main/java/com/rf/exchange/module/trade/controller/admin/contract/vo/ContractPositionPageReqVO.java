package com.rf.exchange.module.trade.controller.admin.contract.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约持仓记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractPositionPageReqVO extends PageParam {

    @Schema(description = "仓位单号")
    private String recordNo;

    @Schema(description = "会员id")
    private Long userId;

    @Schema(description = "会员账号")
    private String username;

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "多空类型 0:做空 1:做多")
    private Integer shortLong;

    @Schema(description = "平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平")
    private Integer closeType;

    @Schema(description = "仓位状态 0:持仓中 1:已平仓")
    private Integer positionStatus;

    @Schema(description = "开仓价格")
    private BigDecimal openPrice;

    @Schema(description = "平仓价格")
    private BigDecimal closePrice;

    @Schema(description = "杠杆倍数")
    private Integer leverage;

    @Schema(description = "开仓时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] openTime;

    @Schema(description = "平仓时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] closeTime;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}