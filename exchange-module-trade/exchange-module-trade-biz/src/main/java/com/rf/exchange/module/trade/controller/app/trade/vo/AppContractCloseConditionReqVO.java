package com.rf.exchange.module.trade.controller.app.trade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * APP 合约平仓 请求参数
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Schema(description = "合约接口平仓的参数 Request VO")
@Data
public class AppContractCloseConditionReqVO {

    @Schema(description = "仓位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{ARG_VALUE_ERROR}")
    private Long id;

    @Schema(description = "止损价格")
    private BigDecimal stopLossPrice;

    @Schema(description = "止盈价格")
    private BigDecimal stopProfitPrice;
}
