package com.rf.exchange.module.trade.controller.admin.contract;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionPageReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractPositionRespVO;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合约持仓记录")
@RestController
@RequestMapping("/trade/contract-position-record")
@Validated
public class ContractPositionController extends TenantBaseController {

    @Resource
    private ContractPositionService contractPositionService;

    @PostMapping("/page")
    @Operation(summary = "获得合约持仓记录分页")
    @PreAuthorize("@ss.hasPermission('trade:contract-position-record:query')")
    public CommonResult<PageResult<ContractPositionRespVO>> getContractPositionPage(@Valid @RequestBody ContractPositionPageReqVO pageReqVO) {
        setupTenantId();
        return success(contractPositionService.getContractPositionPage(pageReqVO));
    }

}