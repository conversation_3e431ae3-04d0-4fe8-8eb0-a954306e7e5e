package com.rf.exchange.module.trade.controller.admin.duration.vo;

import com.rf.exchange.framework.common.enums.DateIntervalEnum;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 订单时长配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeDurationPageReqVO extends PageParam {

    @Schema(description = "时长")
    private Integer duration;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;

    /**
     * {@link DateIntervalEnum}
     */
    @Schema(description = "单位时间 {数据字典中的时间间隔}")
    private Integer timeUnit;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}