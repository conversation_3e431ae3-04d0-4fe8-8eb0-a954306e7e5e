package com.rf.exchange.module.trade.controller.admin.timecontract.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 限时合约交易记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TimeContractRecordRespVO {

    @Schema(description = "记录id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21987")
    @ExcelProperty("记录id")
    private Long id;

    @Schema(description = "交易号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易号")
    private String tradeNo;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29688")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String code;

    @Schema(description = "时长（单位秒）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时长（单位秒）")
    private Integer duration;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("数量")
    private BigDecimal amount;

    @Schema(description = "交易方向 0做多 1做空", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易方向 0做多 1做空")
    private Integer shortLong;

    @Schema(description = "盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("盈利类型")
    private Integer profitType;

    @Schema(description = "用户盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用户盈利类型")
    private Integer userProfitType;

    @Schema(description = "盈利类型为3时的随机赢率", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("随机赢率")
    private BigDecimal randomRate;

    @Schema(description = "收益率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收益率")
    private BigDecimal profitRate;

    @Schema(description = "交易状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("交易状态")
    private Integer tradeStatus;

    @Schema(description = "发送时间(时间戳)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送时间(时间戳)")
    private Long sendTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "实际盈亏额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("实际盈亏额")
    private BigDecimal profitResult;

    @Schema(description = "结算时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结算时间")
    private Long settleTime;

    @Schema(description = "失败原因", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("失败原因")
    private String failReason;

    @Schema(description = "买入价格")
    @ExcelProperty("买入价格")
    private BigDecimal orderPrice;

    @Schema(description = "买入价格")
    @ExcelProperty("结算价格")
    private BigDecimal settlePrice;
}