package com.rf.exchange.module.trade.controller.app.trade.vo;

import com.rf.exchange.module.trade.enums.TimeContractOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-13
 */
@Schema(description = "限时合约订单详情接口 Response VO")
@Data
public class AppTradeTimeContractOrderDetailRespVO {

    @Schema(description = "订单id")
    private Long id;
    @Schema(description = "订单号")
    private String tradeNo;
    @Schema(description = "交易对名称")
    private String name;
    @Schema(description = "交易对代码")
    private String code;
    @Schema(description = "下单价格")
    private String orderPrice;
    @Schema(description = "当前价格", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currentPrice = "0";
    @Schema(description = "结算价格")
    private String settlePrice;
    @Schema(description = "时长")
    private Integer duration;
    @Schema(description = "购买金额")
    private String amount;
    @Schema(description = "预计盈亏 有正负")
    private String profit;
    @Schema(description = "预计盈利金额")
    private String winProfit;
    @Schema(description = "亏损亏损金额")
    private String loseProfit;
    @Schema(description = "交易方向 0做多 1做空")
    private Integer shortLong;
    /**
     * 交易状态
     * {@link TimeContractOrderStatusEnum}
     */
    @Schema(description = "交易状态 0等待开奖 1开奖成功 2开奖失败")
    private Integer tradeStatus;
    @Schema(description = "发送时间(时间戳)")
    private Long sendTime;
    @Schema(description = "结算时间")
    private Long settleTime;
}
