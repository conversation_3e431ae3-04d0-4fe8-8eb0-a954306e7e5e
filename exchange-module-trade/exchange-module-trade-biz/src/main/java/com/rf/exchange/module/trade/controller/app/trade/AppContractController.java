package com.rf.exchange.module.trade.controller.app.trade;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.trade.controller.app.trade.vo.*;
import com.rf.exchange.module.trade.service.contract.ContractOrderService;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * <AUTHOR>
 * @since 2024-08-15
 */
@Tag(name = "用户APP - 合约接口")
@RequestMapping("/trade")
@RestController
public class AppContractController {

    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private ContractPositionService contractPositionService;

    @Operation(summary = "合约的交易对基础信息")
    @GetMapping("/contract/simple-info")
    @PreAuthenticated
    public CommonResult<AppContractSimpleInfoRespVO> simpleInfo(@RequestParam("code") String tradePairCode) {
        return CommonResult.success(contractOrderService.appGetContractSimpleInfo(tradePairCode, getTenantId()));
    }

    @Operation(summary = "合约委托订单记录分页")
    @GetMapping("/order/contract/order-page")
    @PreAuthenticated
    public CommonResult<PageResult<AppContractOrderRespVO>> pageContractOrder(@Valid PageParam reqVO) {
        return success(contractOrderService.appGetContractOrderPage(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约持仓记录分页")
    @GetMapping("/order/contract/position-page")
    @PreAuthenticated
    public CommonResult<PageResult<AppContractPositionRespVO>> pageContractPosition(@Valid AppContractPositionPageReqVO reqVO) {
        return success(contractPositionService.appGetContractPositionPage(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约订单历史记录分页")
    @GetMapping("/order/contract/history-page")
    @PreAuthenticated
    public CommonResult<PageResult<AppContractOrderRespVO>> pageContractOrderHistory(@Valid AppContractOrderHistoryPageReqVO reqVO) {
        return success(contractOrderService.appGetContractOrderHistoryPage(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约买卖-创建委托")
    @PostMapping("/contract/order-create")
    @PreAuthenticated
    public CommonResult<AppContractOrderRespVO> createContractOrder(@Valid @RequestBody AppContractOrderCreateReqVO reqVO) {
        return success(contractOrderService.appCreateContractOrder(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约买卖-取消委托")
    @PostMapping("/contract/order-cancel")
    @PreAuthenticated
    public CommonResult<Long> cancelContractOrder(@Valid @RequestBody AppContractOrderCancelReaVO reqVO) {
        return success(contractOrderService.appCancelContractOrder(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约买卖-设置止盈止损")
    @PostMapping("/contract/close-condition")
    @PreAuthenticated
    public CommonResult<Boolean> positionCloseCondition(@Valid @RequestBody AppContractCloseConditionReqVO reqVO) {
        return success(contractPositionService.appCloseContractPositionWithCondition(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约买卖-平仓")
    @PostMapping("/contract/close")
    @PreAuthenticated
    public CommonResult<Boolean> contractClose(@Valid @RequestBody AppContractCloseReqVO reqVO) {
        return success(contractPositionService.appCloseContractPosition(reqVO, getLoginUser(), getTenantId()));
    }

    @Operation(summary = "合约买卖-批量平仓")
    @PostMapping("/contract/close-batch")
    @PreAuthenticated
    public CommonResult<Boolean> contractCloseBatch(@Valid @RequestBody AppContractBatchCloseReqVO reqVO) {
        return success(contractPositionService.appBatchCloseContractPosition(reqVO, getLoginUser(), getTenantId()));
    }
}
