package com.rf.exchange.module.trade.matchengine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractPositionDO;
import com.rf.exchange.module.trade.dal.redis.ContractLiquidateEngineRedisDAO;
import com.rf.exchange.module.trade.dal.redis.ContractMatchEnginRedisDAO;
import com.rf.exchange.module.trade.enums.ContractMarginTypeEnum;
import com.rf.exchange.module.trade.enums.ContractOrderStatusEnum;
import com.rf.exchange.module.trade.enums.ContractOrderTypeEnum;
import com.rf.exchange.module.trade.enums.ShortLongEnum;
import com.rf.exchange.module.trade.service.contract.ContractOrderService;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 通过RocketMQ的价格消息触发订单匹配引擎计算是否需要成交委托订单
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Slf4j
@Component
public class ContractOrderMatchEngine {

    @Resource
    private ContractOrderService contractOrderService;
    @Resource
    private ContractPositionService positionService;
    @Resource
    private ContractMatchEnginRedisDAO matchEngineRedisDAO;
    @Resource
    private ContractLiquidateEngineRedisDAO liquidateRedisDAO;
    @Resource
    private ContractLiquidateEngine liquidateEngine;
    @Resource
    @Lazy
    private CandleDataApi candleDataApi;

    /**
     * 委托中的订单（未成交订单）
     * 只有未成交的限价合约委托才会在这个map中
     * <p>
     * key：交易对代码
     */
    private static final ConcurrentMap<String, ContractOrderingSafeList> orderingMap = new ConcurrentHashMap<>(100);

    /**
     * 等待结束的订单，其中包括止盈，止损，强平爆仓
     * 所有等待结束的委托订单都会在这个map中
     * <p>
     * key: 交易对代码
     */
    private static final ConcurrentMap<String, ContractOrderingSafeList> waitStopMap = new ConcurrentHashMap<>(100);

    /**
     * 所有委托订单的Map
     * <p>
     * key:委托订单的订单号
     */
    private static final ConcurrentMap<String, ContractOrderDO> orderRecordMap = new ConcurrentHashMap<>(100);

    /**
     * 交易对的价格Map
     * <p>
     * key: 交易对代码
     */
    public static final ConcurrentMap<String, BigDecimal> tmpPriceMap = new ConcurrentHashMap<>(100);

    /**
     * 通过订单编号获取匹配引擎中的委托订单
     *
     * @param orderNo 订单编号
     * @return 委托订单信息
     */
    public ContractOrderDO getOrderByOrderNo(String orderNo) {
        return orderRecordMap.get(orderNo);
    }

    /**
     * 获取当前的交易对的实时价格
     *
     * @param code 系统交易对代码
     * @return 实时价格
     */
    public BigDecimal getPriceByCode(String code) {
        return tmpPriceMap.get(code);
    }

    /**
     * 如果需要重新加载所有未完成的委托订单则加载
     */
    public void loadUnfinishedOrderIfNeed() {
        final Set<String> allUnfinishedOrderNos = matchEngineRedisDAO.getAllOrderNo();
        final Set<String> loadedOrderNos = orderRecordMap.keySet();
        boolean loadAgain = false;
        if (CollectionUtil.isEmpty(allUnfinishedOrderNos) && CollectionUtil.isNotEmpty(loadedOrderNos)) {
            loadAgain = true;
        } else {
            Set<String> difference = new HashSet<>(allUnfinishedOrderNos);
            difference.removeAll(loadedOrderNos);
            if (!difference.isEmpty()) {
                loadAgain = true;
            } else if (loadedOrderNos.size() > allUnfinishedOrderNos.size()) {
                loadAgain = true;
            }
        }
        if (loadAgain) {
            loadUnfinishedContractOrder();
            log.info("再次加载未完成合约订单 redis中订单数:[{}] 已经加载:[{}]", allUnfinishedOrderNos == null ? 0 : allUnfinishedOrderNos.size(), orderRecordMap.size());
        }
    }

    /**
     * 加载所有未完成的合约委托订单
     */
    public void loadUnfinishedContractOrder() {
        final List<ContractOrderDO> allOrders = TenantUtils.executeIgnore(contractOrderService::getAllUnfinishedContractOrders);
        // 先清除一遍所有数据
        orderRecordMap.clear();
        orderingMap.clear();
        waitStopMap.clear();
        matchEngineRedisDAO.resetWithOrderNos(null);

        if (CollUtil.isNotEmpty(allOrders)) {
            final Set<String> positionRecordNos = allOrders.stream().map(ContractOrderDO::getPositionRecordNo).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());

            final List<ContractPositionDO> positions = TenantUtils.executeIgnore(() -> positionService.getUnStoppedPositionsByRecordNos(positionRecordNos));
            final Map<String, ContractPositionDO> positonMap = positions.stream().collect(Collectors.toMap(ContractPositionDO::getRecordNo, contractPositionDO -> contractPositionDO));

            // 如果有需要强平委托订单
            if (CollUtil.isNotEmpty(positions)) {
                liquidateEngine.updateWithAllPositions(positions);
            }

            for (ContractOrderDO order : allOrders) {
                // 如果是强平委托且没有成交
                if (ContractOrderTypeEnum.LIQUIDATE_ORDER.getType().equals(order.getOrderType())) {
                    if (positonMap.get(order.getPositionRecordNo()) != null) {
                        addContractOrderingOrder(order);
                        matchEngineRedisDAO.addOrderNo(order.getOrderNo());
                    } else {
                        // 如果positionMap中没有这个持仓则表示持仓已经结束则需要从redis的未完成委托订单中移除
                        matchEngineRedisDAO.removeOrderNo(order.getOrderNo());
                    }
                } else {
                    addContractOrderingOrder(order);
                    matchEngineRedisDAO.addOrderNo(order.getOrderNo());
                }
            }

            // 先获取一次交易对的价格数据
            final Map<String, CurrentPriceRespDTO> allCurrentPriceList = candleDataApi.getAllCurrentPriceList();
            for (Map.Entry<String, CurrentPriceRespDTO> entry : allCurrentPriceList.entrySet()) {
                final BigDecimal price = entry.getValue().getCurrentPrice();
                if (null != price) {
                    tmpPriceMap.put(entry.getKey(), price);
                } else {
                    log.error("[错误] 没有实时的价格信息 交易对:[{}]", entry.getKey());
                }
            }
            log.info("加载未完成的合约委托订单 DB中订单数量:[{}] 加载总数量:[{}] 委托中:[{}] 等待结束:[{}]", allOrders.size(), orderRecordMap.size(), orderingMap.size(), waitStopMap.size());
        }
    }

    /**
     * 添加委托中的合约订单
     *
     * @param order 委托订单
     */
    public void addContractOrderingOrder(ContractOrderDO order) {
        TenantContextHolder.setIgnore(true);
        if (null == order) {
            return;
        }

        // 如果委托已经完成则返回
        if (ContractOrderStatusEnum.FINISHED.getStatus().equals(order.getOrderStatus())) {
            matchEngineRedisDAO.removeOrderNo(order.getOrderNo());
            return;
        }

        final ContractOrderingSafeList orderingSafeList = getContractOrderingSafeList(order);
        // 如果委托订单已经取消则从SafeList中移除
        if (ContractOrderStatusEnum.CANCELED.getStatus().equals(order.getOrderStatus())) {
            removeContractOrderingOrder(order, orderingSafeList);

        } else {
            if (!ContractOrderTypeEnum.OPEN_ORDER.getType().equals(order.getOrderType()) && StrUtil.isEmpty(order.getPositionRecordNo())) {
                log.error("非开仓委托订单没有持仓记录编号 委托订单id:[{}]", order.getId());
                return;
            }

            if (orderingSafeList.add(order)) {
                log.debug("委托订单列表[{}] 订单添加成功 [{}]", orderingSafeList.getName(), order.getOrderNo());
                orderRecordMap.put(order.getOrderNo(), order);
            } else {
                log.debug("委托订单列表[{}] 订单已存在 [{}]", orderingSafeList.getName(), order.getOrderNo());
            }
        }
    }

    /**
     * 移除委托中的合约订单
     *
     * @param order    委托订单
     * @param safeList 合约订单集合
     */
    public void removeContractOrderingOrder(ContractOrderDO order, @Nullable ContractOrderingSafeList safeList) {
        ContractOrderingSafeList orderingSafeList = safeList == null ? getContractOrderingSafeList(order) : safeList;
        log.debug("委托订单列表[{}] 移除订单[{}]", orderingSafeList.getName(), order.getOrderNo());

        // 从safeList中移除取消的委托订单
        final List<ContractOrderDO> toBeRemoved = orderingSafeList.removeByIds(Collections.singletonList(order.getId()));
        for (ContractOrderDO orderRecord : toBeRemoved) {

            orderRecordMap.remove(orderRecord.getCode());
            // 从redis未完成的委托订单中移除
            matchEngineRedisDAO.removeOrderNo(orderRecord.getOrderNo());

            // 如果委托订单是强平委托则从强平引擎中移除
            if (ContractOrderTypeEnum.LIQUIDATE_ORDER.getType().equals(orderRecord.getOrderType())) {
                liquidateEngine.removeOpenedPosition(order.getUserId(), order.getCode(), order.getPositionRecordNo());
            }
        }
    }

    /**
     * 更新委托中的订单
     *
     * @param order 委托订单信息
     */
    public void updateContractOrderingOrder(ContractOrderDO order) {
        final ContractOrderingSafeList orderingSafeList = getContractOrderingSafeList(order);
        orderingSafeList.updateOrder(order);
    }

    private ContractOrderingSafeList getContractOrderingSafeList(ContractOrderDO order) {
        ConcurrentMap<String, ContractOrderingSafeList> orderMap;
        String listName;
        if (ContractOrderTypeEnum.OPEN_ORDER.getType().equals(order.getOrderType())) {
            orderMap = orderingMap;
            listName = "ordering";
        } else {
            orderMap = waitStopMap;
            listName = "waitStop";
        }
        ContractOrderingSafeList orderingSafeList = orderMap.get(order.getCode());
        if (null == orderingSafeList) {
            orderingSafeList = new ContractOrderingSafeList(listName);
            orderMap.put(order.getCode(), orderingSafeList);
        }
        return orderingSafeList;
    }

    public void updateCandlePrice(String code, BigDecimal price) {
        tmpPriceMap.put(code, price);
    }

    /**
     * 处理价格变化
     *
     * @param code  交易对代码
     * @param price 价格
     */
    @Async("tradeMatchTaskExecutor")
    public void handlePriceChanged(String code, BigDecimal price) {
        TenantContextHolder.setIgnore(true);
        final ContractOrderingSafeList orderingSafeList = orderingMap.get(code);
        final ContractOrderingSafeList waitStopSafeList = waitStopMap.get(code);

        if (orderingSafeList != null && !orderingSafeList.isEmpty()) {
            log.info("未完成的委托订单总数:[{}] 开仓委托订单数:[{}] code:[{}] price:[{}]", orderRecordMap.size(), orderingSafeList.size(), code, price);
            Set<Long> removeOrderId = new HashSet<>(orderingSafeList.size());
            for (ContractOrderDO order : orderingSafeList.getOrderList()) {
                // 如果是空单委托且当前交易对的实时价格大于等于委托价格则立即成交
                if (ShortLongEnum.SHORT.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) >= 0) {
                    final Set<Long> orderIds = contractOrderService.autoCompleteOrderingOrderTransaction(order, price);
                    removeOrderId.addAll(orderIds);

                }
                // 如果是多单委托且当前交易对的实时价格小于等于委托价格则立即成交
                else if (ShortLongEnum.LONG.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) <= 0) {
                    final Set<Long> orderIds = contractOrderService.autoCompleteOrderingOrderTransaction(order, price);
                    removeOrderId.addAll(orderIds);
                }
            }
            // 移除操作成功的委托订单order
            final List<ContractOrderDO> toBeRemoved = orderingSafeList.removeByIds(removeOrderId);
            for (ContractOrderDO order : toBeRemoved) {
                log.info("从引擎中移除已经成交的订单 orderList:[{}] order:[{}]", orderingSafeList.getName(), order);
                orderRecordMap.remove(order.getOrderNo());
                // 从redis中移除已经自动强平的持仓编号
                liquidateRedisDAO.removeUnusefulKeyValue(order.getPositionRecordNo());
            }
        }

        if (waitStopSafeList != null && !waitStopSafeList.isEmpty()) {
            log.info("未完成的委托订单总数:[{}] 平仓委托订单数:[{}] code:[{}] price:[{}]", orderRecordMap.size(), waitStopSafeList.size(), code, price);
            Set<Long> removeOrderIds = new HashSet<>(waitStopSafeList.size());
            for (ContractOrderDO order : waitStopSafeList.getOrderList()) {

                if (ContractOrderTypeEnum.LIQUIDATE_ORDER.getType().equals(order.getOrderType())) {
                    if (ContractMarginTypeEnum.ISOLATED_MARGIN.getType().equals(order.getMarginType())) { // 逐仓模式
                        if (liquidateEngine.checkIfLiquidationPriceTriggered(code, price, order.getPositionRecordNo())) {
                            final Set<Long> orderIds = contractOrderService.autoCompleteLiquidateOrderTransaction(order, price);
                            removeOrderIds.addAll(orderIds);
                        }
                    } else if (ContractMarginTypeEnum.CROSS_MARGIN.getType().equals(order.getMarginType())) { // 全仓模式
                        log.error("暂不支持全仓保证金模式");
                    }
                } else {
                    //boolean isNear = price.subtract(order.getOrderPrice()).abs().compareTo(BigDecimal.valueOf(0.01)) <= 0;

                    // 如果是止盈委托，空单时价格比委托价格低，多单时价格比委托价格高 则符合止盈要求
                    if (ContractOrderTypeEnum.STOP_PROFIT_ORDER.getType().equals(order.getOrderType())) {
                        if ((ShortLongEnum.SHORT.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) <= 0)
                                || (ShortLongEnum.LONG.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) >= 0)) {
                            log.info("触发委托条件 [止盈] 委托单号:[{}] price:[{}]", order.getOrderNo(), price);
                            final Set<Long> orderIds = contractOrderService.autoCompleteStopProfitOrderTransaction(order, price);
                            removeOrderIds.addAll(orderIds);
                        } else {
                            log.debug("委托订单不符合 [止盈] 成交条件 订单:{} 多空:{} 类型:{} 委托价格:{} 交易对:{} 当前价格:{}", order.getOrderNo(), order.getShortLong(), order.getOrderType(), order.getOrderPrice(), order.getCode(), price);
                        }
                    }
                    // 如果是止损委托，空单时价格比委托价格高，多单时价格比委托价格低 则符合止损要求
                    else if (ContractOrderTypeEnum.STOP_LOSS_ORDER.getType().equals(order.getOrderType())) {
                        if ((ShortLongEnum.SHORT.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) >= 0)
                                || (ShortLongEnum.LONG.getType() == order.getShortLong() && price.compareTo(order.getOrderPrice()) <= 0)) {
                            log.info("触发委托条件 [止损] 委托订单号:[{}] price:[{}]", order.getOrderNo(), price);
                            final Set<Long> orderIds = contractOrderService.autoCompleteStopLossOrderTransaction(order, price);
                            removeOrderIds.addAll(orderIds);
                        } else {
                            log.debug("委托订单不符合 [止损] 成交条件 订单:{} 多空:{} 类型:{} 委托价格:{} 交易对:{} 当前价格:{}", order.getOrderNo(), order.getShortLong(), order.getOrderType(), order.getOrderPrice(), order.getCode(), price);
                        }
                    }
                }
            }

            // 移除已经处理成功的委托订单
            final List<ContractOrderDO> toBeRemoved = waitStopSafeList.removeByIds(removeOrderIds);
            for (ContractOrderDO order : toBeRemoved) {
                log.info("从引擎中移除已经成交的订单 orderList:[{}] order:[{}]", waitStopSafeList.getName(), order);
                orderRecordMap.remove(order.getOrderNo());
                liquidateEngine.removeOpenedPosition(order.getUserId(), order.getCode(), order.getPositionRecordNo());
            }
        }
    }
}
