package com.rf.exchange.module.trade.controller.admin.duration.vo;

import com.rf.exchange.framework.common.enums.DateIntervalEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 订单时长配置新增/修改 Request VO")
@Data
public class TradeDurationSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14527")
    private Long id;

    @Schema(description = "时长", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "时长不能为空")
    private Integer duration;

    /**
     * {@link DateIntervalEnum}
     */
    @Schema(description = "单位时间 1天 2周 3月 4季度 5年 6秒 7分 8小时", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位时间不能为空")
    private Integer timeUnit;

    @Schema(description = "最小订单金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最小订单金额不能为空")
    private Integer minAmount;

    @Schema(description = "收益率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收益率不能为空")
    private BigDecimal profitRate;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "开启状态 0开启 1停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "status不能为空")
    private Integer status;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "租户id不能为空")
    private Long tenantId;
}