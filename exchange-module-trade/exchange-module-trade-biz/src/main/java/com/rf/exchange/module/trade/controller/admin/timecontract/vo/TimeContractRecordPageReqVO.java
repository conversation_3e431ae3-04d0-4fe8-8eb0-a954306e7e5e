package com.rf.exchange.module.trade.controller.admin.timecontract.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 限时合约交易记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TimeContractRecordPageReqVO extends PageParam {

    @Schema(description = "交易号")
    private String tradeNo;

    @Schema(description = "用户id", example = "29688")
    private Long userId;

    @Schema(description = "会员账号", example = "张三")
    private String username;

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "时长（单位秒）")
    private Integer duration;

    @Schema(description = "数量")
    private BigDecimal amount;

    @Schema(description = "交易方向 0做多 1做空")
    private Integer shortLong;

    @Schema(description = "盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利", example = "2")
    private Integer profitType;

    @Schema(description = "收益率")
    private BigDecimal profitRate;

    @Schema(description = "交易状态", example = "2")
    private Integer tradeStatus;

    @Schema(description = "发送时间(时间戳)")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] sendTime;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

    @Schema(description = "输赢 false输 true赢")
    private Boolean winOrLose;
}