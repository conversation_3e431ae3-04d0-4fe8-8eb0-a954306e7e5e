package com.rf.exchange.module.trade.controller.admin.timecontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 限时合约交易记录新增/修改 Request VO")
@Data
public class TimeContractRecordSaveReqVO {

    @Schema(description = "记录id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21987")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利不能为空")
    private Integer profitType;

    @Schema(description = "随机赢率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal randomRate;
}