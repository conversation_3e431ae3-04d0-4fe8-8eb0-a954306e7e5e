package com.rf.exchange.module.trade.controller.admin.contract;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractOrderPageReqVO;
import com.rf.exchange.module.trade.controller.admin.contract.vo.ContractOrderRespVO;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import com.rf.exchange.module.trade.service.contract.ContractOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合约委托记录")
@RestController
@RequestMapping("/trade/contract-order-record")
@Validated
public class ContractOrderController extends TenantBaseController {

    @Resource
    private ContractOrderService contractOrderService;

    @PostMapping("/page")
    @Operation(summary = "获得合约委托记录分页")
    @PreAuthorize("@ss.hasPermission('trade:contract-order-record:query')")
    public CommonResult<PageResult<ContractOrderRespVO>> getContractOrderRecordPage(@Valid @RequestBody ContractOrderPageReqVO pageReqVO) {
        setupTenantId();
        PageResult<ContractOrderDO> pageResult = contractOrderService.getContractOrderRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractOrderRespVO.class));
    }
}