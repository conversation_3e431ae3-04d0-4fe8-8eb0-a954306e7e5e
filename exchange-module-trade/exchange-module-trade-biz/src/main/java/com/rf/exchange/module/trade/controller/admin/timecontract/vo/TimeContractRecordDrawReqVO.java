package com.rf.exchange.module.trade.controller.admin.timecontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024-07-17
 */
@Schema(description = "管理后台 - 限时合约交易手动结算 Request VO")
@Data
public class TimeContractRecordDrawReqVO {

    @Schema(description = "记录id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "输还是赢 true:赢 false:输", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "输赢值不能为空")
    private Boolean winOrLose;
}
