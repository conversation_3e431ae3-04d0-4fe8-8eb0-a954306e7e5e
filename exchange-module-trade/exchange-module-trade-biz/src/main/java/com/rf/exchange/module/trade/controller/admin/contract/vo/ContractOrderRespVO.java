package com.rf.exchange.module.trade.controller.admin.contract.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 合约委托记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractOrderRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21452")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "仓位订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("仓位订单号")
    private String positionRecordNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26477")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String code;

    @Schema(description = "价格类型 0:市价订单 1:限价订单", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("价格类型 0:市价订单 1:限价订单")
    private Integer priceType;

    @Schema(description = "多空类型 0:做空 1:做多", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("多空类型 0:做空 1:做多")
    private Integer shortLong;

    @Schema(description = "委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托")
    private Integer orderType;

    @Schema(description = "订单状态 0:委托中 1:已成交", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("订单状态 0:委托中 1:已成交")
    private Integer orderStatus;

    @Schema(description = "委托金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("委托金额")
    private BigDecimal orderAmount;

    @Schema(description = "委托金额法币币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("委托金额法币币种")
    private String orderAmountCurrency;

    @Schema(description = "委托币数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("委托币数量")
    private BigDecimal orderVolume;

    @Schema(description = "委托价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "29506")
    @ExcelProperty("委托价格")
    private BigDecimal orderPrice;

    @Schema(description = "成交价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "15906")
    @ExcelProperty("成交价格")
    private BigDecimal transactionPrice;

    @Schema(description = "手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手续费")
    private BigDecimal fee;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}