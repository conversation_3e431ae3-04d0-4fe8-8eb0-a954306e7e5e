package com.rf.exchange.module.trade.controller.app.tradetotal;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import com.rf.exchange.module.trade.controller.app.tradetotal.vo.AppContractSummaryRespVO;
import com.rf.exchange.module.trade.controller.app.tradetotal.vo.AppTradeTotalRespVO;
import com.rf.exchange.module.trade.dal.dataobject.timecontract.TimeContractRecordDO;
import com.rf.exchange.module.trade.service.contract.ContractPositionService;
import com.rf.exchange.module.trade.service.timecontract.TimeContractRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - 交易统计")
@RestController
@RequestMapping("/trade/total")
public class AppTradeTotalController {
    @Resource
    private TimeContractRecordService timeContractRecordService;
    @Resource
    private ContractPositionService contractPositionService;

    @Operation(summary = "获取今日收益单量")
    @GetMapping("get-today")
    @PreAuthenticated
    public CommonResult<AppTradeTotalRespVO> getToday() {
        long startTime = DateUtils.getTodayStart();
        List<TimeContractRecordDO> list = timeContractRecordService.getListByRange(getLoginUserId(), startTime, DateUtils.getUnixTimestampNow());
        BigDecimal profit = list.parallelStream().map(TimeContractRecordDO::getProfitResult).reduce(BigDecimal.ZERO, BigDecimal::add);
        return CommonResult.success(AppTradeTotalRespVO.builder().profit(profit).tradeCount(list.size()).type(TradeTypeEnum.PERIOD.getType()).build());
    }

    @Operation(summary = "合约总览")
    @GetMapping("contract")
    @PreAuthenticated
    public CommonResult<AppContractSummaryRespVO> getContractSummary() {
        return CommonResult.success(contractPositionService.appGetContractSummary(getLoginUser()));
    }
}
