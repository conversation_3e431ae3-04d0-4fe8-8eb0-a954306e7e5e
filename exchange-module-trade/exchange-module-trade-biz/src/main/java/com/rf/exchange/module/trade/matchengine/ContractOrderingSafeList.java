package com.rf.exchange.module.trade.matchengine;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.module.trade.dal.dataobject.contract.ContractOrderDO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 线程安全的合约委托委托中列表
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
public class ContractOrderingSafeList {

    private final List<ContractOrderDO> orderList;

    private final ConcurrentSkipListSet<Long> orderUniqueIds = new ConcurrentSkipListSet<>();

    private final ReentrantLock lock = new ReentrantLock();

    @Getter
    private final String name;

    public ContractOrderingSafeList(String name) {
        this(100, name);
    }

    public ContractOrderingSafeList(int capacity, String name) {
        orderList = new ArrayList<>(capacity);
        this.name = name;
    }

    /**
     * 添加委托合约订单
     *
     * @param order 委托订单
     */
    public boolean add(ContractOrderDO order) {
        if (null == order) {
            return false;
        }
        lock.lock();
        try {
            if (!orderUniqueIds.contains(order.getId())) {
                orderUniqueIds.add(order.getId());
                orderList.add(order);
                // 按照委托价格升序排列
                orderList.sort(Comparator.comparing(ContractOrderDO::getOrderPrice));
                return true;
            }
            return false;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 更新委托订单
     *
     * @param updateOrder 委托订单
     */
    public void updateOrder(ContractOrderDO updateOrder) {
        lock.lock();
        try {
            for (ContractOrderDO order : orderList) {
                if (order.getId().equals(updateOrder.getId())) {
                    order.setOrderPrice(updateOrder.getOrderPrice());
                    order.setOrderAmount(updateOrder.getOrderAmount());
                    log.info("更新委托订单信息 订单:[{}] 原始order:[{}] 更新order:[{}]", order, order.getOrderPrice(), updateOrder.getOrderPrice());
                }
            }
        } finally {
            lock.unlock();
        }
    }

    public ContractOrderDO get(int index) {
        lock.lock();
        try {
            if (CollUtil.isEmpty(orderList) || index >= orderList.size()) {
                return null;
            }
            return orderList.get(index);
        } finally {
            lock.unlock();
        }
    }

    public void removeAt(int index) {
        lock.lock();
        try {
            if (CollUtil.isEmpty(orderList) || index >= orderList.size()) {
                return;
            }
            orderList.remove(index);
        } finally {
            lock.unlock();
        }
    }

    public List<ContractOrderDO> removeByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        lock.lock();
        try {
            if (CollUtil.isEmpty(orderList)) {
                return Collections.emptyList();
            }
            final List<ContractOrderDO> toBeRemoved = orderList.stream().filter(order -> ids.contains(order.getId())).collect(Collectors.toList());
            final Set<Long> toBeRemovedIds = toBeRemoved.stream().map(ContractOrderDO::getId).collect(Collectors.toSet());
            orderList.removeAll(toBeRemoved);
            orderUniqueIds.removeAll(toBeRemovedIds);
            return toBeRemoved;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取所有委托价格大于价格的委托订单
     *
     * @param price 价格
     * @return 大于price的所有委托订单列表
     */
    public List<ContractOrderDO> getGreaterThan(BigDecimal price) {
        if (null == price || price.compareTo(BigDecimal.ZERO) <= 0) {
            return Collections.emptyList();
        }
        lock.lock();
        try {
            if (CollUtil.isEmpty(orderList)) {
                return Collections.emptyList();
            }

            int separateIndex = -1;
            for (int i = 0; i < orderList.size(); i++) {
                ContractOrderDO order = orderList.get(i);
                if (order.getOrderPrice().compareTo(price) >= 0) {
                    separateIndex = i;
                    break;
                }
            }

            if (separateIndex == -1) {
                return Collections.emptyList();
            } else if (separateIndex == 0) {
                return orderList;
            }

            return orderList.subList(separateIndex, orderList.size());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取所有委托价格小于价格的委托订单
     *
     * @param price 价格
     * @return 小于price的所有委托订单列表
     */
    public List<ContractOrderDO> getLessThan(BigDecimal price) {
        if (null == price || price.compareTo(BigDecimal.ZERO) <= 0) {
            return Collections.emptyList();
        }
        lock.lock();
        try {
            if (CollUtil.isEmpty(orderList)) {
                return Collections.emptyList();
            }

            int separateIndex = -1;
            for (int i = orderList.size() - 1; i >= 0; i--) {
                ContractOrderDO order = orderList.get(i);
                if (order.getOrderPrice().compareTo(price) <= 0) {
                    separateIndex = i;
                    break;
                }
            }

            if (separateIndex == -1) {
                return Collections.emptyList();
            } else if (separateIndex == 0) {
                return orderList;
            }

            return orderList.subList(0, separateIndex + 1);

        } finally {
            lock.unlock();
        }
    }

    public List<ContractOrderDO> getOrderList() {
        if (CollUtil.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        lock.lock();
        try {
            return new ArrayList<>(orderList);
        } finally {
            lock.unlock();
        }
    }

    public boolean isEmpty() {
        lock.lock();
        try {
            return CollUtil.isEmpty(orderList);
        } finally {
            lock.unlock();
        }
    }

    public int size() {
        lock.lock();
        try {
            return orderList.size();
        } finally {
            lock.unlock();
        }
    }
}
