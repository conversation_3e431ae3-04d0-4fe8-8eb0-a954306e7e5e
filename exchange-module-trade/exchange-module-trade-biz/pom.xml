<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-module-trade</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>exchange-module-trade-biz</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-trade-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-exc-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-candle-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- rocketMq -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <!-- websocket -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>
</project>