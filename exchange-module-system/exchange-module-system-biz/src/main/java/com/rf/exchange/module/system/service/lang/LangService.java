package com.rf.exchange.module.system.service.lang;

import java.util.*;

import jakarta.validation.*;
import com.rf.exchange.module.system.controller.admin.lang.vo.*;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import com.rf.exchange.framework.common.pojo.PageResult;

/**
 * 系统语种配置 Service 接口
 *
 * <AUTHOR>
 */
public interface LangService {

    /**
     * 创建系统语种配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLang(@Valid LangSaveReqVO createReqVO);

    /**
     * 更新系统语种配置
     *
     * @param updateReqVO 更新信息
     */
    void updateLang(@Valid LangSaveReqVO updateReqVO);

    /**
     * 删除系统语种配置
     *
     * @param id 编号
     */
    void deleteLang(Long id);

    /**
     * 获得系统语种配置
     *
     * @param id 编号
     * @return 系统语种配置
     */
    LangDO getLang(Long id);

    /**
     * 获得系统语种配置分页
     *
     * @param pageReqVO 分页查询
     * @return 系统语种配置分页
     */
    PageResult<LangDO> getLangPage(LangPageReqVO pageReqVO);

    /**
     * 获取语种列表
     * @return
     */
    Set<String> getLangCodeList();

    /**
     * 获取语言列表
     * @return 语言列表
     */
    List<LangDO> getLangList();
}