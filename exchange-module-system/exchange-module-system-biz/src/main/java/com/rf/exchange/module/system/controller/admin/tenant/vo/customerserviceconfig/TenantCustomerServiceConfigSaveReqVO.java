package com.rf.exchange.module.system.controller.admin.tenant.vo.customerserviceconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 修改通知配置")
@Data
public class TenantCustomerServiceConfigSaveReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long id;
    @Schema(description = "图标路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://1000logos.net/wp-content/uploads/2021/04/WhatsApp-logo.png")
    @NotNull(message = "标签不能为空")
    private String ico;
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "whatsapp")
    @NotNull(message = "名称不能为空")
    private String name;
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Short status;
    @Schema(description = "客服地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://wa.me/?phone=1598556466")
    private String serviceUrl;
    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "租户id不能为空")
    private Long tenantId;
}
