package com.rf.exchange.module.system.controller.admin.agent.vo;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 代理更新状态 Request VO")
@Data
public class AgentUpdateStatusReqVO {

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "代理id不能为空")
    private Long id;

    @Schema(description = "状态，见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

}
