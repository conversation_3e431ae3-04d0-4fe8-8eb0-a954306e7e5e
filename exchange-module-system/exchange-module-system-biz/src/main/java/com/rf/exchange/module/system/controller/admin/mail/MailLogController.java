package com.rf.exchange.module.system.controller.admin.mail;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.mail.vo.log.MailLogPageReqVO;
import com.rf.exchange.module.system.controller.admin.mail.vo.log.MailLogRespVO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailLogDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailLogDetailDO;
import com.rf.exchange.module.system.service.mail.MailLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 邮件日志")
@RestController
@RequestMapping("/system/mail-log")
public class MailLogController {

    @Resource
    private MailLogService mailLogService;

    @GetMapping("/page")
    @Operation(summary = "获得邮箱日志分页")
    @PreAuthorize("@ss.hasPermission('system:mail-log:query')")
    public CommonResult<PageResult<MailLogRespVO>> getMailLogPage(@Valid MailLogPageReqVO pageVO) {
        PageResult<MailLogDO> pageResult = mailLogService.getMailLogPage(pageVO);
        return success(BeanUtils.toBean(pageResult, MailLogRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得邮箱日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:mail-log:query')")
    public CommonResult<MailLogRespVO> getMailTemplate(@RequestParam("id") Long id) {
        MailLogDO log = mailLogService.getMailLog(id);
        final MailLogRespVO resp = BeanUtils.toBean(log, MailLogRespVO.class);
        final List<MailLogDetailDO> detailList = mailLogService.getMailDetail(log.getId());
        final List<MailLogRespVO.LogDetail> logDetailList = BeanUtils.toBean(detailList, MailLogRespVO.LogDetail.class);
        resp.setDetailList(logDetailList);
        return success(resp);
    }
}
