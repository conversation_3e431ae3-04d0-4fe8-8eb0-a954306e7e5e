package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNamePageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNameRespVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNameSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import com.rf.exchange.module.system.service.tenant.TenantServerNameService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 系统租户域名")
@RestController
@RequestMapping("/system/tenant-server-name")
@Validated
public class TenantServerNameController {

    @Resource
    private TenantServerNameService tenantServerNameService;

    @PostMapping("/create")
    @Operation(summary = "创建系统租户域名")
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:create')")
    public CommonResult<Long> createTenantServerName(@Valid @RequestBody TenantServerNameSaveReqVO createReqVO) {
        return success(tenantServerNameService.createTenantServerName(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统租户域名")
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:update')")
    public CommonResult<Boolean> updateTenantServerName(@Valid @RequestBody TenantServerNameSaveReqVO updateReqVO) {
        tenantServerNameService.updateTenantServerName(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统租户域名")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:delete')")
    public CommonResult<Boolean> deleteTenantServerName(@RequestParam("id") Long id) {
        tenantServerNameService.deleteTenantServerName(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统租户域名")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:query')")
    public CommonResult<TenantServerNameRespVO> getTenantServerName(@RequestParam("id") Long id) {
        TenantServerNameDO tenantServerName = tenantServerNameService.getTenantServerName(id);
        return success(BeanUtils.toBean(tenantServerName, TenantServerNameRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得系统租户域名分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:query')")
    public CommonResult<PageResult<TenantServerNameRespVO>> getTenantServerNamePage(@Valid @RequestBody TenantServerNamePageReqVO pageReqVO) {
        PageResult<TenantServerNameDO> pageResult = tenantServerNameService.getTenantServerNamePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TenantServerNameRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统租户域名 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant-server-name:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantServerNameExcel(@Valid TenantServerNamePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantServerNameDO> list = tenantServerNameService.getTenantServerNamePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "系统租户域名.xls", "数据", TenantServerNameRespVO.class,
                        BeanUtils.toBean(list, TenantServerNameRespVO.class));
    }

}