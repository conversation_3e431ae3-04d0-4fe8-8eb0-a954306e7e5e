package com.rf.exchange.module.system.service.lang;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rf.exchange.module.system.controller.admin.lang.vo.*;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.system.dal.mysql.lang.LangMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 系统语种配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LangServiceImpl implements LangService {

    @Resource
    private LangMapper langMapper;

    @Override
    public Long createLang(LangSaveReqVO createReqVO) {
        // 插入
        LangDO lang = BeanUtils.toBean(createReqVO, LangDO.class);
        langMapper.insert(lang);
        // 返回
        return lang.getId();
    }

    @Override
    public void updateLang(LangSaveReqVO updateReqVO) {
        // 更新
        LangDO updateObj = BeanUtils.toBean(updateReqVO, LangDO.class);
        langMapper.updateById(updateObj);
    }

    @Override
    public void deleteLang(Long id) {
        // 校验存在
        // 删除
        langMapper.deleteById(id);
    }


    @Override
    public LangDO getLang(Long id) {
        return langMapper.selectById(id);
    }

    @Override
    public PageResult<LangDO> getLangPage(LangPageReqVO pageReqVO) {
        return langMapper.selectPage(pageReqVO);
    }

    @Override
    public Set<String> getLangCodeList() {
        return langMapper.getLangCodeList();
    }

    @Override
    public List<LangDO> getLangList() {
        LambdaQueryWrapperX<LangDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(LangDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        wrapper.orderByAsc(LangDO::getSort);
        return langMapper.selectList(wrapper);
    }
}