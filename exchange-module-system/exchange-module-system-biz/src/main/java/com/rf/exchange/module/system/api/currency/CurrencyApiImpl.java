package com.rf.exchange.module.system.api.currency;

import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import com.rf.exchange.module.system.service.currency.CurrencyService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CurrencyApiImpl implements CurrencyApi {

    @Resource
    private CurrencyService currencyService;

    @Override
    public CurrencyBaseRespDTO getCurrency(Long id) {
        CurrencyDO currencyDO= currencyService.getCurrency(id);
        return BeanUtils.toBean(currencyDO, CurrencyBaseRespDTO.class);
    }

    @Override
    public CurrencyBaseRespDTO getCurrency(String code) {
        CurrencyDO currencyDO= currencyService.getCurrencyByCode(code);
        return BeanUtils.toBean(currencyDO, CurrencyBaseRespDTO.class);
    }

    @Override
    public List<CurrencyBaseRespDTO> getCurrencyList() {
        List<CurrencyDO> currencyList = currencyService.getCurrencyList();
        return BeanUtils.toBean(currencyList, CurrencyBaseRespDTO.class);
    }
}
