package com.rf.exchange.module.system.controller.admin.tenant.vo.data;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 租户字典数据新增/修改 Request VO")
@Data
@Builder
public class TenantDictDataSaveBatchReqVO {

    @Schema(description = "标签名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "标签名不能为空")
    private String label;

    @Schema(description = "标签值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "标签值不能为空")
    private String value;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "类型不能为空")
    private String dictType;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Short sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Short status;

    @Schema(description = "颜色类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String colorType;

    @Schema(description = "样式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cssClass;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    private String remark;
}