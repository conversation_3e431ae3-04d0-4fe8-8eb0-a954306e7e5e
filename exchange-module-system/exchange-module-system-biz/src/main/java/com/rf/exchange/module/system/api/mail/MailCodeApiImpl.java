package com.rf.exchange.module.system.api.mail;

import com.rf.exchange.module.system.api.mail.dto.MailCodeSendReqDTO;
import com.rf.exchange.module.system.api.mail.dto.MailCodeUseReqDTO;
import com.rf.exchange.module.system.service.mail.MailCodeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
@Validated
public class MailCodeApiImpl implements Mail<PERSON>odeA<PERSON> {

    @Resource
    private MailCodeService mailCodeService;

    @Override
    public void sendMailCode(MailCodeSendReqDTO reqDTO) {
        mailCodeService.sendMailCode(reqDTO);
    }

    @Override
    public void validateMailCode(MailCodeUseReqDTO reqDTO) {
        mailCodeService.validateMailCode(reqDTO);
    }

    @Override
    public void useMailCode(MailCodeUseReqDTO reqDTO) {
        mailCodeService.useMailCode(reqDTO);
    }
}
