package com.rf.exchange.module.system.controller.admin.agent.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rf.exchange.framework.excel.core.annotations.DictFormat;
import com.rf.exchange.framework.excel.core.convert.DictConvert;
import com.rf.exchange.module.system.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 代理 Response VO")
@Data
public class AgentRespVO {

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("代理编号")
    private Long id;

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("代理名称")
    private String name;

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("代理邀请码")
    private String code;

    @Schema(description = "上级id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("代理邀请码")
    private Long ancestorId;

    @Schema(description = "上级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String ancestorName;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "登陆用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String userName;

    @Schema(description = "代理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;

    @Schema(description = "分享地址列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> shareUrls;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;
}
