package com.rf.exchange.module.system.service.agent;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentPageReqVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentRespVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticPageReqVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticRespVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentStatisticDO;

import java.math.BigDecimal;

/**
 * 代理统计 Service 接口
 *
 * <AUTHOR>
 * @since 2024-06-16
 */
public interface AgentStatisticService {
    // TODO: 代理统计
    void incRegister(Long agentId);

    void incRecharge(Long agentId, boolean incCount,BigDecimal amount);

    void incWithdraw(Long agentId,boolean incCount,BigDecimal amount);

    void setTotalBalance(Long agentId,BigDecimal totalBalance);

    /**
     * 获取代理分页列表
     *
     * @param reqVO 分页条件
     * @return 代理分页列表
     */
    PageResult<AgentStatisticDO> getAgentPage(AgentStatisticPageReqVO reqVO);

}
