package com.rf.exchange.module.system.dal.dataobject.tenantdict;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 租户字典数据 DO
 *
 * <AUTHOR>
 */
@TableName("system_tenant_dict_data")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantDictDataDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 标签名
     */
    private String label;
    /**
     * 标签值
     */
    private String value;
    /**
     * 类型
     */
    private String dictType;
    /**
     * 排序
     */
    private Short sort;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 颜色类型
     */
    private String colorType;
    /**
     * 样式
     */
    private String cssClass;
    /**
     * 备注
     */
    private String remark;

}