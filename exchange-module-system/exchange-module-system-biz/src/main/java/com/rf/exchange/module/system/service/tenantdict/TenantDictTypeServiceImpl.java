package com.rf.exchange.module.system.service.tenantdict;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.system.api.permission.RoleApi;
import com.rf.exchange.module.system.api.tenant.TenantApi;
import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.api.tenant.TenantApi;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypePageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypeSaveBatchReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypeSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictTypeDO;
import com.rf.exchange.module.system.dal.mysql.tenantdict.TenantDictDataMapper;
import com.rf.exchange.module.system.dal.mysql.tenantdict.TenantDictTypeMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户配置分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantDictTypeServiceImpl implements TenantDictTypeService {

    @Resource
    private TenantDictTypeMapper tenantDictTypeMapper;
    @Resource
    private TenantDictDataMapper tenantDictDataMapper;
    @Resource
    @Lazy
    private RoleApi roleApi;
    @Resource
    @Lazy
    private TenantApi tenantApi;

    @Override
    @DSTransactional
    @Master
    public Long createTenantDictType(TenantDictTypeSaveReqVO createReqVO) {
        LambdaQueryWrapperX<TenantDictTypeDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TenantDictTypeDO::getTenantId, createReqVO.getTenantId()).eq(TenantDictTypeDO::getType, createReqVO.getType());
        long count = tenantDictTypeMapper.selectCount(queryWrapperX);
        if (count > 0) {
            throw exception(DICT_TYPE_NAME_DUPLICATE);
        }
        // 插入
        TenantDictTypeDO tenantDictType = BeanUtils.toBean(createReqVO, TenantDictTypeDO.class);
        tenantDictTypeMapper.insert(tenantDictType);
        //如果是租户1新增，其它租户也要增加配置
        if (createReqVO.getTenantId() == 1) {
            //新增时，同时插入其它租户
            TenantUtils.executeIgnore(() -> {
                queryWrapperX.clear();
                queryWrapperX.eq(TenantDictTypeDO::getType, createReqVO.getType());
                List<TenantDictTypeDO> allTenantTypeList = tenantDictTypeMapper.selectList(queryWrapperX);
                List<Long> hasTypeTenantIdList = allTenantTypeList.parallelStream().map(TenantDictTypeDO::getTenantId).toList();
                List<Long> tenantIdList = tenantApi.getTenantIdList();
                // 找出所有租户中没有新增的类型的租户
                List<Long> result = tenantIdList.stream()
                        .filter(num -> !hasTypeTenantIdList.contains(num))
                        .collect(Collectors.toList());
                result.forEach(t -> {
                    tenantDictType.setId(null);
                    tenantDictType.setTenantId(t);
                    tenantDictTypeMapper.insert(tenantDictType);
                });
            });
        }
        // 返回
        return tenantDictType.getId();
    }

    @Override
    public void batchCreateTenantDictType(TenantDictTypeSaveBatchReqVO reqVO, LoginUser loginUser) {
        final boolean isSuperAdmin = roleApi.hasAnySuperAdminOf(loginUser.getRoleIds());
        if (!isSuperAdmin) {
            throw exception(FORBIDDEN);
        }
        final List<TenantRespDTO> tenantList = tenantApi.getTenantList();
        if (CollectionUtil.isNotEmpty(tenantList)) {
            List<TenantDictTypeDO> typeDOList = new ArrayList<>();
            for (TenantRespDTO tenantRespDTO : tenantList) {
                final TenantDictTypeDO typeDO = new TenantDictTypeDO();
                typeDO.setName(reqVO.getName());
                typeDO.setType(reqVO.getType());
                typeDO.setStatus(reqVO.getStatus());
                typeDO.setRemark(reqVO.getRemark());
                typeDO.setTenantId(tenantRespDTO.getId());
                typeDOList.add(typeDO);
            }
            tenantDictTypeMapper.insertBatch(typeDOList);
        }
    }

    @Override
    @DSTransactional
    @Master
    public void updateTenantDictType(TenantDictTypeSaveReqVO updateReqVO) {
        LambdaQueryWrapperX<TenantDictTypeDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TenantDictTypeDO::getTenantId, updateReqVO.getTenantId()).eq(TenantDictTypeDO::getType, updateReqVO.getType()).ne(TenantDictTypeDO::getId, updateReqVO.getId());
        long count = tenantDictTypeMapper.selectCount(queryWrapperX);
        if (count > 0) {
            throw exception(DICT_TYPE_NAME_DUPLICATE);
        }

        // 校验存在
        TenantDictTypeDO entity = validateTenantDictTypeExists(updateReqVO.getId());
        // 更新
        TenantDictTypeDO updateObj = BeanUtils.toBean(updateReqVO, TenantDictTypeDO.class);

//        //如果是租户1修改的，其它租户也要修改配置
//        if (entity.getTenantId() == 1) {
//            TenantUtils.executeIgnore(() -> {
//                queryWrapperX.clear();
//                queryWrapperX.eq(TenantDictTypeDO::getType, entity.getType());
//                List<TenantDictTypeDO> allTenantTypeList = tenantDictTypeMapper.selectList(queryWrapperX);
//                allTenantTypeList.forEach(t -> {
//                    updateObj.setId(t.getId());
//                    tenantDictTypeMapper.updateById(updateObj);
//                });
//            });
//        }
        tenantDictTypeMapper.updateById(updateObj);
    }

    @Override
    @DSTransactional
    @Master
    public void deleteTenantDictType(Long id) {
        TenantDictTypeDO entity = validateTenantDictTypeExists(id);
        //如果是租户1删除的，其它租户也要删除
        if (entity.getTenantId() == 1) {
            TenantUtils.executeIgnore(() -> {
                LambdaQueryWrapperX<TenantDictTypeDO> queryWrapperX = new LambdaQueryWrapperX<>();
                queryWrapperX.eq(TenantDictTypeDO::getType, entity.getType());
                tenantDictTypeMapper.delete(queryWrapperX);
            });
        }
        // 删除
        tenantDictTypeMapper.deleteById(id);
    }

    private TenantDictTypeDO validateTenantDictTypeExists(Long id) {
        TenantDictTypeDO entity = tenantDictTypeMapper.selectById(id);
        if (entity == null) {
            throw exception(DICT_TYPE_NOT_EXISTS);
        }
        return entity;
    }

    @Override
    public TenantDictTypeDO getTenantDictType(Long id) {
        return tenantDictTypeMapper.selectById(id);
    }

    @Override
    public PageResult<TenantDictTypeDO> getTenantDictTypePage(TenantDictTypePageReqVO pageReqVO) {
        return tenantDictTypeMapper.selectPage(pageReqVO);
    }

    @Override
    public void copyTenantDictTypeAndData(Long tenantId) {
        List<TenantDictTypeDO> typeList = tenantDictTypeMapper.selectList(new LambdaQueryWrapperX<TenantDictTypeDO>().eq(TenantDictTypeDO::getTenantId, 1));
        List<TenantDictDataDO> dataList = tenantDictDataMapper.selectList(new LambdaQueryWrapperX<TenantDictDataDO>().eq(TenantDictDataDO::getTenantId, 1));
        // 批量设置name属性值
        typeList.forEach(user -> {
            user.setTenantId(tenantId);
            user.setId(null);
        });
        dataList.forEach(user -> {
            user.setTenantId(tenantId);
            user.setId(null);
        });
        tenantDictTypeMapper.insertBatch(typeList);
        tenantDictDataMapper.insertBatch(dataList);
    }

    @Override
    public List<TenantDictTypeDO> getDictTypeList() {
        return tenantDictTypeMapper.selectList();
    }

}