package com.rf.exchange.module.system.dal.mysql.tenantdict;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypePageReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 租户配置分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantDictTypeMapper extends BaseMapperX<TenantDictTypeDO> {

    default PageResult<TenantDictTypeDO> selectPage(TenantDictTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantDictTypeDO>()
                .likeIfPresent(TenantDictTypeDO::getName, reqVO.getName())
                .eqIfPresent(TenantDictTypeDO::getType, reqVO.getType())
                .eqIfPresent(TenantDictTypeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TenantDictTypeDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(TenantDictTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TenantDictTypeDO::getId));
    }

}