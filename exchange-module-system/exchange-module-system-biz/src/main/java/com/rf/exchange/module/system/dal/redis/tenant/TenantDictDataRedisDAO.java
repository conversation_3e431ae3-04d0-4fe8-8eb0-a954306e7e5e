package com.rf.exchange.module.system.dal.redis.tenant;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.rf.exchange.module.system.dal.redis.RedisKeyConstants.PRIVATE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @since 2024-11-10
 */
@Repository
public class TenantDictDataRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void saveDataList(long tenantId, String dictType, List<TenantDictDataDO> dataDOList) {
        if (CollUtil.isEmpty(dataDOList)) {
            return;
        }
        String redisKey = formatKey(tenantId, dictType);
        final String json = JsonUtils.toJsonString(dataDOList);
        stringRedisTemplate.opsForValue().set(redisKey, json);
        stringRedisTemplate.expire(redisKey, 10, TimeUnit.MINUTES);
    }

    public List<TenantDictDataDO> getDataList(long tenantId, String dictType) {
        final String json = stringRedisTemplate.opsForValue().get(formatKey(tenantId, dictType));
        return JsonUtils.parseArray(json, TenantDictDataDO.class);
    }

    private String formatKey(long tenantId, String dictType) {
        return PRIVATE_KEY_PREFIX + "tenant_dict:" + tenantId + "_" + dictType;
    }
}
