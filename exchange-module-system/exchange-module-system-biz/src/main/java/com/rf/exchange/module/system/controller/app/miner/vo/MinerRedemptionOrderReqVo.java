package com.rf.exchange.module.system.controller.app.miner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 挖矿产品创建-编辑参数")
@Data
public class MinerRedemptionOrderReqVo {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

}
