package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 协议多语言 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "管理后台 - 协议多语言 Response VO")
@Data
public class AgreementMultiLangRespVO {

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long agreementId;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long tenantId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "协议类型名称", example = "隐私协议")
    private String typeName;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "状态：0-禁用 1-启用", example = "1")
    private Integer status;

    @Schema(description = "状态名称", example = "启用")
    private String statusName;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "备注", example = "多语言协议")
    private String remark;

    @Schema(description = "多语言内容列表")
    private List<AgreementContentRespVO> contents;

    @Schema(description = "支持的语言数量", example = "3")
    private Integer languageCount;

    @Schema(description = "支持的语言代码列表", example = "[\"zh-CN\", \"en\", \"ja\"]")
    private List<String> languageCodes;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private String updater;

    // ========== 便利方法 ==========

    /**
     * 设置协议类型名称
     */
    public void setTypeName() {
        this.typeName = switch (this.type) {
            case 1 -> "隐私协议";
            case 2 -> "用户准则";
            case 3 -> "服务条款";
            case 4 -> "免责声明";
            case 5 -> "关于我们";
            default -> "未知类型";
        };
    }

    /**
     * 设置状态名称
     */
    public void setStatusName() {
        this.statusName = switch (this.status) {
            case 0 -> "禁用";
            case 1 -> "启用";
            default -> "未知状态";
        };
    }

    /**
     * 设置语言统计信息
     */
    public void setLanguageStatistics() {
        if (contents != null) {
            this.languageCount = contents.size();
            this.languageCodes = contents.stream()
                    .map(AgreementContentRespVO::getLanguageCode)
                    .distinct()
                    .sorted()
                    .toList();
        } else {
            this.languageCount = 0;
            this.languageCodes = List.of();
        }
    }

    /**
     * 根据语言代码获取内容
     */
    public AgreementContentRespVO getContentByLanguage(String languageCode) {
        if (contents == null) {
            return null;
        }
        return contents.stream()
                .filter(content -> languageCode.equals(content.getLanguageCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 是否包含指定语言
     */
    public boolean hasLanguage(String languageCode) {
        return getContentByLanguage(languageCode) != null;
    }

    /**
     * 获取默认语言内容（优先中文，其次英文）
     */
    public AgreementContentRespVO getDefaultContent() {
        if (contents == null || contents.isEmpty()) {
            return null;
        }
        
        // 优先返回中文
        AgreementContentRespVO zhContent = getContentByLanguage("zh-CN");
        if (zhContent != null) {
            return zhContent;
        }
        
        // 其次返回英文
        AgreementContentRespVO enContent = getContentByLanguage("en");
        if (enContent != null) {
            return enContent;
        }
        
        // 最后返回第一个
        return contents.get(0);
    }
}
