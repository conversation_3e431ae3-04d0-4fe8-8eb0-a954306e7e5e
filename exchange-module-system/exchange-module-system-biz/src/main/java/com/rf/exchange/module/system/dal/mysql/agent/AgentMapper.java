package com.rf.exchange.module.system.dal.mysql.agent;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会员代理 Mapper
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Mapper
public interface AgentMapper extends BaseMapperX<AgentDO> {

    default PageResult<AgentDO> selectPage(AgentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgentDO>()
                .likeIfPresent(AgentDO::getCode, reqVO.getCode())
                .likeIfPresent(AgentDO::getName, reqVO.getName())
                .eqIfPresent(AgentDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AgentDO::getId, reqVO.getId())
                .orderByDesc(AgentDO::getId));
    }

    default AgentDO selectByCode(String code) {
        return selectOne(AgentDO::getCode, code);
    }

    default AgentDO getDefault(Long getTenantId) {
        return selectOne(new LambdaQueryWrapperX<AgentDO>()
                .eq(AgentDO::getFirst, true)
                .eqIfPresent(AgentDO::getTenantId, getTenantId)
                .last("limit 1"));
    }

    @Select("select count(1) from system_agent where code=#{code} and id!=#{id}")
    Integer checkCodeExist(@Param("code") String code, @Param("id") Long id);

    @Select("select id from system_agent where user_id=#{userId}")
    Long selectIdByUserId(@Param("userId") long userId);

    /**
     * 获取所有可用的代理id
     *
     * @param tenantId 租户id
     * @return 代理id列表
     */
    @Select("SELECT DISTINCT id FROM system_agent WHERE status = 0 AND deleted = false AND tenant_id=#{tenantId}")
    List<Long> selectAllAvailableAgentIds(@Param("tenantId") Long tenantId);
}
