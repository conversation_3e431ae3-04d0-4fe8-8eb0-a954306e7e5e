package com.rf.exchange.module.system.controller.admin.auth.vo;

import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 账号密码登录 Request VO，如果登录并绑定社交用户，需要传递 social 开头的参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginReqVO {

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "exchangeyuanma")
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 6, message = "账号长度为 6-12 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @NotEmpty(message = "密码不能为空")
    @Length(min = 8, message = "密码长度为 8-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "密码格式为数字以及字母")
    private String password;

    @Schema(description = "谷歌验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String authCode;
}