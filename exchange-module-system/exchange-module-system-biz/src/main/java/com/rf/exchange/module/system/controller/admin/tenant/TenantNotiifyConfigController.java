package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.notifyconfig.TenantNotifyConfigRespVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.notifyconfig.TenantNotifyConfigSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "管理后台 - 租户通知配置")
@RestController
@RequestMapping("/system/tenant-dict-type")
@Validated
public class TenantNotiifyConfigController {
    @Resource
    private TenantDictDataService tenantDictDataService;

    private static final String dictType = "notify_config";

    @Operation(summary = "获取通知配置表")
    @GetMapping("list")
    public CommonResult<List<TenantNotifyConfigRespVO>> getList() {
        List<TenantDictDataDO> list = tenantDictDataService.getDictDataListByDictType(TenantContextHolder.getTenantId(), dictType);
        List<TenantNotifyConfigRespVO> ret = new ArrayList<>();
        for (TenantDictDataDO item : list) {
            ret.add(TenantNotifyConfigRespVO.builder().build().setId(item.getId()).setStatus(item.getStatus()).setName(item.getLabel()).setCmd(item.getValue()).setUrl(item.getRemark()));
        }
        return CommonResult.success(ret);
    }

    @Operation(summary = "修改通知配置表")
    @PostMapping("save")
    public CommonResult<Boolean> save(@Valid @RequestBody TenantNotifyConfigSaveReqVO reqVO) {
        TenantDictDataSaveReqVO updateObj = TenantDictDataSaveReqVO.builder().build().setDictType(dictType).setId(reqVO.getId()).setLabel(reqVO.getName()).setRemark(reqVO.getUrl()).setValue(reqVO.getCmd()).setStatus(reqVO.getStatus());
        tenantDictDataService.updateTenantDictData(updateObj);
        return CommonResult.success(true);
    }
}
