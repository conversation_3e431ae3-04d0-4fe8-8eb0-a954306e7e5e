package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 挖矿订单列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MinerProductOrderPageReqVO extends PageParam {

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "文字内容")
    private String title;

    @Schema(description = "创建时间")
    private Long[] createTime;

}
