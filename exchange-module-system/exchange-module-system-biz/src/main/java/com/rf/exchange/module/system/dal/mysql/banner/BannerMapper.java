package com.rf.exchange.module.system.dal.mysql.banner;

import java.util.*;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.dal.dataobject.banner.BannerDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.system.controller.admin.banner.vo.*;

/**
 * banner Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BannerMapper extends BaseMapperX<BannerDO> {

    default PageResult<BannerDO> selectPage(BannerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BannerDO>()
                .eqIfPresent(BannerDO::getTitle, reqVO.getTitle())
                .eqIfPresent(BannerDO::getImg, reqVO.getImg())
                .eqIfPresent(BannerDO::getSort, reqVO.getSort())
                .eqIfPresent(BannerDO::getLinkUrl, reqVO.getLinkUrl())
                .eqIfPresent(BannerDO::getLinkType, reqVO.getLinkType())
                .betweenIfPresent(BannerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BannerDO::getSort));
    }

    default List<BannerDO> getList() {
        return selectList();
    }
}