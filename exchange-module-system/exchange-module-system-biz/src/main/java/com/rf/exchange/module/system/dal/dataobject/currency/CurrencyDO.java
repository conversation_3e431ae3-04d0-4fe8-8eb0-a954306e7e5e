package com.rf.exchange.module.system.dal.dataobject.currency;

import com.rf.exchange.module.system.enums.currency.CurrencyTypeEnum;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统货币 DO
 *
 * <AUTHOR>
 */
@TableName("system_currency")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyDO extends BaseDO {

    /**
     * 币种编号
     */
    @TableId
    private Long id;
    /**
     * 币种名称
     */
    private String name;
    /**
     * 货币代码
     */
    private String code;
    /**
     * 币种符号如$，¥
     */
    private String symbol;
    /**
     * 货币类型 0:法币 1:加密货币
     * <p>
     * {@link CurrencyTypeEnum}
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;

}