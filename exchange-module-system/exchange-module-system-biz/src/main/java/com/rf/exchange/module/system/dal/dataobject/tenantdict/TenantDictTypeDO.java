package com.rf.exchange.module.system.dal.dataobject.tenantdict;

import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 租户配置分类 DO
 *
 * <AUTHOR>
 */
@TableName("system_tenant_dict_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantDictTypeDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码类型
     */
    private String type;
    /**
     * 状态
     */
    private Short status;
    /**
     * 备注
     */
    private String remark;

}