package com.rf.exchange.module.system.framework.datapermission.config;

import com.rf.exchange.framework.datapermission.core.rule.agent.AgentDataPermissionRuleCustomizer;
import com.rf.exchange.module.system.dal.dataobject.dept.DeptDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(DataPermissionProperties.class)
public class DataPermissionConfiguration {

    @Resource
    private DataPermissionProperties properties;

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(AdminUserDO.class);
            rule.addDeptColumn(DeptDO.class, "id");
            // user
            rule.addUserColumn(AdminUserDO.class, "id");
        };
    }

    @Bean
    @ConditionalOnProperty(prefix = "exchange.system.agent-data-permission", value = "enable")
    public AgentDataPermissionRuleCustomizer sysAgentDataPermissionRuleCustomizer() {
        return rule -> {
            // 设置需要处理代理数据权限的表
            for (String tableName: properties.getMatchUserIdTables()) {
                rule.addMatchUserIdTable(tableName);
            }
            for (String tableName: properties.getMatchAgentIdTables()) {
                rule.addMatchAgentIdTable(tableName);
            }
        };
    }
}
