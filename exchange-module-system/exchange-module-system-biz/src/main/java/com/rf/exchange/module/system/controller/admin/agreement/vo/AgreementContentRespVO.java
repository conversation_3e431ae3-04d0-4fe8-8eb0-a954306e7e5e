package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 协议多语言内容 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "管理后台 - 协议多语言内容 Response VO")
@Data
public class AgreementContentRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long agreementId;

    @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
    private String languageCode;

    @Schema(description = "语言显示名称", example = "简体中文")
    private String languageDisplayName;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    private String title;

    @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    @Schema(description = "内容长度", example = "1024")
    private Integer contentLength;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private String updater;

    // ========== 便利方法 ==========

    /**
     * 设置语言显示名称
     */
    public void setLanguageDisplayName() {
        this.languageDisplayName = switch (this.languageCode) {
            case "zh-CN", "zh" -> "简体中文";
            case "zh-TW" -> "繁体中文";
            case "en", "en-US" -> "English";
            case "ja", "ja-JP" -> "日本語";
            case "ko", "ko-KR" -> "한국어";
            case "es" -> "Español";
            case "fr" -> "Français";
            case "de" -> "Deutsch";
            case "ru" -> "Русский";
            case "ar" -> "العربية";
            case "pt" -> "Português";
            case "it" -> "Italiano";
            case "th" -> "ไทย";
            case "vi" -> "Tiếng Việt";
            case "id" -> "Bahasa Indonesia";
            case "ms" -> "Bahasa Melayu";
            case "hi" -> "हिन्दी";
            default -> this.languageCode;
        };
    }

    /**
     * 设置内容长度
     */
    public void setContentLength() {
        this.contentLength = this.content != null ? this.content.length() : 0;
    }
}
