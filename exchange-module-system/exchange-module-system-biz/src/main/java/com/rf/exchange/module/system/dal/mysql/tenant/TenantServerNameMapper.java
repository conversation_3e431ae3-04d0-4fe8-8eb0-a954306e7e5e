package com.rf.exchange.module.system.dal.mysql.tenant;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNamePageReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
@Mapper
public interface TenantServerNameMapper extends BaseMapperX<TenantServerNameDO> {
    
    default List<TenantServerNameDO> selectServerNameList() {
        return selectList(new LambdaQueryWrapperX<TenantServerNameDO>()
                .eq(TenantServerNameDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
    }

    default PageResult<TenantServerNameDO> selectPage(TenantServerNamePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantServerNameDO>()
                .eqIfPresent(TenantServerNameDO::getTenId, reqVO.getTenId())
                .likeIfPresent(TenantServerNameDO::getServerName, reqVO.getServerName())
                .eqIfPresent(TenantServerNameDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TenantServerNameDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TenantServerNameDO::getId));
    }
}
