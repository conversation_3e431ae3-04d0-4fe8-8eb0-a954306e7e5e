package com.rf.exchange.module.system.service.tenant;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import com.rf.exchange.module.system.service.tenant.handler.TenantInfoHandler;
import com.rf.exchange.module.system.service.tenant.handler.TenantMenuHandler;

import jakarta.validation.Valid;

import java.util.List;
import java.util.Set;

/**
 * 租户 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantService {

    /**
     * 创建租户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenant(@Valid TenantSaveReqVO createReqVO);

    /**
     * 更新租户
     *
     * @param updateReqVO 更新信息
     */
    void updateTenant(@Valid TenantSaveReqVO updateReqVO);

    /**
     * 更新租户的角色菜单
     *
     * @param tenantId 租户编号
     * @param menuIds  菜单编号数组
     */
    void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds);

    /**
     * 删除租户
     *
     * @param id 编号
     */
    void deleteTenant(Long id);

    /**
     * 获得租户
     *
     * @param id 编号
     * @return 租户
     */
    TenantDO getTenant(Long id);

    /**
     * 获得租户分页
     *
     * @param pageReqVO 分页查询
     * @return 租户分页
     */
    PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO);

    /**
     * 获得名字对应的租户
     *
     * @param code 租户码
     * @return 租户
     */
    TenantDO getTenantByCode(String code);

    /**
     * 获得名字对应的租户
     *
     * @param name 租户名
     * @return 租户
     */
    TenantDO getTenantByName(String name);

    /**
     * 获得使用指定套餐的租户数量
     *
     * @param packageId 租户套餐编号
     * @return 租户数量
     */
    Long getTenantCountByPackageId(Long packageId);

    /**
     * 获得使用指定套餐的租户数组
     *
     * @param packageId 租户套餐编号
     * @return 租户数组
     */
    List<TenantDO> getTenantListByPackageId(Long packageId);

    /**
     * 获取所有租户列表
     *
     * @return 租户数组
     */
    List<TenantDO> getTenantList();

    /**
     * 进行租户的信息处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantInfo(TenantInfoHandler handler);

    /**
     * 进行租户的菜单处理逻辑
     * 其中，租户编号从 {@link TenantContextHolder} 上下文中获取
     *
     * @param handler 处理器
     */
    void handleTenantMenu(TenantMenuHandler handler);

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     * @return 租户信息
     */
    TenantDO validTenant(Long id);

    /**
     * 获取租户的默认语言
     * @param tenantId 租户id
     * @return 默认语言的code
     */
    String getTenantDefaultLang(Long tenantId);
}
