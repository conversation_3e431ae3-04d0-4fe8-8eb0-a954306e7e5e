package com.rf.exchange.module.system.dal.dataobject.agent;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 会员代理 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_agent", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentDO extends TenantBaseDO {
    /**
     * 代理ID
     */
    @TableId
    private Long id;
    /**
     * 系统用户的id
     */
    private Long userId;
    /**
     * 登录用户名
     */
    private String userName;
    /**
     * 代理名称
     */
    private String name;
    /**
     * 邀请码
     */
    private String code;
    /**
     * 备注
     */
    private String remark;
    /**
     * 父代理id
     */
    private Long ancestorId;
    /**
     * 父代理名称
     */
    private String ancestorName;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 是否默认
     */
    private Boolean first;

    @TableField(exist = false)
    private Integer dept;

    /**
     * 删除时间
     */
    private Long deleteTime;
}