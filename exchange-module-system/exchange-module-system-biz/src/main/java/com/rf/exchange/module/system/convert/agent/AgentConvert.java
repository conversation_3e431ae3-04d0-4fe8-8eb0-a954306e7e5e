package com.rf.exchange.module.system.convert.agent;

import com.rf.exchange.module.system.controller.admin.agent.vo.AgentSaveReqVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentSimpleInfoRespVO;
import com.rf.exchange.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface AgentConvert {

    com.rf.exchange.module.system.convert.agent.AgentConvert INSTANCE = Mappers.getMapper(com.rf.exchange.module.system.convert.agent.AgentConvert.class);

    default UserSaveReqVO convert(AgentSaveReqVO bean) {
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getName()).setMobile("");
        return reqVO;
    }

    AgentSimpleInfoRespVO convert2(AgentDO bean);

    List<AgentSimpleInfoRespVO> convertList2(List<AgentDO> list);
}