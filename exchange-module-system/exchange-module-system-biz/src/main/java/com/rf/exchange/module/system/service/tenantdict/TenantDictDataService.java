package com.rf.exchange.module.system.service.tenantdict;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveBatchReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 租户字典数据 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantDictDataService {
    /**
     * 校验字典数据们是否有效。如下情况，视为无效：
     * 1. 字典数据不存在
     * 2. 字典数据被禁用
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param values   字典数据值的数组
     */
    void validateDictDataList(Long tenantId, String dictType, Collection<String> values);

    /**
     * 获取指定租户的字典数据
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @param value 字典值
     * @return 字典数据
     */
    TenantDictDataDO getDictData(Long tenantId, String dictType, String value);

    /**
     * 解析获得指定的字典数据，从缓存中
     *
     * @param tenantId 租户id
     * @param dictType 字典类型
     * @param label    字典数据标签
     * @return 字典数据
     */
    TenantDictDataDO parseDictData(Long tenantId, String dictType, String label);

    /**
     * 获得指定数据类型的字典数据列表
     *
     * @param tenantId 租户编号
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<TenantDictDataDO> getDictDataListByDictType(Long tenantId, String dictType);

    /**
     * 获得字典数据列表
     *
     * @param status   状态
     * @param dictType 字典类型
     * @return 字典数据全列表
     */
    List<TenantDictDataDO> getDictDataList(Integer status, String dictType);

    /**
     * 创建租户字典数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantDictData(@Valid TenantDictDataSaveReqVO createReqVO);

    /**
     * 批量创建租户字典数据
     *
     * @param saveReqVO 创建信息
     * @param loginUser 当前登录用户
     */
    void batchCreateTenantDictData(@Valid TenantDictDataSaveBatchReqVO saveReqVO, LoginUser loginUser);

    /**
     * 更新租户字典数据
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantDictData(@Valid TenantDictDataSaveReqVO updateReqVO);

    /**
     * 删除租户字典数据
     *
     * @param id 编号
     */
    void deleteTenantDictData(Long id);

    /**
     * 获得租户字典数据
     *
     * @param id 编号
     * @return 租户字典数据
     */
    TenantDictDataDO getTenantDictData(Long id);

    /**
     * 获得租户字典数据分页
     *
     * @param pageReqVO 分页查询
     * @return 租户字典数据分页
     */
    PageResult<TenantDictDataDO> getTenantDictDataPage(TenantDictDataPageReqVO pageReqVO);

}