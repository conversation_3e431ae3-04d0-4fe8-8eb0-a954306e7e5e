package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataRespVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveBatchReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

@Tag(name = "管理后台 - 租户字典数据")
@RestController
@RequestMapping("/system/tenant-dict-data")
@Validated
public class TenantDictDataController {

    @Resource
    private TenantDictDataService tenantDictDataService;

    @PostMapping("/create")
    @Operation(summary = "创建租户字典数据")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:create')")
    public CommonResult<Long> createTenantDictData(@Valid @RequestBody TenantDictDataSaveReqVO createReqVO) {
        Long id = TenantUtils.execute(createReqVO.getTenantId(), () -> tenantDictDataService.createTenantDictData(createReqVO));
        return success(id);
    }

    @PostMapping("/create/batch")
    @Operation(summary = "批量创建租户字典数据")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:create:batch')")
    @TenantIgnore
    public CommonResult<Boolean> batchCreateTenantDictData(@Valid @RequestBody TenantDictDataSaveBatchReqVO createReqVO) {
        tenantDictDataService.batchCreateTenantDictData(createReqVO, getLoginUser());
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户字典数据")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateTenantDictData(@Valid @RequestBody TenantDictDataSaveReqVO updateReqVO) {
        tenantDictDataService.updateTenantDictData(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户字典数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:delete')")
    @TenantIgnore
    public CommonResult<Boolean> deleteTenantDictData(@RequestParam("id") Long id) {
        tenantDictDataService.deleteTenantDictData(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户字典数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:query')")
    @TenantIgnore
    public CommonResult<TenantDictDataRespVO> getTenantDictData(@RequestParam("id") Long id) {
        TenantDictDataDO tenantDictData = tenantDictDataService.getTenantDictData(id);
        return success(BeanUtils.toBean(tenantDictData, TenantDictDataRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得租户字典数据分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:query')")
    public CommonResult<PageResult<TenantDictDataRespVO>> getTenantDictDataPage(@Valid @RequestBody TenantDictDataPageReqVO pageReqVO) {
        PageResult<TenantDictDataDO> pageResult = TenantUtils.execute(pageReqVO.getTenantId(), () -> tenantDictDataService.getTenantDictDataPage(pageReqVO));
        return success(BeanUtils.toBean(pageResult, TenantDictDataRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户字典数据 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantDictDataExcel(@Valid TenantDictDataPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantDictDataDO> list = tenantDictDataService.getTenantDictDataPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户字典数据.xls", "数据", TenantDictDataRespVO.class,
                BeanUtils.toBean(list, TenantDictDataRespVO.class));
    }

}