package com.rf.exchange.module.system.controller.app.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户 App - 协议内容简化 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "用户 App - 协议内容简化 Response VO")
@Data
public class AppAgreementSimpleRespVO {

    @Schema(description = "语言显示名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "简体中文")
    private String languageDisplayName;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    private String title;

    @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    // ========== 便利方法 ==========

    /**
     * 根据语言代码设置语言显示名称
     */
    public void setLanguageDisplayName(String languageCode) {
        this.languageDisplayName = getLanguageDisplayName(languageCode);
    }

    /**
     * 获取语言显示名称
     */
    private String getLanguageDisplayName(String languageCode) {
        if (languageCode == null) {
            return "";
        }
        return switch (languageCode) {
            case "zh-CN", "zh" -> "简体中文";
            case "zh-TW" -> "繁体中文";
            case "en", "en-US" -> "English";
            case "ja", "ja-JP" -> "日本語";
            case "ko", "ko-KR" -> "한국어";
            case "es" -> "Español";
            case "fr" -> "Français";
            case "de" -> "Deutsch";
            case "ru" -> "Русский";
            case "ar" -> "العربية";
            case "pt" -> "Português";
            case "it" -> "Italiano";
            case "th" -> "ไทย";
            case "vi" -> "Tiếng Việt";
            case "id" -> "Bahasa Indonesia";
            case "ms" -> "Bahasa Melayu";
            case "hi" -> "हिन्दी";
            default -> languageCode;
        };
    }
}
