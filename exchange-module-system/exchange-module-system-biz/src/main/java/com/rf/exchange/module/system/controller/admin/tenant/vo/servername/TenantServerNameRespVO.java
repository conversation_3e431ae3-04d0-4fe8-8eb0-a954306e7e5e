package com.rf.exchange.module.system.controller.admin.tenant.vo.servername;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 系统租户域名 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantServerNameRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10502")
    @ExcelProperty("租户id")
    private Long tenId;

    @Schema(description = "域名", requiredMode = Schema.RequiredMode.REQUIRED, example = "www.google.com")
    @ExcelProperty("域名")
    private String serverName;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("开启状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "是否作为分享链接域名", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("分享链接域名")
    private Boolean isShare;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}