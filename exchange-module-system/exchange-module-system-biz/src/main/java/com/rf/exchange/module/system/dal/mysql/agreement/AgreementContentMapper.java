package com.rf.exchange.module.system.dal.mysql.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementContentPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协议多语言内容 Mapper
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Mapper
public interface AgreementContentMapper extends BaseMapperX<AgreementContentDO> {

    /**
     * 分页查询协议多语言内容
     */
    default PageResult<AgreementContentDO> selectPage(AgreementContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgreementContentDO>()
                .eqIfPresent(AgreementContentDO::getAgreementId, reqVO.getAgreementId())
                .eqIfPresent(AgreementContentDO::getLanguageCode, reqVO.getLanguageCode())
                .likeIfPresent(AgreementContentDO::getTitle, reqVO.getTitle())
                .betweenIfPresent(AgreementContentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AgreementContentDO::getId));
    }

    /**
     * 根据协议ID查询所有语言版本
     */
    default List<AgreementContentDO> selectByAgreementId(Long agreementId) {
        return selectList(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getDeleted, false)
                .orderByAsc(AgreementContentDO::getLanguageCode));
    }

    /**
     * 根据协议ID和语言代码查询
     */
    default AgreementContentDO selectByAgreementIdAndLanguage(Long agreementId, String languageCode) {
        return selectOne(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getLanguageCode, languageCode)
                .eq(AgreementContentDO::getDeleted, false));
    }

    /**
     * 根据协议ID和语言代码列表查询
     */
    default List<AgreementContentDO> selectByAgreementIdAndLanguages(Long agreementId, List<String> languageCodes) {
        return selectList(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .in(AgreementContentDO::getLanguageCode, languageCodes)
                .eq(AgreementContentDO::getDeleted, false)
                .orderByAsc(AgreementContentDO::getLanguageCode));
    }

    /**
     * 查询指定语言代码的所有协议内容
     */
    default List<AgreementContentDO> selectByLanguageCode(String languageCode) {
        return selectList(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getLanguageCode, languageCode)
                .eq(AgreementContentDO::getDeleted, false)
                .orderByAsc(AgreementContentDO::getAgreementId));
    }

    /**
     * 统计协议的语言版本数量
     */
    default Long countByAgreementId(Long agreementId) {
        return selectCount(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getDeleted, false));
    }

    /**
     * 查询缺少指定语言版本的协议ID列表
     */
    default List<Long> selectAgreementIdsMissingLanguage(String languageCode) {
        return selectObjs(new LambdaQueryWrapperX<AgreementContentDO>()
                .select(AgreementContentDO::getAgreementId)
                .notExists("SELECT 1 FROM system_agreement_content ac2 " +
                          "WHERE ac2.agreement_id = system_agreement_content.agreement_id " +
                          "AND ac2.language_code = '" + languageCode + "' " +
                          "AND ac2.deleted = 0")
                .eq(AgreementContentDO::getDeleted, false)
                .groupBy(AgreementContentDO::getAgreementId));
    }

    /**
     * 根据协议ID和语言代码查询协议内容（简化版本）
     * 注意：这个方法不直接支持租户ID和类型查询，需要在Service层先查询协议ID
     */
    default AgreementContentDO selectByAgreementIdAndLanguageCode(Long agreementId, String languageCode) {
        return selectOne(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getLanguageCode, languageCode)
                .eq(AgreementContentDO::getDeleted, false));
    }

    /**
     * 根据协议ID查询支持的语言列表
     */
    @Select("SELECT DISTINCT language_code FROM system_agreement_content " +
            "WHERE agreement_id = #{agreementId} AND deleted = 0 " +
            "ORDER BY language_code")
    List<String> selectLanguagesByAgreementId(@Param("agreementId") Long agreementId);



    /**
     * 删除协议的指定语言版本
     */
    default int deleteByAgreementIdAndLanguage(Long agreementId, String languageCode) {
        return delete(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getLanguageCode, languageCode));
    }

    /**
     * 批量删除协议的所有语言版本
     */
    int deleteByAgreementId(@Param("agreementId") Long agreementId, @Param("updater") String updater);

    /**
     * 批量更新语言内容的协议ID（用于协议合并等场景）
     */
    int updateAgreementId(@Param("oldAgreementId") Long oldAgreementId, 
                         @Param("newAgreementId") Long newAgreementId, 
                         @Param("updater") String updater);

    /**
     * 查询协议多语言统计信息
     */
    List<AgreementContentStatistics> selectLanguageStatistics();

    /**
     * 协议多语言统计信息内部类
     */
    class AgreementContentStatistics {
        private String languageCode;
        private String languageDisplayName;
        private Long agreementCount;
        private Long totalContentLength;
        
        // getters and setters
        public String getLanguageCode() { return languageCode; }
        public void setLanguageCode(String languageCode) { this.languageCode = languageCode; }
        
        public String getLanguageDisplayName() { return languageDisplayName; }
        public void setLanguageDisplayName(String languageDisplayName) { this.languageDisplayName = languageDisplayName; }
        
        public Long getAgreementCount() { return agreementCount; }
        public void setAgreementCount(Long agreementCount) { this.agreementCount = agreementCount; }
        
        public Long getTotalContentLength() { return totalContentLength; }
        public void setTotalContentLength(Long totalContentLength) { this.totalContentLength = totalContentLength; }
    }
}
