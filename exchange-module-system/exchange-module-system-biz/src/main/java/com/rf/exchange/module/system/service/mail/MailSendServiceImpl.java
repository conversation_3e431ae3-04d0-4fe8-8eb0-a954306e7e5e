package com.rf.exchange.module.system.service.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailTemplateDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.module.system.mq.message.mail.MailSendMessage;
import com.rf.exchange.module.system.mq.producer.mail.MailProducer;
import com.rf.exchange.module.system.service.member.MemberService;
import com.rf.exchange.module.system.service.tenant.TenantService;
import com.rf.exchange.module.system.service.user.AdminUserService;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.extra.mail.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 邮箱发送 Service 实现类
 *
 * @since 2022-03-21
 */
@Service
@Validated
@Slf4j
public class MailSendServiceImpl implements MailSendService {

    @Resource
    private AdminUserService adminUserService;
    @Resource
    private MemberService memberService;
    @Resource
    private MailAccountService mailAccountService;
    @Resource
    private MailTemplateService mailTemplateService;
    @Resource
    private MailLogService mailLogService;
    @Resource
    private MailProducer mailProducer;
    @Resource
    private TenantService tenantService;

    @Override
    public Long testSendMailTo(String mail, String lang, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams) {
        return sendSingleMail(mail, lang, null, UserTypeEnum.MEMBER.getValue(), tenantId, templateCode, domain, templateParams);
    }

    @Override
    public Long sendSingleMailToAdmin(String mail, String lang, Long userId, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            AdminUserDO user = adminUserService.getUser(userId);
            if (user != null) {
                mail = user.getEmail();
            }
        }
        // 执行发送
        return sendSingleMail(mail, lang, userId, UserTypeEnum.ADMIN.getValue(), tenantId, templateCode, domain, templateParams);
    }

    @Override
    public Long sendSingleMailToMember(String mail, String lang, Long userId, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams) {
        // 如果 mail 为空，则加载用户编号对应的邮箱
        if (StrUtil.isEmpty(mail)) {
            mail = memberService.getMemberUserEmail(userId);
        }
        // 执行发送
        return sendSingleMail(mail, lang, userId, UserTypeEnum.MEMBER.getValue(), tenantId, templateCode, domain, templateParams);
    }

    @Override
    @Master
    public Long sendSingleMail(String mail, String lang, Long userId, Integer userType, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams) {
        // 获取租户信息
        tenantService.validTenant(tenantId);
        // 校验邮箱模版是否合法
        MailTemplateDO template = validateMailTemplate(templateCode, lang, tenantId);
        // 校验是否有合适的邮箱账号
        MailAccountDO account = validateMailTenantId(tenantId, templateCode, domain);
        // 校验邮箱是否存在
        mail = validateMail(mail);
        // 验证模版和模板参数
        validateTemplateParams(template, templateParams);
        // 创建发送日志。如果模板被禁用，则不发送邮件，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus());
        // 格式化邮件标题
        String title = mailTemplateService.formatMailTemplateContent(template.getTitle(), templateParams);
        // 格式化邮件内容
        String content = mailTemplateService.formatMailTemplateContent(template.getContent(), templateParams);
        // 保存邮件发送日志
        Long sendLogId = mailLogService.createMailLog(tenantId, userId, userType, mail, account, template, content, templateParams, isSend);

        // 发送 MQ 消息，异步执行发送短信
        if (isSend) {
            mailProducer.sendMailSendMessage(sendLogId, mail, account.getId(), account.getSenderName(), title, content);
        }
        return sendLogId;
    }

    @Override
    public void doSendMail(MailSendMessage message) {
        // 1. 创建发送账号
        MailAccountDO account = validateMailAccount(message.getAccountId());
        MailAccount mailAccount = buildMailAccount(account, message.getNickname());
        // 2. 发送邮件
        try {
            String messageId = MailUtil.send(mailAccount, message.getMail(), message.getTitle(), message.getContent(), true);
            // 去除messageId开头和末尾的<>符号
            final String processedMessageId = messageId.replaceAll("[<>]", "");
            // 3. 更新结果（成功）
            mailLogService.updateMailSendResult(message.getLogId(), processedMessageId, null);
        } catch (Exception e) {
            // 3. 更新结果（异常）
            mailLogService.updateMailSendResult(message.getLogId(), null, e);
        }
    }

    private MailAccount buildMailAccount(MailAccountDO account, String nickname) {
        String from = StrUtil.isNotEmpty(nickname) ? nickname + " <" + account.getMail() + ">" : account.getMail();
        return new MailAccount().setFrom(from).setAuth(true)
                .setUser(account.getUsername()).setPass(account.getPassword().toCharArray())
                .setHost(account.getHost()).setPort(account.getPort())
                .setSslEnable(account.getSslEnable()).setStarttlsEnable(account.getStarttlsEnable());
    }

    @VisibleForTesting
    MailTemplateDO validateMailTemplate(String templateCode, String lang, Long tenantId) {
        // 获得邮件模板。考虑到效率，从缓存中获取
        MailTemplateDO template = mailTemplateService.getMailTemplateByCodeFromCache(templateCode, lang, tenantId);
        // 邮件模板不存在
        if (template == null) {
            throw exception(MAIL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    MailAccountDO validateMailTenantId(Long tenantId, String templateCount, String domain) {
        final List<MailAccountDO> accountList = mailAccountService.getMailAccountListFromCache(tenantId, domain);
        if (CollectionUtil.isEmpty(accountList)) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
        for (MailAccountDO account : accountList) {
            // 先判断邮箱账号支持的模板是否匹配
            List<String> validTemplateCodes = StrUtil.split(account.getTemplateCodes(), StrUtil.COMMA);
            if (CollectionUtil.isEmpty(validTemplateCodes)) {
                continue;
            }
            // 如果需要判断邮箱域
            if (StrUtil.isNotEmpty(domain)) {
                // 如果账号的模版编码中包含则可以使用
                if (validTemplateCodes.contains(templateCount) && domain.equalsIgnoreCase(account.getDomain())) {
                    return account;
                }
            } else {
                if (validTemplateCodes.contains(templateCount)) {
                    return account;
                }
            }
        }
        throw exception(MAIL_ACCOUNT_TEMPLATE_NOT_MATCH);
    }

    @VisibleForTesting
    MailAccountDO validateMailAccount(Long accountId) {
        // 获得邮箱账号。考虑到效率，从缓存中获取
        MailAccountDO account = mailAccountService.getMailAccountFromCache(accountId);
        // 邮箱账号不存在
        if (account == null) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
        return account;
    }

    @VisibleForTesting
    String validateMail(String mail) {
        if (StrUtil.isEmpty(mail)) {
            throw exception(MAIL_SEND_MAIL_NOT_EXISTS);
        }
        return mail;
    }

    /**
     * 校验邮件参数是否确实
     *
     * @param template       邮箱模板
     * @param templateParams 参数列表
     */
    @VisibleForTesting
    void validateTemplateParams(MailTemplateDO template, Map<String, Object> templateParams) {
        template.getParams().forEach(key -> {
            Object value = templateParams.get(key);
            if (value == null) {
                throw exception(MAIL_SEND_TEMPLATE_PARAM_MISS, key);
            }
        });
    }

}
