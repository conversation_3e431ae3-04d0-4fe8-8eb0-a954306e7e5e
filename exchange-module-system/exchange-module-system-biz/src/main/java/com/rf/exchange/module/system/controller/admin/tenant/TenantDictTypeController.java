package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.*;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictTypeDO;
import com.rf.exchange.module.system.service.tenantdict.TenantDictTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

@Tag(name = "管理后台 - 租户配置分类")
@RestController
@RequestMapping("/system/tenant-dict-type")
@Validated
public class TenantDictTypeController {

    @Resource
    private TenantDictTypeService tenantDictTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建租户字典类型")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:create')")
    public CommonResult<Long> createTenantDictType(@Valid @RequestBody TenantDictTypeSaveReqVO createReqVO) {
        Long suc=TenantUtils.execute(createReqVO.getTenantId(),()-> tenantDictTypeService.createTenantDictType(createReqVO));
        return success(suc);
    }

    @PostMapping("/create/batch")
    @Operation(summary = "批量创建租户字典类型")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:create:batch')")
    @TenantIgnore
    public CommonResult<Boolean> batchCreateTenantDictType(@Valid @RequestBody TenantDictTypeSaveBatchReqVO createReqVO) {
        tenantDictTypeService.batchCreateTenantDictType(createReqVO, getLoginUser());
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户配置分类")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:update')")
    @TenantIgnore
    public CommonResult<Boolean> updateTenantDictType(@Valid @RequestBody TenantDictTypeSaveReqVO updateReqVO) {
        tenantDictTypeService.updateTenantDictType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户配置分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:delete')")
    @TenantIgnore
    public CommonResult<Boolean> deleteTenantDictType(@RequestParam("id") Long id) {
        tenantDictTypeService.deleteTenantDictType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户配置分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:query')")
    @TenantIgnore
    public CommonResult<TenantDictTypeRespVO> getTenantDictType(@RequestParam("id") Long id) {
        TenantDictTypeDO tenantDictType = tenantDictTypeService.getTenantDictType(id);
        return success(BeanUtils.toBean(tenantDictType, TenantDictTypeRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得租户配置分类分页")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:query')")
    public CommonResult<PageResult<TenantDictTypeRespVO>> getTenantDictTypePage(@Valid @RequestBody TenantDictTypePageReqVO pageReqVO) {
        PageResult<TenantDictTypeDO> pageResult = TenantUtils.execute(pageReqVO.getTenantId(), () -> {
            PageResult<TenantDictTypeDO> ret = tenantDictTypeService.getTenantDictTypePage(pageReqVO);
            return ret;
        });
        return success(BeanUtils.toBean(pageResult, TenantDictTypeRespVO.class));
    }

    @GetMapping(value = "/list-all-simple")
    @Operation(summary = "获得全部字典类型列表", description = "包括开启 + 禁用的字典类型，主要用于前端的下拉选项")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:query')")
    public CommonResult<List<TenantDictTypeSimpleRespVO>> getSimpleDictTypeList() {
        List<TenantDictTypeDO> list = tenantDictTypeService.getDictTypeList();
        return success(BeanUtils.toBean(list, TenantDictTypeSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户配置分类 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-type:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantDictTypeExcel(@Valid TenantDictTypePageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantDictTypeDO> list = tenantDictTypeService.getTenantDictTypePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户配置分类.xls", "数据", TenantDictTypeRespVO.class,
                BeanUtils.toBean(list, TenantDictTypeRespVO.class));
    }

}