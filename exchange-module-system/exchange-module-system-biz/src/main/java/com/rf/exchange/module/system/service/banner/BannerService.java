package com.rf.exchange.module.system.service.banner;

import java.util.*;
import jakarta.validation.*;
import com.rf.exchange.module.system.controller.admin.banner.vo.*;
import com.rf.exchange.module.system.dal.dataobject.banner.BannerDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.PageParam;

/**
 * banner Service 接口
 *
 * <AUTHOR>
 */
public interface BannerService {

    /**
     * 创建banner
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBanner(@Valid BannerSaveReqVO createReqVO);

    /**
     * 更新banner
     *
     * @param updateReqVO 更新信息
     */
    void updateBanner(@Valid BannerSaveReqVO updateReqVO);

    /**
     * 删除banner
     *
     * @param id 编号
     */
    void deleteBanner(Long id);

    /**
     * 获得banner
     *
     * @param id 编号
     * @return banner
     */
    BannerDO getBanner(Long id);

    /**
     * 获得banner分页
     *
     * @param pageReqVO 分页查询
     * @return banner分页
     */
    PageResult<BannerDO> getBannerPage(BannerPageReqVO pageReqVO);

    /**
     * 获取banner列表
     * @return
     */
    List<BannerDO> getList();
}