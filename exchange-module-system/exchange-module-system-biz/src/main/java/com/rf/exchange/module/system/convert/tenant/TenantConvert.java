package com.rf.exchange.module.system.convert.tenant;

import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.rf.exchange.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 租户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {

    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);

    default UserSaveReqVO convert(TenantSaveReqVO bean) {
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile());
        return reqVO;
    }

    List<TenantRespDTO> convertList(List<TenantDO> beans);
}
