package com.rf.exchange.module.system.api.agent;

import com.rf.exchange.module.system.service.agent.AgentStatisticService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class AgentStatisticApiImpl implements AgentStatisticApi {
    @Resource
    private AgentStatisticService agentStatisticService;

    @Override
    public void incRegisterCount(Long agentId) {
        agentStatisticService.incRegister(agentId);
    }

    @Override
    public void incRechargeCount(Long agentId, boolean incCount, BigDecimal amount) {
        agentStatisticService.incRecharge(agentId, incCount, amount);
    }

    @Override
    public void incWithdrawCount(Long agentId, boolean incCount, BigDecimal amount) {
        agentStatisticService.incWithdraw(agentId, incCount, amount);
    }
}
