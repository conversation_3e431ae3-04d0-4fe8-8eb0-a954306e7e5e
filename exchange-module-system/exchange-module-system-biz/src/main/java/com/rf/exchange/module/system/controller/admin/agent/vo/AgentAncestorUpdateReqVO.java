package com.rf.exchange.module.system.controller.admin.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-31
 */
@Schema(description = "管理后台 - 移动代理 Request VO")
@Data
public class AgentAncestorUpdateReqVO {

    @Schema(description = "需要移动的代理id", example = "1024", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "agentId不能为空")
    private Long agentId;

    @Schema(description = "新的父代理id", example = "121", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "newAncestorId不能为空")
    private Long newAncestorId;
}
