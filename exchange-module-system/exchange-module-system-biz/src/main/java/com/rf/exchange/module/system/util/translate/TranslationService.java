package com.rf.exchange.module.system.util.translate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
@Component
@Slf4j
public class TranslationService {
    public String translate(String text,String lang) {
//        String url = "http://translate.codeai.net.cn/api/index/translate?token=VDLXblNX3VfKUCqP8QaV";
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//
//        try {
//            HttpPost request = new HttpPost(url);
//            request.setHeader("Content-Type", "application/json");
//
//            ObjectMapper objectMapper = new ObjectMapper();
//            String payload = objectMapper.writeValueAsString(new TranslationRequest(text, lang));
//
//            StringEntity entity = new StringEntity(payload);
//            request.setEntity(entity);
//
//            CloseableHttpResponse response = httpClient.execute(request);
//            try {
//                HttpEntity responseEntity = response.getEntity();
//                if (responseEntity != null) {
//                    String result = EntityUtils.toString(responseEntity);
//                    JsonNode jsonResponse = objectMapper.readTree(result);
//                    return jsonResponse.path("data").path("text").asText();
//                }
//            } catch (ParseException e) {
//                e.printStackTrace();
//            } finally {
//                response.close();
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                httpClient.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return "fail";
        return "";
    }

    static class TranslationRequest {
        private String keywords;
        private String targetLanguage;

        public TranslationRequest(String keywords, String targetLanguage) {
            this.keywords = keywords;
            this.targetLanguage = targetLanguage;
        }

        public String getKeywords() {
            return keywords;
        }

        public void setKeywords(String keywords) {
            this.keywords = keywords;
        }

        public String getTargetLanguage() {
            return targetLanguage;
        }

        public void setTargetLanguage(String targetLanguage) {
            this.targetLanguage = targetLanguage;
        }
    }
}
