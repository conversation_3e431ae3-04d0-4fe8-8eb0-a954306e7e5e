package com.rf.exchange.module.system.controller.admin.currency.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 系统货币分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CurrencyPageReqVO extends PageParam {

    @Schema(description = "币种名称", example = "王五")
    private String name;

    @Schema(description = "货币代码")
    private String code;

    @Schema(description = "币种符号如$，¥")
    private String symbol;

    @Schema(description = "货币类型 0:法币 1:加密货币", example = "1")
    private Integer type;

    @Schema(description = "开启状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}