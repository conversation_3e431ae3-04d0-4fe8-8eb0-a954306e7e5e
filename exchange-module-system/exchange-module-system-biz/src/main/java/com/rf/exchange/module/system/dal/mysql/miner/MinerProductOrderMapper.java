package com.rf.exchange.module.system.dal.mysql.miner;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MinerProductOrderMapper extends BaseMapperX<MinerProductOrder> {

    default PageResult<MinerProductOrder> selectPage(MinerOrderPageReqVo reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MinerProductOrder>()
                .betweenIfPresent(MinerProductOrder::getCreateTime, reqVO.getCreateTime())
                .eq(reqVO.getUserId()!=null, MinerProductOrder::getUserId, reqVO.getUserId())
                .eq(reqVO.getOrderStatus()!=null, MinerProductOrder::getOrderStatus, reqVO.getOrderStatus())
                .eq(reqVO.getOrderNo()!=null, MinerProductOrder::getOrderNo, reqVO.getOrderNo())
                .orderByDesc(MinerProductOrder::getId));
    }

}
