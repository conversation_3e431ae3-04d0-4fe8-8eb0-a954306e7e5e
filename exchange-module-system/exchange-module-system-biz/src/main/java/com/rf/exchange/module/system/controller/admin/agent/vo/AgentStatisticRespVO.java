package com.rf.exchange.module.system.controller.admin.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 代理统计 Response VO")
@Data
public class AgentStatisticRespVO {
    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long agentId;
    @Schema(description = "代理", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String agentName;
    @Schema(description = "注册人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long registerCount;
    @Schema(description = "充值人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long rechargeCount;
    @Schema(description = "提现人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long withdrawCount;
    @Schema(description = "总余额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private BigDecimal totalbalance;
    @Schema(description = "总充值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private BigDecimal totalDepositAmount;
    @Schema(description = "总提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private BigDecimal totalWithdrawAmount;
}
