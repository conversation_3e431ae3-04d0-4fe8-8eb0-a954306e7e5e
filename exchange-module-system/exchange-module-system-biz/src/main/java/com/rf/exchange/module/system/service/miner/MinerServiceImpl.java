package com.rf.exchange.module.system.service.miner;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.module.member.api.balance.MemberBalanceApi;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.MemberUserApi;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductIncomePageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductOrderResp;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductCreateOrderReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductInfoReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerRedemptionOrderReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductIncome;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;
import com.rf.exchange.module.system.dal.mysql.miner.MinerProductIncomeMapper;
import com.rf.exchange.module.system.dal.mysql.miner.MinerProductMapper;
import com.rf.exchange.module.system.dal.mysql.miner.MinerProductOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * 短信渠道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MinerServiceImpl implements MinerService {

    @Resource
    @Lazy
    private MemberUserApi memberUserApi;

    @Resource
    @Lazy
    private MemberBalanceApi memberBalanceApi;

    @Resource
    private MinerProductMapper minerProductMapper;

    @Resource
    private MinerProductOrderMapper minerProductOrderMapper;

    @Resource
    private MinerProductIncomeMapper minerProductIncomeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MinerProductOrder createOrder(MinerProductCreateOrderReqVo reqVO) {
        MinerProduct product = minerProductMapper.selectById(reqVO.getProductId());
        if (product == null) {
            throw exception(MINER_NOT_EXISTS);
        }
        //判断最低消费金额
        if (reqVO.getAmount().compareTo(product.getMinPurchase()) < 0) {
            throw exception(MINER_AMOUNT_LIMIT);
        }
        if (reqVO.getAmount().compareTo(product.getMaxPurchase()) > 0) {
            throw exception(MINER_AMOUNT_MAX_LIMIT);
        }
        if (product.getCycle()==null || product.getCycle()<1) {
            throw exception(MINER_CYCLE_ERR);
        }
        //判断用户金额
        Long userId = getLoginUserId();
        final MemberUserRespDTO user = memberUserApi.checkUserExists(userId);
        // 判断用户余额是否足够 (需要大于保证金加上手续费)
        memberBalanceApi.checkUSDTBalanceEnough(userId, reqVO.getAmount());
        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.UPDATE_BALANCE);

        //创建业务订单
        MinerProductOrder insert = new MinerProductOrder();
        insert.setCycle(product.getCycle());
        insert.setUsername(user.getUsername());
        insert.setUserId(userId);
        insert.setOrderAmount(reqVO.getAmount());
        insert.setOrderIncome(BigDecimal.ZERO);
        insert.setOrderStatus(1);
        insert.setProductId(product.getId());
        insert.setOrderNo(orderNo);
        insert.setLiquidatedDamages(BigDecimal.ZERO);
        insert.setLiquidatedDamagesRatio(product.getLiquidatedDamagesRatio());
        long expireTime = DateUtils.getUnixTimestampNow() + product.getCycle() * 24L * 60 * 60 * 1000;
        insert.setExpireTime(expireTime);

        minerProductOrderMapper.insert(insert);
        //账变和余额变更
        updateUserBalance(user,reqVO.getAmount(),orderNo,MemberTransactionsTypeEnum.MINING_ORDER,false);
        return insert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean redemptionOrder(MinerRedemptionOrderReqVo reqVO) {

        LambdaQueryWrapper<MinerProductOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MinerProductOrder::getOrderNo,reqVO.getOrderNo());
        MinerProductOrder productOrder = minerProductOrderMapper.selectOne(queryWrapper);
        if (productOrder == null) {
            throw exception(MINER_NOT_EXISTS);
        }
        if (productOrder.getOrderStatus()!=1){
            throw exception(MINER_ORDER_STATUS_ERROR);
        }

        Long userId = getLoginUserId();
        MemberUserRespDTO userDTO = memberUserApi.checkUserExists(userId);
        BigDecimal redemptionAmount = productOrder.getOrderAmount().multiply(productOrder.getLiquidatedDamagesRatio());
        //退回本金
        updateUserBalance(userDTO,productOrder.getOrderAmount(), productOrder.getOrderNo(),MemberTransactionsTypeEnum.MINING_ORDER_PRINCIPAL_REFUND,true);
        //扣除违约金
        updateUserBalance(userDTO,redemptionAmount, productOrder.getOrderNo(),MemberTransactionsTypeEnum.MINING_ORDER_REDEMPTION_PENALTY,false);
        //修改订单状态
        productOrder.setOrderStatus(2);
        productOrder.setLiquidatedDamages(redemptionAmount);
        minerProductOrderMapper.updateById(productOrder);
        return true;
    }

    @Override
    public Boolean createMinerProduct(MinerProductReqVO reqVO) {
        MinerProduct minerProduct = BeanUtils.toBean(reqVO, MinerProduct.class);
        return minerProductMapper.insert(minerProduct) > 0;
    }

    @Override
    public Boolean updateMinerProduct(MinerProductReqVO reqVO) {
        MinerProduct product = minerProductMapper.selectById(reqVO.getId());
        if (product == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        MinerProduct minerProduct = BeanUtils.toBean(reqVO, MinerProduct.class);
        return minerProductMapper.updateById(minerProduct) > 0;
    }

    @Override
    public Boolean deleteMinerProduct(Long id) {
        LambdaQueryWrapper<MinerProductOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MinerProductOrder::getProductId, id);
        queryWrapper.eq(MinerProductOrder::getOrderStatus, 1);
        Long count = minerProductOrderMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw exception(MINER_HAS_ORDER);
        }
        return minerProductMapper.deleteById(id) > 0;
    }

    @Override
    public PageResult<MinerProduct> getMinerPage(MinerProductPageReqVO pageReqVO) {
        return minerProductMapper.selectPage(pageReqVO);
    }

    @Override
    public MinerProduct getMinerInfo(MinerProductInfoReqVo reqVo) {
        return minerProductMapper.selectById(reqVo.getId());
    }

    @Override
    public PageResult<MinerProductOrderResp> getMinerPageOrders(MinerOrderPageReqVo pageReqVO) {
        PageResult<MinerProductOrder> orderPage = minerProductOrderMapper.selectPage(pageReqVO);

        if (CollUtil.isEmpty(orderPage.getList())) {
            return new PageResult<>(List.of(), orderPage.getTotal(), orderPage.getPageNo(), orderPage.getTotalPage());
        }

        List<MinerProductOrderResp> resultList = BeanUtils.toBean(orderPage.getList(), MinerProductOrderResp.class);
        fillOrderTitles(resultList);
        return new PageResult<>(resultList, orderPage.getTotal(), orderPage.getPageNo(), orderPage.getTotalPage());
    }


    @Override
    public PageResult<MinerProductOrderResp> appMinerPageOrders(MinerOrderPageReqVo pageReqVO) {
        pageReqVO.setUserId(getLoginUserId());
        PageResult<MinerProductOrder> orderPage = minerProductOrderMapper.selectPage(pageReqVO);

        if (CollUtil.isEmpty(orderPage.getList())) {
            return new PageResult<>(List.of(), orderPage.getTotal(), orderPage.getPageNo(), orderPage.getTotalPage());
        }

        List<MinerProductOrderResp> resultList = BeanUtils.toBean(orderPage.getList(), MinerProductOrderResp.class);
        fillOrderTitles(resultList);
        return new PageResult<>(resultList, orderPage.getTotal(), orderPage.getPageNo(), orderPage.getTotalPage());
    }


    @Override
    public PageResult<MinerProductIncome> getMinerPageIncome(MinerProductIncomePageReqVO pageReqVO) {
        return minerProductIncomeMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<MinerProductIncome> appMinerPageIncome(MinerProductIncomePageReqVO pageReqVO) {
        pageReqVO.setUserId(getLoginUserId());
        return minerProductIncomeMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @TenantIgnore
    public void minerOrderIncomeJob() {
        //查看当前时间的订单
        LambdaQueryWrapperX<MinerProductOrder> queryWrapper = new LambdaQueryWrapperX<>();
        long timestampNow = DateUtils.getUnixTimestampNow();
        queryWrapper.eq(MinerProductOrder::getOrderStatus, 1);
        queryWrapper.ge(MinerProductOrder::getExpireTime, timestampNow);
        queryWrapper.and(qw ->
                qw.lt(MinerProductOrder::getRewardDate, LocalDate.now())
                        .or()
                        .isNull(MinerProductOrder::getRewardDate)
        );
        Long count = minerProductOrderMapper.selectCount(queryWrapper);
        if (count <= 0 ){
            log.info("暂无挖矿订单需要发送收益-当前时间{}", timestampNow);
            return;
        }
        int pageNum = 1;
        int pageSize = 100;

        while (true) {
            Page<MinerProductOrder> page = new Page<>(pageNum, pageSize);
            Page<MinerProductOrder> selectPage = minerProductOrderMapper.selectPage(page, queryWrapper);

            List<MinerProductOrder> records = selectPage.getRecords();
            if (records.isEmpty()) {
                break;
            }

            HashSet<Long> minerProductsId = new HashSet<>();
            for (MinerProductOrder order : records) {
                minerProductsId.add(order.getProductId());
            }

            LambdaQueryWrapperX<MinerProduct> minerQueryWrapperX = new LambdaQueryWrapperX<>();
            minerQueryWrapperX.in(MinerProduct::getId, minerProductsId);
            List<MinerProduct> productList = minerProductMapper.selectList(minerQueryWrapperX);
            Map<Long, MinerProduct> productMap = productList.stream()
                    .collect(Collectors.toMap(MinerProduct::getId, Function.identity()));

            for (MinerProductOrder val : records) {
                MinerProduct product = productMap.get(val.getProductId());
                if (product == null) {
                    log.info("未获去到挖矿产品，产品ID{}订单号{}", val.getProductId(), val.getOrderNo());
                    break;
                }
                //发送利息
                processOrderIncome(val, product,true,false);
            }
            if (records.size() < pageSize) {
                break;
            }
            pageNum++;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @TenantIgnore
    public void minerOrderExpireJob() {
        //查看当前时间的订单
        LambdaQueryWrapperX<MinerProductOrder> queryWrapper = new LambdaQueryWrapperX<>();
        long timestampNow = DateUtils.getUnixTimestampNow();
        queryWrapper.eq(MinerProductOrder::getOrderStatus,1);
        queryWrapper.le(MinerProductOrder::getExpireTime,timestampNow);
        Long count = minerProductOrderMapper.selectCount(queryWrapper);
        if (count <= 0 ){
            log.info("暂无挖矿订单过期-当前时间{}", timestampNow);
            return;
        }

        int pageNum = 1;
        int pageSize = 100;

        while (true) {

            Page<MinerProductOrder> page = new Page<>(pageNum, pageSize);
            Page<MinerProductOrder> selectPage = minerProductOrderMapper.selectPage(page, queryWrapper);

            List<MinerProductOrder> records = selectPage.getRecords();
            if (records.isEmpty()) {
                break;
            }
            HashSet<Long> minerProductsId = new HashSet<>();
            for (MinerProductOrder order : records) {
                minerProductsId.add(order.getProductId());
            }

            LambdaQueryWrapperX<MinerProduct> minerQueryWrapperX = new LambdaQueryWrapperX<>();
            minerQueryWrapperX.in(MinerProduct::getId, minerProductsId);
            List<MinerProduct> productList = minerProductMapper.selectList(minerQueryWrapperX);
            Map<Long, MinerProduct> productMap = productList.stream()
                    .collect(Collectors.toMap(MinerProduct::getId, Function.identity()));

            for (MinerProductOrder val : records) {
                val.setOrderStatus(3);
                boolean needSendReward = val.getRewardDate() == null || !val.getRewardDate().equals(LocalDate.now());
                //判断是否发送利息+必须退回本金
                processOrderIncome(val, productMap.get(val.getProductId()),needSendReward,true);
            }

            if (records.size() < pageSize) {
                break;
            }
            pageNum++;
        }
    }

    private void processOrderIncome(MinerProductOrder order, MinerProduct product,boolean needSendIncome,boolean needBackPrincipal) {
        if (product == null) {
            log.warn("未找到对应产品，产品ID：{}，订单号：{}", order.getProductId(), order.getOrderNo());
            return;
        }

        // 生成随机收益率
        double randomDouble = ThreadLocalRandom.current().nextDouble(
                product.getMinProfit().doubleValue(),
                product.getMaxProfit().doubleValue()
        );
        BigDecimal randomRate = new BigDecimal(randomDouble).setScale(4, RoundingMode.DOWN);

        // 计算收益
        BigDecimal incomeAmount = randomRate.multiply(order.getOrderAmount());

        // 获取用户信息
        MemberUserRespDTO userDTO = memberUserApi.checkUserExists(order.getUserId());

        //发送利息
        if (needSendIncome){
            updateUserBalance(userDTO,incomeAmount,order.getOrderNo(),
                    MemberTransactionsTypeEnum.MINING_ORDER_INCOME,true);
        }

        //需要退回本金
        if (needBackPrincipal) {
            updateUserBalance(userDTO,order.getOrderAmount(),order.getOrderNo(),
                    MemberTransactionsTypeEnum.MINING_ORDER_PRINCIPAL_REFUND,true);
        }


        // 更新订单
        order.setOrderIncome(order.getOrderIncome().add(incomeAmount));
        order.setRewardDate(LocalDate.now());
        minerProductOrderMapper.updateById(order);

        // 保存收益记录
        MinerProductIncome incomeRecord = new MinerProductIncome();
        incomeRecord.setOrderNo(order.getOrderNo());
        incomeRecord.setUserId(order.getUserId());
        incomeRecord.setUsername(order.getUsername());
        incomeRecord.setIncome(incomeAmount);
        incomeRecord.setProductId(order.getProductId());
        incomeRecord.setPrincipal(BigDecimal.ZERO);
        if (needBackPrincipal){
            incomeRecord.setPrincipal(order.getOrderAmount());
        }
        minerProductIncomeMapper.insert(incomeRecord);
    }

     public void updateUserBalance(MemberUserRespDTO userDTO, BigDecimal amount,
                                   String bizOrderNo, MemberTransactionsTypeEnum transactionsTypeEnum,boolean t) {
        MemberBalanceUpdateReqVO balanceUpdate = new MemberBalanceUpdateReqVO();
        balanceUpdate.setUserId(userDTO.getId());
        balanceUpdate.setAmount(amount);

        MemberTransactionSaveReqVO transactionSave = new MemberTransactionSaveReqVO();
        transactionSave.setUserId(userDTO.getId());
        transactionSave.setTransactionType(transactionsTypeEnum);
        transactionSave.setRemark(transactionsTypeEnum.getLabel());
        transactionSave.setBizOrderNo(bizOrderNo);
        transactionSave.setAmountCurrency("USDT");
        transactionSave.setTenantId(userDTO.getTenantId());

        MemberBalanceUpdateContext context = new MemberBalanceUpdateContext(userDTO, balanceUpdate, transactionSave);
        if (t){//加钱
            memberBalanceApi.incrUserBalance(context);
        }else {//扣钱
            memberBalanceApi.decrUserBalance(context);
        }
    }

    private void fillOrderTitles(List<MinerProductOrderResp> orderList) {
        if (CollUtil.isEmpty(orderList)) return;

        List<Long> productIds = orderList.stream()
                .map(MinerProductOrderResp::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollUtil.isEmpty(productIds)) return;

        LambdaQueryWrapperX<MinerProduct> productWrapper = new LambdaQueryWrapperX<>();
        productWrapper.in(MinerProduct::getId, productIds);
        List<MinerProduct> products = minerProductMapper.selectList(productWrapper);

        Map<Long, MinerProduct> productMap = products.stream()
                .collect(Collectors.toMap(MinerProduct::getId, Function.identity()));

        for (MinerProductOrderResp respVo : orderList) {
            MinerProduct product = productMap.get(respVo.getProductId());
            if (product != null) {
                respVo.setTitle(product.getTitle());
            }
        }
    }



}
