package com.rf.exchange.module.system.controller.app.currencyrate;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppConfigRespVO;
import com.rf.exchange.module.system.service.appconfig.AppConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * APP/h5 汇率接口
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Tag(name = "用户APP - 汇率")
@RestController
@RequestMapping("/system/currency-rate")
public class AppCurrencyRateController {

    @Resource
    AppConfigService appConfigService;

    @Operation(summary = "获取APP的汇率接口")
    @GetMapping("/list")
    @PreAuthenticated
    public CommonResult<List<AppConfigRespVO.ExchangeRate>> list() {
        return success(appConfigService.getCurrencyRates(getTenantId()));
    }
}
