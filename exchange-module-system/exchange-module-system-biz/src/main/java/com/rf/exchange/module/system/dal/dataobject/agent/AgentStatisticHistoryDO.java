package com.rf.exchange.module.system.dal.dataobject.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 系统代理统计数据历史 实体
 *
 * <AUTHOR>
 * @since 2024-06-16
 */
@TableName(value = "system_agent_statistic_history", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class AgentStatisticHistoryDO extends AgentStatisticDO {
    /**
     * 统计日期
     */
    private Long statisticDate;
}
