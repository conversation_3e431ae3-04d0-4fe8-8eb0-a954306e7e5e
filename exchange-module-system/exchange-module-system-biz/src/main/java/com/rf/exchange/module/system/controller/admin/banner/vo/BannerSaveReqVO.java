package com.rf.exchange.module.system.controller.admin.banner.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.framework.i18n.annotation.I18n;
import com.rf.exchange.module.system.enums.banner.BannerLocationEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Schema(description = "管理后台 - banner新增/修改 Request VO")
@Data
public class BannerSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25704")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED,example = "test")
    @NotNull(message = "标题不能为空")
    @I18n
    private String title;

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "图片不能为空")
    private String img;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "链接路径 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.xxx.xx")
    //@NotEmpty(message = "链接路径 不能为空")
    private String linkUrl;

    @Schema(description = "链接类型，0内网，1外网", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    //@NotNull(message = "链接类型，0内网，1外网不能为空")
    //@InEnum(BannerLinkTypeEnum.class)
    private Integer linkType;

    @Schema(description = "位置:1左，2右", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Positive(message = "位置不能为空")
    @InEnum(BannerLocationEnum.class)
    private Integer location;

    @Schema(description = "交易对编码，可选，需要从租户的交易对列表获取", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private String tradeCode;
}