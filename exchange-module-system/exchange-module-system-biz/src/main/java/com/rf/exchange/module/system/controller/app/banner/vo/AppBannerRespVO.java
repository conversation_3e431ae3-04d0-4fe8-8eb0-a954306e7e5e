package com.rf.exchange.module.system.controller.app.banner.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.framework.i18n.annotation.I18n;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Schema(description = "用户 App - BANNER")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppBannerRespVO {

    @Schema(description = "banner标题")
    @I18n
    private String title;
    @Schema(description = "图片")
    @ImageUrl
    private String img;
    @Schema(description = "链接路径")
    private String linkUrl;
    @Schema(description = "链接类型，1内部，2外部")
    private Integer linkType;
    @Schema(description = "位置:1左，2右")
    private Short location;

    @Schema(description = "交易对编码")
    private String tradeCode;
    @Schema(description = "交易对名称")
    private String tradeName;
    /**
     * 当前价格
     */
    @Schema(description = "交易对价格")
    private BigDecimal currentPrice;
    /**
     * 涨幅百分比
     */
    @Schema(description = "交易对涨幅")
    private String percentage;

    @Schema(description = "排序")
    private Integer sort;
}
