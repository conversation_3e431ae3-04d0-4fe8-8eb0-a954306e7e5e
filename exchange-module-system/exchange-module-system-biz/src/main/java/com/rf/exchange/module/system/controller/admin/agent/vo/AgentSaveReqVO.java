package com.rf.exchange.module.system.controller.admin.agent.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @since 2024-06-08
 */
@Schema(description = "管理后台 - 代理创建/修改 Request VO")
@Data
public class AgentSaveReqVO {

    @Schema(description = "代理id", example = "1024")
    private Long id;

    @Schema(description = "代理名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @Size(max = 20, message = "代理名称长度不能超过20个字符")
    @DiffLogField(name = "代理名称")
    private String name;

    @Schema(description = "邀请码", example = "aabb")
    @DiffLogField(name = "邀请码")
    private String code;

    @Schema(description = "备注", example = "这是一个备注")
    @DiffLogField(name = "备注")
    private String remark;

    @Schema(description = "上级代理id", example = "")
    @NotNull(message = "请选择上级ID")
    private Long ancestorId;

    @Schema(description = "状态,0正常，1禁用", example = "")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 是否是租户的默认根代理
     * true:是
     */
    @JsonIgnore
    private boolean first;

    // ========== 仅【创建】时，需要传递的字段 ==========
    // TODO: 需要根据传递的这两个字段去系统用户表中创建系统管理员
    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "exchange")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 6, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    private String password;
}
