package com.rf.exchange.module.system.controller.admin.tenant.vo.servername;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 系统租户域名新增/修改 Request VO")
@Data
public class TenantServerNameSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10502")
    @NotNull(message = "租户id不能为空")
    private Long tenId;

    @Schema(description = "域名", requiredMode = Schema.RequiredMode.REQUIRED, example = "www.google.com")
    @NotEmpty(message = "域名不能为空")
    private String serverName;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "是否可以作为分享链接的域名 true:可以作为分享域名 false:不作为分享链接域名")
    private Boolean isShare = true;
}