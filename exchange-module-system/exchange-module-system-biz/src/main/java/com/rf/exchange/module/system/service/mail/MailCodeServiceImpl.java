package com.rf.exchange.module.system.service.mail;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.system.api.mail.dto.MailCodeSendReqDTO;
import com.rf.exchange.module.system.api.mail.dto.MailCodeUseReqDTO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailCodeDO;
import com.rf.exchange.module.system.dal.mysql.mail.MailCodeMapper;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import com.rf.exchange.module.system.framework.mail.config.MailCodeProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.hutool.core.util.RandomUtil.randomInt;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.util.date.DateUtils.isToday;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @since 2024-06-07
 */
@Slf4j
@Service
@Validated
public class MailCodeServiceImpl implements MailCodeService {
    @Resource
    private MailCodeMapper mailCodeMapper;
    @Resource
    private MailCodeProperties mailCodeProperties;
    @Resource
    private MailSendService mailSendService;

    @Override
    public void sendMailCode(MailCodeSendReqDTO reqDTO) {
        MailSceneEnum sceneEnum = MailSceneEnum.getCodeByScene(reqDTO.getScene());
        Assert.notNull(sceneEnum, "验证码场景({}) 查找不到配置", reqDTO.getScene());
        // 创建验证码
        String code = createMailCode(reqDTO.getEmail(), reqDTO.getScene(), reqDTO.getCreateIp());
        try {
            // 发送验证码
            mailSendService.sendSingleMail(reqDTO.getEmail(), reqDTO.getLang(), reqDTO.getToUserId(),
                    reqDTO.getToUserType(), reqDTO.getTenantId(), sceneEnum.getTemplateCode(), reqDTO.getDomain(), MapUtil.of("code", code));
        } catch (Exception e) {
            log.error("发送邮件出现异常: {}", e.getMessage());
            throw exception(Mail_CODE_SEND_FAIL);
        }
    }

    private String createMailCode(String mail, Integer scene, String ip) {
        // 校验是否可以发送验证码，不用筛选场景
        MailCodeDO lastMailCode = mailCodeMapper.selectLastByMail(mail, null, null);
        if (lastMailCode != null) {
            long duration = DateUtils.getUnixTimestampNow() - lastMailCode.getCreateTime();
            if (duration < mailCodeProperties.getSendFrequency().toMillis()) { // 发送过于频繁
                throw exception(MAIL_CODE_SEND_TOO_FAST);
            }
            if (isToday(lastMailCode.getCreateTime()) && // 必须是今天，才能计算超过当天的上限
                    lastMailCode.getTodayIndex() >= mailCodeProperties.getSendMaximumQuantityPerDay()) { // 超过当天发送的上限。
                throw exception(MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY);
            }
            // TODO: 提升，每个 IP 每天可发送数量
            // TODO: 提升，每个 IP 每小时可发送数量
        }
        // 创建验证码记录
        String code = String.valueOf(randomInt(mailCodeProperties.getBeginCode(), mailCodeProperties.getEndCode() + 1));

        MailCodeDO mailCodeDO = MailCodeDO.builder().mail(mail).code(code).scene(scene)
                .todayIndex(lastMailCode != null && isToday(lastMailCode.getCreateTime()) ? lastMailCode.getTodayIndex() + 1 : 1)
                .createIp(ip).used(false).build();
        mailCodeMapper.insert(mailCodeDO);
        return code;
    }

    @Override
    public MailCodeDO validateMailCode(MailCodeUseReqDTO reqDTO) {
        return validateMailCode(reqDTO.getEmail(), reqDTO.getCode(), reqDTO.getScene());
    }

    private MailCodeDO validateMailCode(String mail, String code, Integer scene) {
        // 校验验证码
        MailCodeDO lastMailCode = mailCodeMapper.selectLastByMail(mail, code, scene);
        // 若验证码不存在，抛出异常
        if (lastMailCode == null) {
            throw exception(MAIL_CODE_NOT_FOUND);
        }
        // 超过时间
        long duration = DateUtils.getUnixTimestampNow() - lastMailCode.getCreateTime();
        if (duration >= mailCodeProperties.getExpireTimes().toMillis()) { // 验证码已过期
            throw exception(MAIL_CODE_EXPIRED);
        }
        // 判断验证码是否已被使用
        if (Boolean.TRUE.equals(lastMailCode.getUsed())) {
            throw exception(MAIL_CODE_USED);
        }
        return lastMailCode;
    }

    @Override
    public void useMailCode(MailCodeUseReqDTO reqDTO) {
        // 检测验证码是否有效
        MailCodeDO lastMailCode = validateMailCode(reqDTO);
        lastMailCode.setUsed(true).setUsedTime(DateUtils.getUnixTimestampNow()).setUsedIp(reqDTO.getUsedIp());
        // 使用验证码
        mailCodeMapper.updateById(lastMailCode);
    }
}
