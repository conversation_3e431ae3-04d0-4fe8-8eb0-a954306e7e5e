package com.rf.exchange.module.system.dal.mysql.lang;

import java.util.*;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.system.controller.admin.lang.vo.*;
import org.apache.ibatis.annotations.Select;

/**
 * 系统语种配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LangMapper extends BaseMapperX<LangDO> {

    default PageResult<LangDO> selectPage(LangPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LangDO>()
                .eqIfPresent(LangDO::getCode, reqVO.getCode())
                .likeIfPresent(LangDO::getName, reqVO.getName())
                .eqIfPresent(LangDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LangDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LangDO::getId));
    }

    @Select("select code from system_lang")
    Set<String> getLangCodeList();


}