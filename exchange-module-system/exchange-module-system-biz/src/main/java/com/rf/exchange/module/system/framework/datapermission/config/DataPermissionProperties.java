package com.rf.exchange.module.system.framework.datapermission.config;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@Validated
@ConfigurationProperties(prefix = "exchange.system.agent-data-permission")
public class DataPermissionProperties {

    @NotNull(message = "enable不能为空")
    private Boolean enable;

    /**
     * 按照 user_id 匹配的表
     */
    private List<String> matchUserIdTables = Collections.emptyList();
    /**
     * 按照 agent_id 字段匹配的表
     */
    private List<String> matchAgentIdTables = Collections.emptyList();

    @Data
    public static class CustomColumnTable {

        @NotEmpty(message = "tableName不能为空")
        private String tableName;

        @NotEmpty(message = "columnName不能为空")
        private String columnName;

        @NotNull(message = "op不能为空")
        private String op;
    }
}
