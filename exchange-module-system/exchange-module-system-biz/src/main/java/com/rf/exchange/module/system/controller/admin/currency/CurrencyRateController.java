package com.rf.exchange.module.system.controller.admin.currency;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import java.math.BigDecimal;
import java.util.Collection;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

@Tag(name = "管理后台 - 系统货币汇率")
@RestController
@RequestMapping("/system/currencyRate")
@Validated
public class CurrencyRateController {

    @Resource
    private CurrencyRateApi currencyRateApi;

    @GetMapping("/getAll")
    @Operation(summary = "获取汇率列表")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('system:currencyRate:queryAll')")
    public CommonResult<Collection<CurrencyRateDTO>>
        queryTenantAllCurrencyRate(@RequestParam("tenantId") Long tenantId) {
        return success(currencyRateApi.getTenantCurrencyRateList(tenantId));
    }

    @GetMapping("/get")
    @Operation(summary = "获取指定货币汇率")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1024")
    @Parameter(name = "currencyCode", description = "货币", required = true, example = "JPY")
    // @PreAuthorize("@ss.hasPermission('system:currencyRate:query')")
    public CommonResult<BigDecimal> queryTenantCurrencyRate(@RequestParam(value = "tenantId") Long tenantId,
        @RequestParam("currencyCode") String currencyCode) {
        return success(currencyRateApi.getTenantCurrencyRateToUSD(tenantId, currencyCode));
    }

}