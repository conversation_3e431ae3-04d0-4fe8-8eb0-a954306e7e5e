package com.rf.exchange.module.system.controller.admin.tenant.vo.data;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 租户字典数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantDictDataPageReqVO extends PageParam {

    @Schema(description = "标签名")
    private String label;

    @Schema(description = "标签值")
    private String value;

    @Schema(description = "类型", example = "2")
    private String dictType;

    @Schema(description = "排序")
    private Short sort;

    @Schema(description = "状态", example = "1")
    private Short status;

    @Schema(description = "颜色类型", example = "2")
    private String colorType;

    @Schema(description = "样式")
    private String cssClass;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;
@Schema(description = "租户id")
    private Long tenantId;
}