package com.rf.exchange.module.system.dal.redis.currencyrate;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.monitor.TracerUtils;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.system.dal.redis.RedisKeyConstants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-07-11
 */
@Slf4j
@Repository
public class CurrencyRateRedisDAO {

    private static final String KEY = RedisKeyConstants.SYS_CURRENCY_RATE + ":all";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存汇率信息
     *
     * @param dto 汇率信息
     */
    public void set(CurrencyRateDTO dto) {
        String valueKey = formatValueKey(dto.getBaseCurrency(), dto.getQuoteCurrency());
        String json = JsonUtils.toJsonString(dto);
        redisTemplate.opsForHash().put(KEY, valueKey, json);
    }

    /**
     * 缓存汇率信息
     *
     * @param dtos 汇率信息列表
     */
    public void set(Collection<CurrencyRateDTO> dtos) {
        Map<String, String> values = new HashMap<>();
        for (CurrencyRateDTO dto : dtos) {
            String valueKey = formatValueKey(dto.getBaseCurrency(), dto.getQuoteCurrency());
            String json = JsonUtils.toJsonString(dto);
            values.put(valueKey, json);
        }
        if (CollectionUtil.isNotEmpty(values)) {
            redisTemplate.opsForHash().putAll(KEY, values);
        }
    }

    /**
     * 获取单一的汇率信息
     *
     * @param baseCurrency  基础币种
     * @param quoteCurrency 报价币种
     * @return 汇率信息
     */
    public CurrencyRateDTO get(String baseCurrency, String quoteCurrency) {
        String valueKey = formatValueKey(baseCurrency, quoteCurrency);
        Object json = redisTemplate.opsForHash().get(KEY, valueKey);
        if (json != null) {
            return parseJson((String) json);
        }
        return null;
    }

    /**
     * 获取所有的汇率信息
     *
     * @return 格式
     */
    public Map<String, CurrencyRateDTO> getAll() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(KEY);
        Map<String, CurrencyRateDTO> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            CurrencyRateDTO dto = parseJson((String) entry.getValue());
            if (dto != null) {
                result.put((String) entry.getKey(), dto);
            }
        }
        return result;
    }

    private static String formatValueKey(String baseCurrency, String quoteCurrency) {
        return baseCurrency + "/" + quoteCurrency;
    }

    private CurrencyRateDTO parseJson(String jsonStr) {
        try {
            return JsonUtils.parseObject(jsonStr, CurrencyRateDTO.class);
        } catch (RuntimeException e) {
            log.error("解析redis中的交易对价格信息异常 {} traceId:{}", e.getMessage(), TracerUtils.getTraceId());
        }
        return null;
    }
}
