package com.rf.exchange.module.system.service.miner;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductIncomePageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductOrderResp;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductCreateOrderReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductInfoReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerRedemptionOrderReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductIncome;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;

import java.math.BigDecimal;

public interface MinerService {

    /**
     * 创建挖矿订单
     * @param reqVO 下单参数
     * @return boolean
     */
    Boolean createOrder(MinerProductCreateOrderReqVo reqVO);

    /**
     * 赎回挖矿订单
     * @param reqVO 下单参数
     * @return boolean
     */
    Boolean redemptionOrder(MinerRedemptionOrderReqVo reqVO);


    /**
     * 创建挖矿产品
     * @param reqVO reqVO
     * @return boolean
     */
    Boolean createMinerProduct(MinerProductReqVO reqVO);

    /**
     * 编辑挖矿产品
     * @param reqVO reqVO
     * @return boolean
     */
    Boolean updateMinerProduct(MinerProductReqVO reqVO);

    /**
     *
     * @param id 数据ID
     * @return boolean
     */
    Boolean deleteMinerProduct(Long id);

    /**
     * 获取挖矿产品-分页
     *
     * @param pageReqVO 分页参数
     * @return 分页信息
     */
    PageResult<MinerProduct> getMinerPage(MinerProductPageReqVO pageReqVO);

    /**
     * 获取挖矿产品-详情
     *
     * @param reqVo 详情产品
     * @return 挖矿产品
     */
    MinerProduct getMinerInfo(MinerProductInfoReqVo reqVo);


    /**
     * 获取挖矿订单-分页
     *
     * @param pageReqVO 分页参数
     * @return 分页信息
     */
    PageResult<MinerProductOrderResp> appMinerPageOrders(MinerOrderPageReqVo pageReqVO);
    PageResult<MinerProductOrder> getMinerPageOrders(MinerOrderPageReqVo pageReqVO);

    /**
     * 获取挖矿订单-分页
     *
     * @param pageReqVO 分页参数
     * @return 分页信息
     */
    PageResult<MinerProductIncome> getMinerPageIncome(MinerProductIncomePageReqVO pageReqVO);
    PageResult<MinerProductIncome> appMinerPageIncome(MinerProductIncomePageReqVO pageReqVO);


    void updateUserBalance(MemberUserRespDTO userDTO, BigDecimal amount,
                         String bizOrderNo, MemberTransactionsTypeEnum transactionsTypeEnum, boolean t);


    /**
     * 定时发送订单利息
     */
    void minerOrderIncomeJob();

    /**
     * 定时订单到期扫描
     */
    void minerOrderExpireJob();

}
