package com.rf.exchange.module.system.dal.redis.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONNull;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

import static com.rf.exchange.module.system.dal.redis.RedisKeyConstants.MAIL_ACCOUNT;

/**
 * MailAccount的redis缓存
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Repository
public class MailAccountRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public void set(Long tenantId, MailAccountDO accountDO) {
        if (accountDO == null) {
            return;
        }
        String json = JsonUtils.toJsonString(accountDO);
        redisTemplate.opsForHash().put(formatKey(tenantId), accountDO.getMail(), json);
    }

    public void setAll(Long tenantId, List<MailAccountDO> accountDOList) {
        if (CollectionUtil.isEmpty(accountDOList)) {
            return;
        }
        Map<String, String> jsonMap = new HashMap<>(accountDOList.size());
        for (MailAccountDO accountDO : accountDOList) {
            final String json = JsonUtils.toJsonString(accountDO);
            jsonMap.put(accountDO.getMail(), json);
        }
        redisTemplate.opsForHash().putAll(formatKey(tenantId), jsonMap);
    }

    public List<MailAccountDO> getAll(Long tenantId) {
        final Map<Object, Object> entries = redisTemplate.opsForHash().entries(formatKey(tenantId));
        if (CollectionUtil.isEmpty(entries)) {
            return Collections.emptyList();
        }
        ArrayList<MailAccountDO> mailAccountList = new ArrayList<>(entries.size());
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            final MailAccountDO account = JsonUtils.parseObject((String) entry.getValue(), MailAccountDO.class);
            mailAccountList.add(account);
        }
        return mailAccountList;
    }

    public void delete(Long tenantId) {
        redisTemplate.delete(formatKey(tenantId));
    }

    private String formatKey(Long tenantId) {
        return MAIL_ACCOUNT + ":" + tenantId;
    }
}
