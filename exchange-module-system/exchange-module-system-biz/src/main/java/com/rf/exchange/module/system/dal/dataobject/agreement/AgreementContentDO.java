package com.rf.exchange.module.system.dal.dataobject.agreement;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 协议多语言内容 DO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@TableName("system_agreement_content")
@KeySequence("system_agreement_content_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgreementContentDO extends BaseDO implements Serializable, TransPojo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 协议ID，关联system_agreement表
     */
    private Long agreementId;
    
    /**
     * 语言代码，如zh-CN, en, ja, ko等
     */
    private String languageCode;
    
    /**
     * 协议标题
     */
    private String title;
    
    /**
     * 协议内容（HTML格式）
     */
    private String content;

    // ========== 便利方法 ==========

    /**
     * 是否为中文
     */
    public boolean isChineseLanguage() {
        return "zh-CN".equals(this.languageCode) || "zh".equals(this.languageCode);
    }

    /**
     * 是否为英文
     */
    public boolean isEnglishLanguage() {
        return "en".equals(this.languageCode) || "en-US".equals(this.languageCode);
    }

    /**
     * 是否为日文
     */
    public boolean isJapaneseLanguage() {
        return "ja".equals(this.languageCode) || "ja-JP".equals(this.languageCode);
    }

    /**
     * 是否为韩文
     */
    public boolean isKoreanLanguage() {
        return "ko".equals(this.languageCode) || "ko-KR".equals(this.languageCode);
    }

    /**
     * 获取语言显示名称
     */
    public String getLanguageDisplayName() {
        return switch (this.languageCode) {
            case "zh-CN", "zh" -> "简体中文";
            case "zh-TW" -> "繁体中文";
            case "en", "en-US" -> "English";
            case "ja", "ja-JP" -> "日本語";
            case "ko", "ko-KR" -> "한국어";
            case "es" -> "Español";
            case "fr" -> "Français";
            case "de" -> "Deutsch";
            case "ru" -> "Русский";
            case "ar" -> "العربية";
            case "pt" -> "Português";
            case "it" -> "Italiano";
            case "th" -> "ไทย";
            case "vi" -> "Tiếng Việt";
            case "id" -> "Bahasa Indonesia";
            case "ms" -> "Bahasa Melayu";
            case "hi" -> "हिन्दी";
            default -> this.languageCode;
        };
    }
}
