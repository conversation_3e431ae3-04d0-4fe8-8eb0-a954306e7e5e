package com.rf.exchange.module.system.service.mail;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.mail.vo.log.MailLogPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailLogDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailLogDetailDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailTemplateDO;
import com.rf.exchange.module.system.dal.mysql.mail.MailLogDetailMapper;
import com.rf.exchange.module.system.dal.mysql.mail.MailLogMapper;
import com.rf.exchange.module.system.enums.mail.MailSendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.exceptions.ExceptionUtil.getRootCauseMessage;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 邮件日志 Service 实现类
 *
 * @since 2022-03-21
 */
@Slf4j
@Service
@Validated
public class MailLogServiceImpl implements MailLogService {

    @Resource
    private MailLogMapper mailLogMapper;
    @Resource
    private MailLogDetailMapper mailLogDetailMapper;

    @Override
    public PageResult<MailLogDO> getMailLogPage(MailLogPageReqVO pageVO) {
        return mailLogMapper.selectPage(pageVO);
    }

    @Override
    public MailLogDO getMailLog(Long id) {
        final MailLogDO mailLogDO = mailLogMapper.selectById(id);
        if (Objects.isNull(mailLogDO)) {
            throw exception(GlobalErrorCodeConstants.NOT_FOUND);
        }
        return mailLogDO;
    }

    @Override
    public List<MailLogDetailDO> getMailDetail(Long id) {
        LambdaQueryWrapperX<MailLogDetailDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MailLogDetailDO::getLogId, id);
        return mailLogDetailMapper.selectList(wrapper);
    }

    @Override
    public Long createMailLog(Long tenantId, Long userId, Integer userType, String toMail,
                              MailAccountDO account, MailTemplateDO template,
                              String templateContent, Map<String, Object> templateParams, Boolean isSend) {
        MailLogDO.MailLogDOBuilder logDOBuilder = MailLogDO.builder();
        // 根据是否要发送，设置状态
        logDOBuilder.sendStatus(Objects.equals(isSend, true) ? MailSendStatusEnum.INIT.getStatus() : MailSendStatusEnum.IGNORE.getStatus())
                .tenantId(tenantId)
                // 用户信息
                .userId(userId).userType(userType).toMail(toMail)
                .accountId(account.getId()).fromMail(account.getMail())
                // 模板相关字段
                .templateId(template.getId()).templateCode(template.getCode())
                .templateTitle(template.getTitle()).templateContent(templateContent).templateParams(templateParams);

        // 插入数据库
        MailLogDO logDO = logDOBuilder.build();
        mailLogMapper.insert(logDO);
        return logDO.getId();
    }

    @Override
    public void updateMailSendResult(Long logId, String messageId, Exception exception) {
        // 1. 成功
        if (exception == null) {
            mailLogMapper.updateById(new MailLogDO().setId(logId).setSendTime(DateUtils.getUnixTimestampNow())
                    .setSendStatus(MailSendStatusEnum.SENT.getStatus()).setSendMessageId(messageId));
            return;
        }
        // 2. 失败
        mailLogMapper.updateById(new MailLogDO().setId(logId).setSendTime(DateUtils.getUnixTimestampNow())
                .setSendStatus(MailSendStatusEnum.FAILURE.getStatus()).
                setSendException(getRootCauseMessage(exception)));

    }

    @Override
    public void updateMailSendWebhookResult(String messageId, Integer status, String failReason) {
        final MailLogDO mailLogDO = mailLogMapper.selectByMessageId(messageId);
        if (Objects.isNull(mailLogDO)) {
            return;
        }
        mailLogDO.setSendStatus(status);
        mailLogDO.setSendException(StrUtil.emptyIfNull(failReason));
        mailLogDO.setUpdater("webhook");
        mailLogMapper.updateById(mailLogDO);
    }

    @Override
    public void createMailDetailLog(MailLogDetailDO mailLogDetailDO) {
        MailLogDO logDO = mailLogMapper.selectByMessageId(mailLogDetailDO.getMessageId());
        if (logDO == null) {
            log.error("邮件发送日志不存在 messageId:{}", mailLogDetailDO.getMessageId());
            return;
        }
        mailLogDetailDO.setLogId(logDO.getId());
        mailLogDetailMapper.insert(mailLogDetailDO);
    }
}
