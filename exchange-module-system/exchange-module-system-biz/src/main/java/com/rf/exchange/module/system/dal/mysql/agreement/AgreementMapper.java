package com.rf.exchange.module.system.dal.mysql.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 协议管理 Mapper
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Mapper
public interface AgreementMapper extends BaseMapperX<AgreementDO> {

    default PageResult<AgreementDO> selectPage(AgreementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgreementDO>()
                .eqIfPresent(AgreementDO::getTenantId, reqVO.getTenantId())
                .eqIfPresent(AgreementDO::getType, reqVO.getType())
                .eqIfPresent(AgreementDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AgreementDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AgreementDO::getId));
    }

    default AgreementDO selectByTenantIdAndType(Long tenantId, Integer type) {
        return selectOne(new LambdaQueryWrapperX<AgreementDO>()
                .eq(AgreementDO::getTenantId, tenantId)
                .eq(AgreementDO::getType, type)
                .eq(AgreementDO::getStatus, 1) // 只查询启用状态的协议
                .orderByDesc(AgreementDO::getId)
                .last("LIMIT 1"));
    }

    default AgreementDO selectByTenantIdAndTypeForValidation(Long tenantId, Integer type) {
        return selectOne(new LambdaQueryWrapperX<AgreementDO>()
                .eq(AgreementDO::getTenantId, tenantId)
                .eq(AgreementDO::getType, type)
                .orderByDesc(AgreementDO::getId)
                .last("LIMIT 1"));
    }

    default AgreementDO selectByTenantIdAndTitle(Long tenantId, String title) {
        return selectOne(new LambdaQueryWrapperX<AgreementDO>()
                .eq(AgreementDO::getTenantId, tenantId)
                .last("LIMIT 1"));
    }
}
