package com.rf.exchange.module.system.controller.admin.agreement.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 协议多语言内容分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "管理后台 - 协议多语言内容分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgreementContentPageReqVO extends PageParam {

    @Schema(description = "协议ID", example = "1")
    private Long agreementId;

    @Schema(description = "语言代码", example = "zh-CN")
    private String languageCode;

    @Schema(description = "协议标题", example = "隐私协议")
    private String title;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
