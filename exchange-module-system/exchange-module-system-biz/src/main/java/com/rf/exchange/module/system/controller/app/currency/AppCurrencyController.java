package com.rf.exchange.module.system.controller.app.currency;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.system.controller.app.currency.vo.AppCurrencyRespVO;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import com.rf.exchange.module.system.service.currency.CurrencyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 获取币种列表")
@RestController
@RequestMapping("/system/currency")
@Validated
public class AppCurrencyController {

    @Resource
    private CurrencyService currencyService;

    @Operation(summary = "获取币种")
    @GetMapping("/list")
    @PreAuthenticated
    public CommonResult<List<AppCurrencyRespVO>> getList() {
        List<CurrencyDO> list=currencyService.getCurrencyList();
        List<AppCurrencyRespVO> result = BeanUtils.toBean(list, AppCurrencyRespVO.class);
        return success(result);
    }
}
