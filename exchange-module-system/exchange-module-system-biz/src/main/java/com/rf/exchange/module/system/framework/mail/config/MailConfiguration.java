package com.rf.exchange.module.system.framework.mail.config;

import com.rf.exchange.module.system.framework.mail.core.client.MailClientFactory;
import com.rf.exchange.module.system.framework.mail.core.client.imp.MailClientFactoryImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类，包括短信客户端、短信验证码两部分
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(MailCodeProperties.class)
public class MailConfiguration {

    @Bean
    public MailClientFactory MailClientFactory() {
        return new MailClientFactoryImpl();
    }

}
