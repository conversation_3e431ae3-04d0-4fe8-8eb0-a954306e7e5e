package com.rf.exchange.module.system.dal.dataobject.lang;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统语种配置 DO
 *
 * <AUTHOR>
 */
@TableName("system_lang")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LangDO extends BaseDO {
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 编码
     */
    private String code;
    /**
     * 本地华语言名称
     */
    private String localName;
    /**
     * 名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 状态
     */
    private Short status;

    /**
     * 图标
     */
    private String ico;
}