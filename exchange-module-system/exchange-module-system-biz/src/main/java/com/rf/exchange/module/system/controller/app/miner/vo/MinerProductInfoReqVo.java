package com.rf.exchange.module.system.controller.app.miner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 挖矿产品创建-编辑参数")
@Data
public class MinerProductInfoReqVo {

    @Schema(description = "挖矿产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "挖矿产品id不能为空")
    private Long id;


}
