package com.rf.exchange.module.system.controller.admin.tenant.vo.tenant;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.common.validation.CharAndNumber;
import com.rf.exchange.framework.common.validation.ImageUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 租户创建/修改 Request VO")
@Data
public class TenantSaveReqVO {

    @Schema(description = "租户编号", example = "1024")
    private Long id;

    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "tenant01")
    @NotNull(message = "租户名不能为空")
    @CharAndNumber(message = "租户编码必须是字母加数字")
    private String code;

    @Schema(description = "租户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "新租户")
    @NotNull(message = "租户名不能为空")
    private String name;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "David")
    @NotNull(message = "联系人不能为空")
    private String contactName;

    @Schema(description = "联系手机", example = "15601691300")
    private String contactMobile;

    @Schema(description = "租户状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户状态")
    private Integer status;

//    @Schema(description = "租户套餐编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
//    @NotNull(message = "租户套餐编号不能为空")
//    private Long packageId;

    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "过期时间不能为空")
    private Long expireTime;

    @Schema(description = "账号数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "账号数量不能为空")
    private Integer accountCount;

    // ========== 仅【创建】时，需要传递的字段 ==========
    // TODO: 需要根据传递的这两个字段去系统用户表中创建系统管理员
    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "exchange")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 6, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
    private String password;


    @Schema(description = "租户logo", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @ImageUrl(message = "租户logo路径不正确")
    @NotEmpty(message = "请上传LOGO")
    private String logo;

    @Schema(description = "APP域名", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    //@NotEmpty(message = "请输入默认域名")
    private String frontUrl;

    @AssertTrue(message = "用户账号、密码不能为空")
    @JsonIgnore
    public boolean isUsernameValid() {
        return id != null // 修改时，不需要传递
                || (ObjectUtil.isAllNotEmpty(username, password)); // 新增时，必须都传递 username、password
    }

}
