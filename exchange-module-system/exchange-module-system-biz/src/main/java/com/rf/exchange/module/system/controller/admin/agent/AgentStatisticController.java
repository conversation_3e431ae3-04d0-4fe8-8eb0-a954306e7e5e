package com.rf.exchange.module.system.controller.admin.agent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentRespVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticPageReqVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticRespVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentStatisticDO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.agent.AgentStatisticService;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 代理统计")
@RestController
@RequestMapping("/system/agent/statistic")
@Validated
public class AgentStatisticController {

    @Resource
    private AgentStatisticService agentStatisticService;
    @PostMapping("/page")
    @Operation(summary = "代理统计")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<PageResult<AgentStatisticRespVO>> getList(AgentStatisticPageReqVO reqVO){
        PageResult<AgentStatisticDO> pageResult = agentStatisticService.getAgentPage(reqVO);
        return success(BeanUtils.toBean(pageResult, AgentStatisticRespVO.class));
    }
}
