package com.rf.exchange.module.system.controller.admin.auth;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.common.util.collection.CollectionUtils.convertSet;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.security.config.SecurityProperties;
import com.rf.exchange.framework.security.core.annotations.AuthCode;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.controller.admin.auth.vo.*;
import com.rf.exchange.module.system.convert.auth.AuthConvert;
import com.rf.exchange.module.system.dal.dataobject.permission.MenuDO;
import com.rf.exchange.module.system.dal.dataobject.permission.RoleDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.module.system.enums.logger.LoginLogTypeEnum;
import com.rf.exchange.module.system.service.auth.AdminAuthService;
import com.rf.exchange.module.system.service.permission.MenuService;
import com.rf.exchange.module.system.service.permission.PermissionService;
import com.rf.exchange.module.system.service.permission.RoleService;
import com.rf.exchange.module.system.service.user.AdminUserService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;

    @Resource
    private SecurityProperties securityProperties;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    @AuthCode
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        // FIXME 这里暂时不用谷歌验证
        // String secret = userService.getSecretByUsername(reqVO.getUsername());
        // if (!StringUtils.hasText(secret)) {
        // return error(GOOGLE_AUTH_NOT_BIND);
        // }
        // boolean suc = Authenticator.checkCode(secret, reqVO.getAuthCode(), System.currentTimeMillis());
        // if (!suc) {
        // return error(GOOGLE_AUTH_CODE_ERROR);
        // }
        AuthLoginRespVO resp = TenantUtils.executeIgnore(() -> authService.login(reqVO));
        return success(resp);
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader(),
            securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return success(null);
        }

        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollUtil.isEmpty(roleIds)) {
            return success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), Collections.emptyList()));
        }

        List<RoleDO> roles = roleService.getRoleList(roleIds);
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色
        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList.removeIf(menu -> !CommonStatusEnum.ENABLE.getStatus().equals(menu.getStatus())); // 移除禁用的菜单
        // 2. 拼接结果返回
        return success(AuthConvert.INSTANCE.convert(user, roles, menuList));
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "使用短信验证码登录")
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(reqVO);
        return success(true);
    }

    @PostMapping("/valid-binding-google-secret")
    @Operation(summary = "验证是否绑定谷歌密钥: 未绑定返回二维码绑定- 根据查询结果的绑定状态进下一步操作")
    @PermitAll
    public CommonResult<AuthValidBindingGoogleSecretRespVO>
        validBindingGoogleSecret(@RequestBody AuthValidBindingGoogleSecretReqVO req, HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        return success(authService.validBindingGoogleSecret(req.getName(), sessionId));
    }

    @PostMapping("/binding-google-secret")
    @Operation(summary = "绑定谷歌密钥 绑定异常如过期 重新调用getGoogleSecretAndValidBinding接口进行绑定")
    @PermitAll
    public CommonResult<Boolean> bindingGoogleSecret(@RequestBody @Valid AuthBindingGoogleSecretReqVO req) {
        return success(authService.bindingGoogleSecret(req.getAuthorization(), req.getGoogleCode()));
    }
}
