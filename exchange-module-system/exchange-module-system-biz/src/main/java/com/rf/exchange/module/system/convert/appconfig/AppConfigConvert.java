package com.rf.exchange.module.system.convert.appconfig;

import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.system.api.areainfo.dto.AreaInfoDTO;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import com.rf.exchange.module.system.api.lang.dto.LangRespDTO;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppConfigRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Mapper
public interface AppConfigConvert {

    AppConfigConvert INSTANCE = Mappers.getMapper(AppConfigConvert.class);

    AppConfigRespVO.Country convert(AreaInfoDTO infoDTO);

    AppConfigRespVO.Currency convert2(CurrencyBaseRespDTO dto);

    AppConfigRespVO.Language convert3(LangRespDTO dto);

    AppConfigRespVO.DefaultTradePair convert4(TradePairRespDTO dto);

    default AppConfigRespVO.TradeAssetType convert4(TenantTradeAssetTypeRespDTO dto) {
        AppConfigRespVO.TradeAssetType tradeAssetType = new AppConfigRespVO.TradeAssetType();
        tradeAssetType.setType(dto.getAssetType());
        tradeAssetType.setSort(dto.getSort());
        tradeAssetType.setNameI18n(dto.getNameI18n());
        return tradeAssetType;
    }

    List<AppConfigRespVO.Country> convertList(List<AreaInfoDTO> dtoList);

    List<AppConfigRespVO.Currency> convertList2(List<CurrencyBaseRespDTO> dtoList);

    List<AppConfigRespVO.Language> convertList3(List<LangRespDTO> dtoList);

    List<AppConfigRespVO.TradeAssetType> convertList4(List<TenantTradeAssetTypeRespDTO> dtoList);
}
