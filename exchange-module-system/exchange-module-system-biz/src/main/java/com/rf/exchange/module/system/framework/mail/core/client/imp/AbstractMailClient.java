package com.rf.exchange.module.system.framework.mail.core.client.imp;

import com.rf.exchange.framework.common.core.KeyValue;
import com.rf.exchange.module.system.framework.sms.core.client.dto.SmsReceiveRespDTO;
import com.rf.exchange.module.system.framework.sms.core.client.dto.SmsSendRespDTO;
import com.rf.exchange.module.system.framework.sms.core.client.dto.SmsTemplateRespDTO;

import java.util.List;

public abstract class AbstractMailClient {
//FIXME 邮件客户端抽像类
}
