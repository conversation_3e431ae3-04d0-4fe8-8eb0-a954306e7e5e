package com.rf.exchange.module.system.dal.redis.tenant;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import com.rf.exchange.module.system.dal.redis.RedisKeyConstants;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
@Repository
public class TenantServerNameRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取所有缓存的租户域名列表
     *
     * @return 域名列表
     */
    public List<TenantServerNameDO> get() {
        String redisKey = formatKey();
        return JsonUtils.parseArray(stringRedisTemplate.opsForValue().get(redisKey), TenantServerNameDO.class);
    }

    /**
     * 缓存租户的域名列表
     *
     * @param list 域名列表
     */
    public void set(List<TenantServerNameDO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        String redisKey = formatKey();
        String jsonString = JsonUtils.toJsonString(list);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString);
    }

    /**
     * 删除缓存
     */
    public void delete() {
        stringRedisTemplate.delete(formatKey());
    }

    private static String formatKey() {
        return RedisKeyConstants.TENANT_KEY_SERVERNAME + ":all";
    }
}
