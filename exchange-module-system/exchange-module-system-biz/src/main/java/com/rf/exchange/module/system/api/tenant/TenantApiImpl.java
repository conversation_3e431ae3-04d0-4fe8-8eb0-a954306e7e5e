package com.rf.exchange.module.system.api.tenant;

import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;
import com.rf.exchange.module.system.convert.tenant.TenantConvert;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import com.rf.exchange.module.system.service.tenant.TenantServerNameService;
import com.rf.exchange.module.system.service.tenant.TenantService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 多租户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantServerNameService tenantServerNameService;

    @Override
    public List<Long> getTenantIdList() {
        return tenantService.getTenantIdList();
    }

    @Override
    public List<TenantRespDTO> getTenantList() {
        List<TenantDO> tenantList = tenantService.getTenantList();
        return TenantConvert.INSTANCE.convertList(tenantList);
    }

    @Override
    public void validateTenant(Long id) {
        tenantService.validTenant(id);
    }

    @Override
    public Map<String, Long> getTenantIdServerNameMap() {
        return tenantServerNameService.getServerNameCachedMap();
    }
}
