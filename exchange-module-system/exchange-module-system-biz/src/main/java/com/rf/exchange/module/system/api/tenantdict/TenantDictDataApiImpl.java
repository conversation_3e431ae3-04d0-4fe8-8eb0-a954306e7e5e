package com.rf.exchange.module.system.api.tenantdict;

import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import com.rf.exchange.module.system.util.tenantdict.TenantDictUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-11
 */
@Service
public class TenantDictDataApiImpl implements TenantDictDataApi {

    @Resource
    private TenantDictDataService tenantDictDataService;

    @Override
    public void validateDictDataList(long tenantId, String dictType, Collection<String> values) {
        tenantDictDataService.validateDictDataList(tenantId, dictType, values);
    }

    @Override
    public TenantDictDataRespDTO getDictData(long tenantId, String dictType, String value) {
        TenantDictDataDO dictData = tenantDictDataService.getDictData(tenantId, dictType, value);
        return BeanUtils.toBean(dictData, TenantDictDataRespDTO.class);
    }

    @Override
    public TenantDictDataRespDTO parseDictData(long tenantId, String dictType, String label) {
        TenantDictDataDO dictData = tenantDictDataService.parseDictData(tenantId, dictType, label);
        return BeanUtils.toBean(dictData, TenantDictDataRespDTO.class);
    }

    @Override
    public String parseDictDataStringValue(long tenantId, String dictType, String label) {
        return TenantDictUtils.parseDictDataValue(tenantId, dictType, label);
    }

    @Override
    public int parseDictDataIntValue(long tenantId, String dictType, String label) {
        return TenantDictUtils.parseDictDataIntValue(tenantId, dictType, label);
    }

    @Override
    public double parseDictDataDoubleValue(long tenantId, String dictType, String label) {
        return TenantDictUtils.parseDictDataDoubleValue(tenantId, dictType, label);
    }

    @Override
    public boolean parseDictDataBoolValue(long tenantId, String dictType, String label) {
        return TenantDictUtils.parseDictDataBoolValue(tenantId, dictType, label);
    }

    @Override
    public List<TenantDictDataRespDTO> getDictDataList(long tenantId, String dictType) {
        List<TenantDictDataDO> list = tenantDictDataService.getDictDataListByDictType(tenantId, dictType);
        return BeanUtils.toBean(list, TenantDictDataRespDTO.class);
    }
}
