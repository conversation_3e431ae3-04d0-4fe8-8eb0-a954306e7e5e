package com.rf.exchange.module.system.dal.dataobject.agent;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 代理实时统计 实体
 *
 * <AUTHOR>
 * @since 2024-06-16
 */
@TableName(value = "system_agent_statistic", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentStatisticDO extends TenantBaseDO {
    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 代理商编号
     */
    private Long agentId;
    /**
     * 代理名称
     */
    private String agentName;
    /**
     * 注册用户数
     */
    private Long registerCount;
    /**
     * 充值用户数
     */
    private Long rechargeCount;
    /**
     * 提现用户数
     */
    private Long withdrawCount;
    /**
     * 总余额
     */
    private BigDecimal totalBalance;
    /**
     * 用户总存款金额
     */
    private BigDecimal totalDepositAmount;
    /**
     * 用户总提款金额
     */
    private BigDecimal totalWithdrawAmount;

}
