package com.rf.exchange.module.system.api.agent;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.service.agent.AgentService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AgentApiImpl implements AgentApi {

    @Resource
    private AgentService agentService;

    @Override
    public AgentBaseRespDTO getAgentByCode(String code) {
        AgentDO agentDO = agentService.getAgentByCode(code);
        return BeanUtils.toBean(agentDO, AgentBaseRespDTO.class);
    }

    @Override
    public AgentBaseRespDTO getAgent(Long id) {
        AgentDO agentDO = agentService.getAgent(id);
        return BeanUtils.toBean(agentDO, AgentBaseRespDTO.class);
    }

    @Override
    public AgentBaseRespDTO getDefault(long tenantId) {
        AgentDO agentDO = agentService.getDefault(tenantId);
        return BeanUtils.toBean(agentDO, AgentBaseRespDTO.class);
    }

    @Override
    public Map<Long, AgentBaseRespDTO> getAncestorListByIds(Set<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new HashMap<>();
        }
        return agentService.getAncestorListByIds(idList);
    }

    @Override
    public List<AgentBaseRespDTO> getAllDescendants(Long ancestor) {
        final List<AgentDO> allDescendants = agentService.getAllDescendants(ancestor);
        return BeanUtils.toBean(allDescendants, AgentBaseRespDTO.class);
    }

    @Override
    public AgentBaseRespDTO getAgentChainRoot(Long agentId) {
        final AgentDO rootAgent = agentService.getRootAncestor(agentId);
        return BeanUtils.toBean(rootAgent, AgentBaseRespDTO.class);
    }

    @Override
    public TreeMap<Long, AgentBaseRespDTO> getAncestorMapOfAgent(Long agentId) {
        final Map<Long, AgentDO> ancestorMap = agentService.getAncestorMapOfAgent(agentId);
        TreeMap<Long, AgentBaseRespDTO> map = new TreeMap<>(Comparator.reverseOrder());
        for (Map.Entry<Long, AgentDO> entry : ancestorMap.entrySet()) {
            map.put(entry.getKey(), BeanUtils.toBean(entry.getValue(), AgentBaseRespDTO.class));
        }
        return map;
    }

    @Override
    public Map<Long, AgentBaseRespDTO> getAgentsByIds(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Map.of();
        }
        final Map<Long, AgentDO> agentMap = agentService.getAgentsByIds(ids);
        return agentMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BeanUtils.toBean(entry.getValue(), AgentBaseRespDTO.class)
                ));
    }

    @Override
    public List<AgentBaseRespDTO> getUserMoveAvailableAgents(Long agentId) {
        return agentService.getUserMoveAvailableAgents(agentId);
    }
}
