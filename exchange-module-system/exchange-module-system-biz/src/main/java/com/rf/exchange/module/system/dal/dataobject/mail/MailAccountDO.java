package com.rf.exchange.module.system.dal.dataobject.mail;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邮箱账号 DO
 * <p>
 * 用途：配置发送邮箱的账号
 *
 * @since 2022-03-21
 */
@TableName(value = "system_mail_account", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class MailAccountDO extends TenantBaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 邮箱
     */
    private String mail;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 密钥
     */
    private String secret;
    /**
     * SMTP 服务器域名
     */
    private String host;
    /**
     * SMTP 服务器端口
     */
    private Integer port;
    /**
     * 发件人名称
     */
    private String senderName;
    /**
     * webhook的签名key
     */
    private String webhookKey;
    /**
     * 是否开启 SSL
     */
    private Boolean sslEnable;
    /**
     * 是否开启 STARTTLS
     */
    private Boolean starttlsEnable;
    /**
     * 模版代码列表
     * 使用逗号(,)分割
     */
    //@TableField(typeHandler = StringListTypeHandler.class)
    private String templateCodes;
    /**
     * 删除时间
     */
    private Long deleteTime;
    /**
     * 邮箱域
     */
    private String domain;
}
