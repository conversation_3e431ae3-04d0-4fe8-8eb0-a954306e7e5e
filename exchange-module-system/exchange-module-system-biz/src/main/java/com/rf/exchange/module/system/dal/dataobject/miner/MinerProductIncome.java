package com.rf.exchange.module.system.dal.dataobject.miner;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 挖矿产品收益明细表 DO
 *
 * <AUTHOR>
 */
@TableName(value = "miner_product_income", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MinerProductIncome extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 本金
     */
    private BigDecimal principal;

    /**
     * 利息
     */
    private BigDecimal income;

}