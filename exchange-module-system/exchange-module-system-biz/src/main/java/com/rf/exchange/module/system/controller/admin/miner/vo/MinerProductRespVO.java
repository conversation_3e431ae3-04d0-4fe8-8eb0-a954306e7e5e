package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 挖矿产品创建-编辑参数")
@Data
public class MinerProductRespVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "文字内容")
    private String title;

    @Schema(description = "周期", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    private Integer cycle;

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    private String img;

    @Schema(description = "最小收益率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.5")
    private BigDecimal minProfit;

    @Schema(description = "最大收益率", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.5")
    private BigDecimal maxProfit;

    @Schema(description = "最低购买金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private BigDecimal minPurchase;

    @Schema(description = "最大购买金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "500")
    private BigDecimal maxPurchase;

    @Schema(description = "提前赎回违约金百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.2")
    private BigDecimal liquidatedDamagesRatio;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    private Integer sort;
}
