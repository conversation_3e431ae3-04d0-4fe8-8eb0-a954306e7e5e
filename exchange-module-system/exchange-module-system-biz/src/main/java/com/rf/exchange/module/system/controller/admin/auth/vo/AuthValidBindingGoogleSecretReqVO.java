package com.rf.exchange.module.system.controller.admin.auth.vo;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 校验是否绑定谷歌验证
 * @author: Petter
 * @create: 2024-08-14
 **/
@Data
@Schema(description = "校验是否绑定谷歌验证")
public class AuthValidBindingGoogleSecretReqVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -6341448017972166692L;

    @Schema(description = "用户名")
    private String name;

}
