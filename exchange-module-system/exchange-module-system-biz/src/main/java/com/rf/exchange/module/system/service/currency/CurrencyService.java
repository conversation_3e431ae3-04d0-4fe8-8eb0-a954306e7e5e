package com.rf.exchange.module.system.service.currency;

import java.util.*;
import jakarta.validation.*;
import com.rf.exchange.module.system.controller.admin.currency.vo.*;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.PageParam;

/**
 * 系统货币 Service 接口
 *
 * <AUTHOR>
 */
public interface CurrencyService {

    /**
     * 创建系统货币
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCurrency(@Valid CurrencySaveReqVO createReqVO);

    /**
     * 更新系统货币
     *
     * @param updateReqVO 更新信息
     */
    void updateCurrency(@Valid CurrencySaveReqVO updateReqVO);

    /**
     * 删除系统货币
     *
     * @param id 编号
     */
    void deleteCurrency(Long id);

    /**
     * 获得系统货币
     *
     * @param id 编号
     * @return 系统货币
     */
    CurrencyDO getCurrency(Long id);

    /**
     * 获得系统货币
     *
     * @param code 编号
     * @return 系统货币
     */
    CurrencyDO getCurrencyByCode(String code);
    /**
     * 获得系统货币分页
     *
     * @param pageReqVO 分页查询
     * @return 系统货币分页
     */
    PageResult<CurrencyDO> getCurrencyPage(CurrencyPageReqVO pageReqVO);

    /**
     * 获取系统法定货币币种信息
     * @return 货币列表
     */
    List<CurrencyDO> getCurrencyList();
}