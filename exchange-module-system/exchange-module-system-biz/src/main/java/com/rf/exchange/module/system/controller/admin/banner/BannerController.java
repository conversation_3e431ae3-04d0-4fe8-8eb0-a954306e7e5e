package com.rf.exchange.module.system.controller.admin.banner;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.image.ImageUrlUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.system.api.dict.DictDataApi;
import com.rf.exchange.module.system.controller.admin.banner.vo.BannerPageReqVO;
import com.rf.exchange.module.system.controller.admin.banner.vo.BannerRespVO;
import com.rf.exchange.module.system.controller.admin.banner.vo.BannerSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.banner.BannerDO;
import com.rf.exchange.module.system.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - banner")
@RestController
@RequestMapping("/system/banner")
@Validated
public class BannerController {

    @Resource
    private BannerService bannerService;
    @Resource
    private DictDataApi dictDataApi;

    @PostMapping("/create")
    @Operation(summary = "创建banner")
    @PreAuthorize("@ss.hasPermission('system:banner:create')")
    public CommonResult<Long> createBanner(@Valid @RequestBody BannerSaveReqVO createReqVO) {
        createReqVO.setImg(ImageUrlUtil.extractPath(createReqVO.getImg()));
        if(!StringUtils.hasText(createReqVO.getLinkUrl())){
            createReqVO.setLinkUrl("");
        }
        if(createReqVO.getLinkType()==null){
            createReqVO.setLinkType(0);
        }
        return success(bannerService.createBanner(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新banner")
    @PreAuthorize("@ss.hasPermission('system:banner:update')")
    public CommonResult<Boolean> updateBanner(@Valid @RequestBody BannerSaveReqVO updateReqVO) {
        updateReqVO.setImg(ImageUrlUtil.extractPath(updateReqVO.getImg()));
        if(!StringUtils.hasText(updateReqVO.getLinkUrl())){
            updateReqVO.setLinkUrl("");
        }
        if(updateReqVO.getLinkType()==null){
            updateReqVO.setLinkType(0);
        }
        bannerService.updateBanner(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除banner")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:banner:delete')")
    public CommonResult<Boolean> deleteBanner(@RequestParam("id") Long id) {
        bannerService.deleteBanner(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得banner")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:banner:query')")
    public CommonResult<BannerRespVO> getBanner(@RequestParam("id") Long id) {
        BannerDO banner = bannerService.getBanner(id);
        BannerRespVO respVO=BeanUtils.toBean(banner, BannerRespVO.class);
        dictDataApi.fillS3Host(respVO,BannerRespVO.class);
        return success(respVO);
    }

    @PostMapping("/page")
    @Operation(summary = "获得banner分页")
    @PreAuthorize("@ss.hasPermission('system:banner:query')")
    public CommonResult<PageResult<BannerRespVO>> getBannerPage(@Valid @RequestBody BannerPageReqVO pageReqVO) {
        PageResult<BannerDO> pageResult = bannerService.getBannerPage(pageReqVO);
        PageResult<BannerRespVO> list=BeanUtils.toBean(pageResult, BannerRespVO.class);
        dictDataApi.fillS3Host(list.getList(),BannerRespVO.class);
        return success(list);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出banner Excel")
    @PreAuthorize("@ss.hasPermission('system:banner:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBannerExcel(@Valid BannerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BannerDO> list = bannerService.getBannerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "banner.xls", "数据", BannerRespVO.class,
                        BeanUtils.toBean(list, BannerRespVO.class));
    }

}