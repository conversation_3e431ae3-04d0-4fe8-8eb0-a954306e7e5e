package com.rf.exchange.module.system.dal.mysql.miner;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.sms.vo.channel.SmsChannelPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.sms.SmsChannelDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MinerProductMapper extends BaseMapperX<MinerProduct> {

    default PageResult<MinerProduct> selectPage(MinerProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MinerProduct>()
                .likeIfPresent(MinerProduct::getTitle, reqVO.getTitle())
                .betweenIfPresent(MinerProduct::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MinerProduct::getSort));
    }

}
