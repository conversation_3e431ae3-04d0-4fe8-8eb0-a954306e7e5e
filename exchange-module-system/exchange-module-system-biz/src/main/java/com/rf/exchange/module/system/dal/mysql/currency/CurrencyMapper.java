package com.rf.exchange.module.system.dal.mysql.currency;

import java.util.*;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.system.controller.admin.currency.vo.*;

/**
 * 系统货币 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CurrencyMapper extends BaseMapperX<CurrencyDO> {

    default PageResult<CurrencyDO> selectPage(CurrencyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CurrencyDO>()
                .likeIfPresent(CurrencyDO::getName, reqVO.getName())
                .eqIfPresent(CurrencyDO::getCode, reqVO.getCode())
                .eqIfPresent(CurrencyDO::getSymbol, reqVO.getSymbol())
                .eqIfPresent(CurrencyDO::getType, reqVO.getType())
                .eqIfPresent(CurrencyDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(CurrencyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CurrencyDO::getId));
    }

    default List<CurrencyDO> selectByIdList(Set<Long> idList) {
        return selectList(new LambdaQueryWrapperX<CurrencyDO>()
                .inIfPresent(CurrencyDO::getId,idList));
    }
}