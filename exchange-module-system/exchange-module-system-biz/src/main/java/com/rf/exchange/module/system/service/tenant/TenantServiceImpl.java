package com.rf.exchange.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.collection.CollectionUtils;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.config.TenantProperties;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.exc.api.tradeassettype.TradeAssetTypeApi;
import com.rf.exchange.module.exc.api.tradepair.TradePairTenantApi;
import com.rf.exchange.module.member.api.levelconfig.MemberLevelConfigApi;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentSaveReqVO;
import com.rf.exchange.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNameSaveReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.rf.exchange.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import com.rf.exchange.module.system.convert.tenant.TenantConvert;
import com.rf.exchange.module.system.dal.dataobject.permission.MenuDO;
import com.rf.exchange.module.system.dal.dataobject.permission.RoleDO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.module.system.dal.mysql.tenant.TenantMapper;
import com.rf.exchange.module.system.enums.DictTypeConstants;
import com.rf.exchange.module.system.enums.permission.RoleCodeEnum;
import com.rf.exchange.module.system.enums.permission.RoleTypeEnum;
import com.rf.exchange.module.system.service.agent.AgentService;
import com.rf.exchange.module.system.service.permission.MenuService;
import com.rf.exchange.module.system.service.permission.PermissionService;
import com.rf.exchange.module.system.service.permission.RoleService;
import com.rf.exchange.module.system.service.tenant.handler.TenantInfoHandler;
import com.rf.exchange.module.system.service.tenant.handler.TenantMenuHandler;
import com.rf.exchange.module.system.service.tenantdict.TenantDictTypeService;
import com.rf.exchange.module.system.service.user.AdminUserService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.rf.exchange.module.system.util.tenantdict.TenantDictUtils;
import com.rf.exchange.module.trade.api.TradeDurationApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;
import static java.util.Collections.singleton;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 exchange.tenant.enable 配置项，可以关闭多租户的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;
    @Resource
    private TenantServerNameService tenantServerNameService;
    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantPackageService tenantPackageService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private TenantDictTypeService tenantDictTypeService;
    @Resource
    private AgentService agentService;
    @Resource
    @Lazy
    private MemberLevelConfigApi memberLevelConfigApi;
    @Resource
    @Lazy
    private TradeDurationApi tradeDurationApi;
    @Resource
    @Lazy
    private TradeAssetTypeApi tradeAssetTypeApi;
    @Resource
    @Lazy
    private TradePairTenantApi tradePairTenantApi;


    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public TenantDO validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
        if (DateUtils.isExpired(tenant.getExpireTime())) {
            throw exception(TENANT_EXPIRE, tenant.getName());
        }
        return tenant;
    }

    @Override
    public String getTenantDefaultLang(Long tenantId) {
        return TenantDictUtils.parseDictDataValue(tenantId, DictTypeConstants.TENANT_DEFAULT_LANGUAGE, "default");
    }

    @Override
    @Master
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    public Long createTenant(TenantSaveReqVO createReqVO) {
        // 校验租户编码是否重复
        validTenantCodeDuplicate(createReqVO.getCode(), null);
        // 校验租户的登录账号是否已经存在
        if (userService.validUsernameIsExist(createReqVO.getUsername())) {
            throw exception(USER_USERNAME_EXISTS, "管理");
        }

        // 校验租户域名是否重复
        //validTenantWebsiteDuplicate(createReqVO.getWebsite(), null);
        // 校验套餐被禁用
//        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(createReqVO.getPackageId());

        // 创建租户
        TenantDO tenant = BeanUtils.toBean(createReqVO, TenantDO.class);
        tenant.setPackageId(0L);
        tenantMapper.insert(tenant);

        //这里使用固定的代理编码获取角色
        RoleDO role = roleService.getRoleByCode(RoleCodeEnum.TENANT_ADMIN.getCode());
        if (role == null) {
            throw exception(ROLE_NOT_EXISTS);
        }

        // 创建租户的登陆用户，并分配角色
        Long userId = createUser(tenant.getId(), role.getId(), createReqVO);
        // 修改租户的管理员
        tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
        //复制租户ID为1的字典数据
        tenantDictTypeService.copyTenantDictTypeAndData(tenant.getId());
        //添加域名
        TenantServerNameSaveReqVO serverNameSaveReqVO = new TenantServerNameSaveReqVO();
        serverNameSaveReqVO.setTenId(tenant.getId());
        serverNameSaveReqVO.setServerName(createReqVO.getFrontUrl());
        serverNameSaveReqVO.setStatus(0);
        tenantServerNameService.createTenantServerName(serverNameSaveReqVO);

        final AdminUserDO adminUser = userService.getUser(userId);

        //创建默认代理
        AgentSaveReqVO agentDO = new AgentSaveReqVO();
        agentDO.setName(tenant.getName());
        agentDO.setCode(tenant.getCode() + RandomUtil.randomNumbers(4));
        agentDO.setUsername("agent" + createReqVO.getUsername());
        agentDO.setPassword(createReqVO.getPassword());
        agentDO.setRemark(tenant.getName() + "默认代理");
        agentDO.setAncestorId(0L);
        agentDO.setFirst(true);
        agentService.createAgent(tenant.getId(), agentDO, adminUser);
        //创建这个租户的默认等级
        memberLevelConfigApi.createDefaultLevelConfig(tenant.getId());

        // 从租户1拷贝租户的配置数据
        tradeDurationApi.copyDataFromTenant(1L, tenant.getId());
        tradeAssetTypeApi.copyDataFromTenant(1L, tenant.getId());
        tradePairTenantApi.copyDataFromTenant(1L, tenant.getId());

        return tenant.getId();
    }

    private Long createUser(Long tenantId, Long roleId, TenantSaveReqVO createReqVO) {
        // 创建用户
        UserSaveReqVO userSaveReqVO = TenantConvert.INSTANCE.convert(createReqVO);
        userSaveReqVO.setIsTenantAdmin(true);
        userSaveReqVO.setDeptId(102L);
        Long userId = userService.createUser(tenantId, userSaveReqVO);
        // 分配角色
        permissionService.assignUserRole(userId, singleton(roleId));
        return userId;
    }

    private Long createRole(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleSaveReqVO reqVO = new RoleSaveReqVO();
        reqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName()).setCode(RoleCodeEnum.TENANT_ADMIN.getCode())
                .setSort(0).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
        return roleId;
    }

    @Override
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    public void updateTenant(TenantSaveReqVO updateReqVO) {
        // 校验存在
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        // 校验租户码是否重复
        validTenantCodeDuplicate(updateReqVO.getCode(), updateReqVO.getId());
        // 校验租户域名是否重复
        //validTenantWebsiteDuplicate(updateReqVO.getWebsite(), updateReqVO.getId());
        // 校验套餐被禁用
//        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(updateReqVO.getPackageId());

        // 更新租户
        TenantDO updateObj = BeanUtils.toBean(updateReqVO, TenantDO.class);
        updateObj.setCode(null);
        tenantMapper.updateById(updateObj);
//        // 如果套餐发生变化，则修改其角色的权限
//        if (ObjectUtil.notEqual(tenant.getPackageId(), updateReqVO.getPackageId())) {
//            updateTenantRoleMenu(tenant.getId(), tenantPackage.getMenuIds());
//        }
    }

    private void validTenantCodeDuplicate(String code, Long id) {
        TenantDO tenant = tenantMapper.selectByCode(code);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_CODE_DUPLICATE, code);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_CODE_DUPLICATE, code);
        }
    }

//    private void validTenantWebsiteDuplicate(String website, Long id) {
//        if (StrUtil.isEmpty(website)) {
//            return;
//        }
//        TenantDO tenant = tenantMapper.selectByWebsite(website);
//        if (tenant == null) {
//            return;
//        }
//        // 如果 id 为空，说明不用比较是否为相同名字的租户
//        if (id == null) {
//            throw exception(TENANT_WEBSITE_DUPLICATE, website);
//        }
//        if (!tenant.getId().equals(id)) {
//            throw exception(TENANT_WEBSITE_DUPLICATE, website);
//        }
//    }

    @Override
    @DSTransactional
    public void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds) {
        TenantUtils.execute(tenantId, () -> {
            // 获得所有角色
            List<RoleDO> roles = roleService.getRoleList();
            roles.forEach(role -> Assert.isTrue(tenantId.equals(role.getTenantId()), "角色({}/{}) 租户不匹配",
                    role.getId(), role.getTenantId(), tenantId)); // 兜底校验
            // 重新分配每个角色的权限
            roles.forEach(role -> {
                // 如果是租户管理员，重新分配其权限为租户套餐的权限
                if (Objects.equals(role.getCode(), RoleCodeEnum.TENANT_ADMIN.getCode())) {
                    permissionService.assignRoleMenu(role.getId(), menuIds);
                    log.info("[updateTenantRoleMenu][租户管理员({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), menuIds);
                    return;
                }
                // 如果是其他角色，则去掉超过套餐的权限
                Set<Long> roleMenuIds = permissionService.getRoleMenuListByRoleId(role.getId());
                roleMenuIds = CollUtil.intersectionDistinct(roleMenuIds, menuIds);
                permissionService.assignRoleMenu(role.getId(), roleMenuIds);
                log.info("[updateTenantRoleMenu][角色({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), roleMenuIds);
            });
        });
    }

    @Override
    public void deleteTenant(Long id) {
        // 校验存在
        validateUpdateTenant(id);
        // 删除
        tenantMapper.deleteById(id);
    }

    private TenantDO validateUpdateTenant(Long id) {
        TenantDO tenant = tenantMapper.selectById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置租户，不允许删除
        if (isSystemTenant(tenant)) {
            throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
        }
        return tenant;
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO) {
        return tenantMapper.selectPage(pageReqVO);
    }

    @Override
    public TenantDO getTenantByCode(String code) {
        return tenantMapper.selectByCode(code);
    }

    @Override
    public TenantDO getTenantByName(String name) {
        return tenantMapper.selectByName(name);
    }


    @Override
    public Long getTenantCountByPackageId(Long packageId) {
        return tenantMapper.selectCountByPackageId(packageId);
    }

    @Override
    public List<TenantDO> getTenantListByPackageId(Long packageId) {
        return tenantMapper.selectListByPackageId(packageId);
    }

    @Override
    public List<TenantDO> getTenantList() {
        return tenantMapper.selectTenantList();
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        Set<Long> menuIds;
        if (isSystemTenant(tenant)) { // 系统租户，菜单是全量的
            menuIds = CollectionUtils.convertSet(menuService.getMenuList(), MenuDO::getId);
        } else {
            menuIds = tenantPackageService.getTenantPackage(tenant.getPackageId()).getMenuIds();
        }
        // 执行处理器
        handler.handle(menuIds);
    }

    private static boolean isSystemTenant(TenantDO tenant) {
        return tenant.getId()==1l;
        //return Objects.equals(tenant.getPackageId(), TenantDO.PACKAGE_ID_SYSTEM);
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

}
