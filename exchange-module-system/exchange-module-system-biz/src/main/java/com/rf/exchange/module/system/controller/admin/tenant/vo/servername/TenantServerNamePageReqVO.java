package com.rf.exchange.module.system.controller.admin.tenant.vo.servername;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 系统租户域名分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantServerNamePageReqVO extends PageParam {

    @Schema(description = "租户id", example = "10502")
    private Long tenId;

    @Schema(description = "域名", example = "赵六")
    private String serverName;

    @Schema(description = "开启状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}