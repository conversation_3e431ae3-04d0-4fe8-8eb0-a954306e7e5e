package com.rf.exchange.module.system.service.agent;


import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import com.rf.exchange.module.system.controller.admin.agent.vo.*;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentTreeDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 代理 Service 接口
 *
 * <AUTHOR>
 * @since 2024-06-08
 */
public interface AgentService {

    /**
     * 创建代理
     *
     * @param createReqVO 创建代理信息
     * @param loginUser   与此代理关联的系统用户
     * @return 代理id
     */
    Long createAgent(Long tenantId, AgentSaveReqVO createReqVO, AdminUserDO loginUser);

    /**
     * 更新代理
     *
     * @param updateReqVO 代理信息
     */
    void updateAgent(@Valid AgentSaveReqVO updateReqVO);

    /**
     * 修改代理状态
     *
     * @param id     代理id
     * @param status 状态
     */
    void updateAgentStatus(Long id, Integer status);

    /**
     * 移动代理到新的父代理
     *
     * @param moveReqVO 更新信息
     * @param loginUser 当前登录用户
     * @return true: 移动成功
     */
    boolean updateAgentAncestor(AgentAncestorUpdateReqVO moveReqVO, LoginUser loginUser);

    /**
     * 删除代理
     *
     * @param id 代理id
     */
    void deleteAgent(Long id);

    /**
     * 获取代理分页列表
     *
     * @param reqVO     分页条件
     * @param loginUser 当前登录的用户
     * @return 代理分页列表
     */
    PageResult<AgentRespVO> getAgentPage(AgentPageReqVO reqVO, LoginUser loginUser);

    /**
     * 获取移动会员的代理时可用的代理列表
     *
     * @param agentId 用户所属代理id
     * @return 代理列表
     */
    List<AgentBaseRespDTO> getUserMoveAvailableAgents(long agentId);

    /**
     * 获取指定代理下所有层级的子代理
     *
     * @param ancestor 祖先代理id
     * @return 代理对象列表
     */
    List<AgentDO> getAllDescendants(Long ancestor);

    /**
     * 获取 agentId 的根代理
     *
     * @param agentId 代理id
     * @return 根代理信息
     */
    AgentDO getRootAncestor(Long agentId);

    /**
     * 获取所有代理列表
     *
     * @return 代理信息列表
     */
    List<AgentDO> getAllSimpleList();

    /**
     * 获取agentId 可以移动的代理信息
     *
     * @param loginUser 当前登录用户
     * @param agentId   需要移动的代理
     */
    List<AgentSimpleInfoRespVO> getMoveAvailableList(LoginUser loginUser, Long agentId);

    /**
     * 获取指定祖先代理下的指定层级的子代理
     *
     * @param ancestor 祖先代理id
     * @param dept     级差
     * @return 代理对象列表
     */
    List<AgentDO> getAllDescendants(Long ancestor, int dept);

    /**
     * 获取指定代理所有的祖先代理
     *
     * @param descendant 子代理id
     * @return 代理对象列表
     */
    List<AgentDO> getAllAncestors(Long descendant);

    /**
     * 通过id获取代理
     *
     * @param idList 代理id列表
     * @return
     */
    Map<Long, AgentBaseRespDTO> getAncestorListByIds(Set<Long> idList);

    /**
     * 获取子代理的祖先代理
     *
     * @param descendantId 子代理id
     * @return 代理树Map key:深度 value:代理信息
     */
    Map<Long, AgentDO> getAncestorMapOfAgent(Long descendantId);

    /**
     * 获取指定代理的指定层级的祖先代理
     *
     * @param descendant 子代理id
     * @param dept       级差
     * @return 代理对象列表
     */
    AgentDO getAncestorByDept(Long descendant, int dept);

    /**
     * 获取代理id指定层级深度的父代理
     *
     * @param descendants 子代理列表
     * @param dept        层级深度
     * @return 代理信息列表
     */
    List<AgentDO> getAncestorsByDept(List<Long> descendants, int dept);

    /**
     * 获取指定子代理和指定祖先代理之间的级差
     *
     * @param descendant 子代理id
     * @param ancestor   祖先代理id
     * @return 级差
     */
    int getDepth(Long descendant, Long ancestor);

    /**
     * 插入新的代理关系
     *
     * @param descendant 子代理对象
     * @param ancestor   父代理对象
     */
    void insertAgent(AgentDO descendant, AgentDO ancestor);

    /**
     * 通过编码获取代理信息
     *
     * @param code 代理邀请码
     * @return 代理对象
     */
    AgentDO getAgentByCode(String code);

    /**
     * 获取指定id的代理信息
     *
     * @param id 代理id
     * @return 代理对象
     */
    AgentDO getAgent(Long id);

    /**
     * 获取指定代理id集合的代理信息
     *
     * @param ids 代理id集合
     * @return 代理信息的map key:代理id
     */
    Map<Long, AgentDO> getAgentsByIds(Set<Long> ids);

    /**
     * 通过用户id获取代理id
     *
     * @param userId 用户id
     * @return 代理id
     */
    Long getAgentIdByUserId(Long userId);

    /**
     * 获取所有子代理(不包含自己)通过登陆账号id，用于过滤代理查看的数据
     *
     * @param agentId 代理id
     * @return 子代理id
     */
    List<Long> selectAllDescendantIdList(Long agentId);

    /**
     * 获取租户的默认代理
     *
     * @return 默认代理信息
     */
    AgentDO getDefault(Long tenantId);

    /**
     * 获取所有树关系
     *
     * @return
     */
    List<AgentTreeDO> getAllTree();

}