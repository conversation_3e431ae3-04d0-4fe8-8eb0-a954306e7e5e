package com.rf.exchange.module.system.controller.app.miner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 挖矿产品创建-编辑参数")
@Data
public class MinerProductCreateOrderReqVo {

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    @Schema(description = "下单金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.5")
    @NotNull(message = "下单金额不能为空")
    private BigDecimal amount;

}
