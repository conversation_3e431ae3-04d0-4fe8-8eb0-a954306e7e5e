package com.rf.exchange.module.system.controller.admin.agent.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 代理统计 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentStatisticPageReqVO extends PageParam {
    @Schema(description = "代理名称, 模糊匹配", example = "1")
    private String agentName;
}
