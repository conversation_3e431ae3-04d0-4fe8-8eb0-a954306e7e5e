package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 协议管理新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Schema(description = "管理后台 - 协议管理新增/修改 Request VO")
@Data
public class AgreementSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "协议类型不能为空")
    private Integer type;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    @NotBlank(message = "协议标题不能为空")
    private String title;

    @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "协议内容不能为空")
    private String content;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "状态：0-禁用 1-启用", example = "1")
    private Integer status;

    @Schema(description = "生效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime effectiveTime;

    @Schema(description = "备注", example = "这是一个备注")
    private String remark;
}
