package com.rf.exchange.module.system.service.currency;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rf.exchange.module.system.controller.admin.currency.vo.*;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.system.dal.mysql.currency.CurrencyMapper;

import java.util.List;
import java.util.Set;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_EXISTS;

/**
 * 系统货币 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CurrencyServiceImpl implements CurrencyService {

    @Resource
    private CurrencyMapper currencyMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.SYS_CURRENCY)
    public Long createCurrency(CurrencySaveReqVO createReqVO) {
        // 插入
        CurrencyDO currency = BeanUtils.toBean(createReqVO, CurrencyDO.class);
        currencyMapper.insert(currency);
        // 返回
        return currency.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.SYS_CURRENCY)
    public void updateCurrency(CurrencySaveReqVO updateReqVO) {
        // 校验存在
        validateCurrencyExists(updateReqVO.getId());
        // 更新
        CurrencyDO updateObj = BeanUtils.toBean(updateReqVO, CurrencyDO.class);
        currencyMapper.updateById(updateObj);
    }

    @Override
    public void deleteCurrency(Long id) {
        // 校验存在
        validateCurrencyExists(id);
        // 删除
        currencyMapper.deleteById(id);
    }

    private void validateCurrencyExists(Long id) {
        if (currencyMapper.selectById(id) == null) {
            throw exception(CURRENCY_NOT_EXISTS);
        }
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SYS_CURRENCY, key = "#id", unless = "#result == null")
    public CurrencyDO getCurrency(Long id) {
        return currencyMapper.selectById(id);
    }

    @Override
    public CurrencyDO getCurrencyByCode(String code) {
        CurrencyDO currencyDO=currencyMapper.selectOne(new LambdaQueryWrapperX<CurrencyDO>().eq(CurrencyDO::getCode,code));
        return currencyDO;
    }

    @Override
    public PageResult<CurrencyDO> getCurrencyPage(CurrencyPageReqVO pageReqVO) {
        return currencyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CurrencyDO> getCurrencyList() {
        LambdaQueryWrapperX<CurrencyDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CurrencyDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        wrapper.orderByAsc(CurrencyDO::getSort);
        return currencyMapper.selectList();
    }
}