package com.rf.exchange.module.system.controller.admin.lang;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.system.controller.admin.lang.vo.*;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import com.rf.exchange.module.system.service.lang.LangService;

@Tag(name = "管理后台 - 系统语种配置")
@RestController
@RequestMapping("/system/lang")
@Validated
public class LangController {

    @Resource
    private LangService langService;

    @PostMapping("/create")
    @Operation(summary = "创建系统语种配置")
    @PreAuthorize("@ss.hasPermission('system:lang:create')")
    public CommonResult<Long> createLang(@Valid @RequestBody LangSaveReqVO createReqVO) {
        return success(langService.createLang(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统语种配置")
    @PreAuthorize("@ss.hasPermission('system:lang:update')")
    public CommonResult<Boolean> updateLang(@Valid @RequestBody LangSaveReqVO updateReqVO) {
        langService.updateLang(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统语种配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:lang:delete')")
    public CommonResult<Boolean> deleteLang(@RequestParam("id") Long id) {
        langService.deleteLang(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统语种配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:lang:query')")
    public CommonResult<LangRespVO> getLang(@RequestParam("id") Long id) {
        LangDO lang = langService.getLang(id);
        return success(BeanUtils.toBean(lang, LangRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得系统语种配置分页")
    @PreAuthorize("@ss.hasPermission('system:lang:query')")
    public CommonResult<PageResult<LangRespVO>> getLangPage(@Valid @RequestBody LangPageReqVO pageReqVO) {
        PageResult<LangDO> pageResult = langService.getLangPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LangRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统语种配置 Excel")
    @PreAuthorize("@ss.hasPermission('system:lang:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLangExcel(@Valid LangPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LangDO> list = langService.getLangPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "系统语种配置.xls", "数据", LangRespVO.class,
                        BeanUtils.toBean(list, LangRespVO.class));
    }

}