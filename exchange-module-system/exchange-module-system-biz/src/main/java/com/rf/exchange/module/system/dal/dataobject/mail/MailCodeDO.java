package com.rf.exchange.module.system.dal.dataobject.mail;

import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 邮箱验证码 DO
 *
 * <AUTHOR>
 */
@TableName("system_mail_code")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailCodeDO extends BaseNoDeleteDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 邮箱
     */
    private String mail;
    /**
     * 验证码
     */
    private String code;
    /**
     * 创建ip
     */
    private String createIp;
    /**
     * 发送场景
     * <p>
     * {@link MailSceneEnum}
     */
    private Integer scene;
    /**
     * 今日发送的第几条
     */
    private Integer todayIndex;
    /**
     * 是否使用
     */
    private Boolean used;
    /**
     * 使用时间
     */
    private Long usedTime;
    /**
     * 使用ip
     */
    private String usedIp;

}