package com.rf.exchange.module.system.controller.app.ip;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.enums.AreaTypeEnum;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.framework.ip.core.utils.IPUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 地区")
@RestController
@RequestMapping("/system/area")
@Validated
public class AppAreaController {

    @GetMapping("/get-id-by-ip")
    @Operation(summary = "获得 IP 对应的地区编码")
    @Parameter(name = "ip", description = "IP", required = true)
    public CommonResult<Integer> getAreaCodeByIp(@RequestParam("ip") String ip) {
        // 获得城市
        Area area = IPUtils.getArea(ip);
        if (area == null) {
            return success(-1);
        }
        // 格式化返回
        return success(area.getId());
    }
}
