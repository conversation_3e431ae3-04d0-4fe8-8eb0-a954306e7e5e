package com.rf.exchange.module.system.dal.mysql.tenantdict;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 租户字典数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantDictDataMapper extends BaseMapperX<TenantDictDataDO> {

    default TenantDictDataDO selectByDictTypeAndValue(Long tenantId, String dictType, String value) {
        return selectOne(new LambdaQueryWrapperX<TenantDictDataDO>()
                .eqIfPresent(TenantDictDataDO::getTenantId, tenantId)
                .eqIfPresent(TenantDictDataDO::getDictType, dictType)
                .eqIfPresent(TenantDictDataDO::getValue, value)
                .eqIfPresent(TenantDictDataDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
    }

    default TenantDictDataDO selectByDictTypeAndLabel(Long tenantId, String dictType, String label) {
        return selectOne(new LambdaQueryWrapperX<TenantDictDataDO>()
                .eqIfPresent(TenantDictDataDO::getTenantId, tenantId)
                .eqIfPresent(TenantDictDataDO::getDictType, dictType)
                .eqIfPresent(TenantDictDataDO::getLabel, label)
                .eqIfPresent(TenantDictDataDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
    }

    default List<TenantDictDataDO> selectByDictTypeAndValues(Long tenantId, String dictType, Collection<String> values) {
        return selectList(new LambdaQueryWrapper<TenantDictDataDO>()
                .eq(tenantId != null, TenantDictDataDO::getTenantId, tenantId)
                .eq(TenantDictDataDO::getDictType, dictType)
                .in(TenantDictDataDO::getValue, values));
    }

    default PageResult<TenantDictDataDO> selectPage(TenantDictDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantDictDataDO>()
                .eqIfPresent(TenantDictDataDO::getLabel, reqVO.getLabel())
                .eqIfPresent(TenantDictDataDO::getValue, reqVO.getValue())
                .eqIfPresent(TenantDictDataDO::getDictType, reqVO.getDictType())
                .eqIfPresent(TenantDictDataDO::getSort, reqVO.getSort())
                .eqIfPresent(TenantDictDataDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TenantDictDataDO::getColorType, reqVO.getColorType())
                .eqIfPresent(TenantDictDataDO::getCssClass, reqVO.getCssClass())
                .eqIfPresent(TenantDictDataDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(TenantDictDataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TenantDictDataDO::getId));
    }

    default List<TenantDictDataDO> selectListByStatusAndDictType(Integer status, String dictType) {
        return selectList(new LambdaQueryWrapperX<TenantDictDataDO>()
                .eqIfPresent(TenantDictDataDO::getStatus, status)
                .eqIfPresent(TenantDictDataDO::getDictType, dictType));
    }
}