package com.rf.exchange.module.system.controller.admin.tenant.vo.notifyconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 通知配置")
@Data
@Builder
public class TenantNotifyConfigRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String cmd;
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String name;
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer status;
    @Schema(description = "声音路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String url;
}
