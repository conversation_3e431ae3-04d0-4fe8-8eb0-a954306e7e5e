package com.rf.exchange.module.system.framework.mail.core.client.imp;

import com.rf.exchange.module.system.framework.mail.core.client.MailClient;
import com.rf.exchange.module.system.framework.mail.core.client.MailClientFactory;
import com.rf.exchange.module.system.framework.mail.core.property.MailChannelProperties;

public class Mail<PERSON><PERSON><PERSON><PERSON>yImpl implements MailClientFactory {

    @Override
    public MailClient getMailClient(Long channelId) {
        //FIXME 渠道id获取客户端
        return null;
    }

    @Override
    public MailClient getMailClient(String channelCode) {
        //FIXME 渠道代码获取客户端
        return null;
    }

    @Override
    public void createOrUpdateMailClient(MailChannelProperties properties) {
        //FIXME 更新配置
    }


}
