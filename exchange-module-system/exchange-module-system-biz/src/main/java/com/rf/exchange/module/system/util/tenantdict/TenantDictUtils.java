package com.rf.exchange.module.system.util.tenantdict;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rf.exchange.framework.common.core.DKeyValue;
import com.rf.exchange.framework.common.core.KeyValue;
import com.rf.exchange.framework.common.util.cache.CacheUtils;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;

/**
 * 字典工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantDictUtils {

    private static TenantDictDataApi dictDataApi;

    private static final TenantDictDataRespDTO DICT_DATA_NULL = new TenantDictDataRespDTO();

    /**
     * 针对 {@link #getDictDataLabel(Long, String, String)} 的缓存
     */
    private static final LoadingCache<DKeyValue<Long, String, String>, TenantDictDataRespDTO> GET_DICT_DATA_CACHE = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<>() {
                @Override
                public TenantDictDataRespDTO load(DKeyValue<Long, String, String> key) {
                    return ObjectUtil.defaultIfNull(dictDataApi.getDictData(key.getKey1(), key.getKey2(), key.getValue()), DICT_DATA_NULL);
                }
            });

    private static final LoadingCache<DKeyValue<Long, String, String>, TenantDictDataRespDTO> GET_DICT_LABEL_DATA_CACHE = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<>() {
                @Override
                public TenantDictDataRespDTO load(DKeyValue<Long, String, String> key) {
                    return ObjectUtil.defaultIfNull(dictDataApi.parseDictData(key.getKey1(), key.getKey2(), key.getValue()), DICT_DATA_NULL);
                }
            });

    /**
     * 针对 {@link #getDictDataLabelList(Long, String)} 的缓存
     */
    private static final LoadingCache<KeyValue<Long, String>, List<String>> GET_DICT_DATA_LIST_CACHE = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L), // 过期时间 1 分钟
            new CacheLoader<>() {
                @Override
                public List<String> load(KeyValue<Long, String> key) {
                    return dictDataApi.getDictDataLabelList(key.getKey(), key.getValue());
                }
            });

    /**
     * 针对 {@link #getDictDataDtoList(Long, String)} 的缓存
     */
    private static final LoadingCache<KeyValue<Long, String>, List<TenantDictDataRespDTO>> GET_DICT_DATA_DTO_LIST_CACHE = CacheUtils.buildAsyncReloadingCache(
            Duration.ofMinutes(1L),
            new CacheLoader<>() {
                @Override
                public List<TenantDictDataRespDTO> load(KeyValue<Long, String> keyValue) throws Exception {
                    return dictDataApi.getDictDataList(keyValue.getKey(), keyValue.getValue());
                }
            });

    public static void init(TenantDictDataApi dictDataApi) {
        TenantDictUtils.dictDataApi = dictDataApi;
        log.info("[init][初始化 TenantDictUtils 成功]");
    }

    @SneakyThrows
    public static String getDictDataLabel(Long tenantId, String dictType, Integer value) {
        return GET_DICT_DATA_CACHE.get(new DKeyValue<>(tenantId, dictType, String.valueOf(value))).getLabel();
    }

    @SneakyThrows
    public static String getDictDataLabel(Long tenantId, String dictType, String value) {
        return GET_DICT_DATA_CACHE.get(new DKeyValue<>(tenantId, dictType, value)).getLabel();
    }

    @SneakyThrows
    public static List<String> getDictDataLabelList(Long tenantId, String dictType) {
        return GET_DICT_DATA_LIST_CACHE.get(new KeyValue<>(tenantId, dictType));
    }

    @SneakyThrows
    public static List<TenantDictDataRespDTO> getDictDataDtoList(Long tenantId, String dictType) {
        return GET_DICT_DATA_DTO_LIST_CACHE.get(new KeyValue<>(tenantId, dictType));
    }

    @SneakyThrows
    public static String parseDictDataValue(Long tenantId, String dictType, String label) {
        return GET_DICT_LABEL_DATA_CACHE.get(new DKeyValue<>(tenantId, dictType, label)).getValue();
    }

    @SneakyThrows
    public static int parseDictDataIntValue(Long tenantId, String dictType, String label) {
        final String val = parseDictDataValue(tenantId, dictType, label);
        return NumberUtil.parseInt(val, 0);
    }

    @SneakyThrows
    public static double parseDictDataDoubleValue(Long tenantId, String dictType, String label) {
        final String val = parseDictDataValue(tenantId, dictType, label);
        return NumberUtil.parseDouble(val, 0.0);
    }

    @SneakyThrows
    public static boolean parseDictDataBoolValue(Long tenantId, String dictType, String label) {
        final String val = parseDictDataValue(tenantId, dictType, label);
        return BooleanUtil.toBoolean(val);
    }
}

