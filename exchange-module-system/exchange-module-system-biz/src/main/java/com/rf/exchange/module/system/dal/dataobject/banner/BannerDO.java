package com.rf.exchange.module.system.dal.dataobject.banner;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * banner DO
 *
 * <AUTHOR>
 */
@TableName("system_banner")
@KeySequence("system_banner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BannerDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 图片
     */
    private String img;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 链接路径 
     */
    private String linkUrl;
    /**
     * 链接类型，0内网，1外网
     */
    private Integer linkType;

    /**
     * 位置
     */
    private Short location;

    /**
     * 关联的交易对代码
     */
    private String tradeCode;

    private String tradeName;
}