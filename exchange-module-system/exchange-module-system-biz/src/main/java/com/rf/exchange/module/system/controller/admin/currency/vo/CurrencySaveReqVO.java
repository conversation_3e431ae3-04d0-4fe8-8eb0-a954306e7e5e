package com.rf.exchange.module.system.controller.admin.currency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 系统货币新增/修改 Request VO")
@Data
public class CurrencySaveReqVO {

    @Schema(description = "币种编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2579")
    private Long id;

    @Schema(description = "币种名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "币种名称不能为空")
    private String name;

    @Schema(description = "货币代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "货币代码不能为空")
    private String code;

    @Schema(description = "币种符号如$，¥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "币种符号如$，¥不能为空")
    private String symbol;

    @Schema(description = "货币类型 0:法币 1:加密货币", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "货币类型 0:法币 1:加密货币不能为空")
    private Integer type;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "排序", example = "1")
    private Integer sort = 0;

}