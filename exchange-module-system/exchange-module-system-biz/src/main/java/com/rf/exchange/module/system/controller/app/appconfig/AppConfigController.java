package com.rf.exchange.module.system.controller.app.appconfig;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.i18n.core.I18nHelper;
import com.rf.exchange.framework.ratelimiter.core.annotation.RateLimiter;
import com.rf.exchange.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppConfigRespVO;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppCustomerServiceRespVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.appconfig.AppConfigService;
import com.rf.exchange.module.system.service.tenant.TenantService;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * APP/h5 初始化配置接口
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Tag(name = "用户APP - 配置")
@RestController
@RequestMapping("/system/config")
public class AppConfigController {

    @Resource
    private AppConfigService appConfigService;
    @Resource
    private TenantDictDataService tenantDictDataService;
    @Resource
    private TenantService tenantService;

    @Operation(summary = "获取App配置信息")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/list")
    public CommonResult<AppConfigRespVO> list() {
        AppConfigRespVO respVO = appConfigService.getAppConfigs(getTenantId());
        I18nHelper.getContent(respVO.getCountry(), AppConfigRespVO.Country.class);
        return success(respVO);
    }

    @Operation(summary = "获取App国家列表")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/list-country")
    public CommonResult<List<AppConfigRespVO.Country>> country() {
        List<AppConfigRespVO.Country> list = appConfigService.getAreaList();
        I18nHelper.getContent(list, AppConfigRespVO.Country.class);
        return success(list);
    }

    @Operation(summary = "获取App币种列表")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/list-currency")
    public CommonResult<List<AppConfigRespVO.Currency>> currency() {
        List<AppConfigRespVO.Currency> list = appConfigService.getCurrencyList();
        I18nHelper.getContent(list, AppConfigRespVO.Currency.class);
        return success(list);
    }

    @Operation(summary = "获取App语言列表")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/list-lang")
    public CommonResult<List<AppConfigRespVO.Language>> lang() {
        List<AppConfigRespVO.Language> list = appConfigService.getLangList();
        final String defaultLang = tenantService.getTenantDefaultLang(getTenantId());
        for (AppConfigRespVO.Language language : list) {
            language.setIsDefault(language.getCode().equals(defaultLang));
        }
        I18nHelper.getContent(list, AppConfigRespVO.Language.class);
        return success(list);
    }

    @Operation(summary = "获取App资产类型列表")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/list-asset-type")
    public CommonResult<List<AppConfigRespVO.TradeAssetType>> assetType() {
        List<AppConfigRespVO.TradeAssetType> list = appConfigService.getTradeTypeList(getTenantId());
        I18nHelper.getContent(list, AppConfigRespVO.TradeAssetType.class);
        return success(list);
    }

    @Operation(summary = "获取租户默认交易对")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/default-trade-pair")
    public CommonResult<AppConfigRespVO.DefaultTradePair> defaultTradePair() {
        return success(appConfigService.getDefaultTradePair(getTenantId()));
    }

    @Operation(summary = "获取客服地址列表")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    // 限流1分钟只能请求10次
    @GetMapping("/service-url-list")
    public CommonResult<List<AppCustomerServiceRespVO>> getCustomerServiceUrl() {
        List<TenantDictDataDO> list = tenantDictDataService.getDictDataList(0, "customer_service_config");
        List<AppCustomerServiceRespVO> ret = new ArrayList<>(list.size());
        for (TenantDictDataDO item : list) {
            ret.add(AppCustomerServiceRespVO.builder().serviceUrl(item.getValue()).ico(item.getRemark()).name(item.getLabel()).build());
        }
        return success(ret);
    }
}
