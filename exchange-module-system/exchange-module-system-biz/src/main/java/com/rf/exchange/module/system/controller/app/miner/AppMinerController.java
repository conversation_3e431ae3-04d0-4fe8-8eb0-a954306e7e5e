package com.rf.exchange.module.system.controller.app.miner;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.miner.vo.*;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductCreateOrderReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductInfoReqVo;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerRedemptionOrderReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductIncome;
import com.rf.exchange.module.system.service.miner.MinerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 挖矿")
@RestController
@RequestMapping("system/miner")
public class AppMinerController {

    @Resource
    private MinerService minerService;

    @PostMapping("/query")
    @Operation(summary = "获得挖矿产品分页")
    public CommonResult<PageResult<MinerProductRespVO>> getSmsChannelPage(@Valid @RequestBody MinerProductPageReqVO pageVO) {
        PageResult<MinerProduct> pageResult = minerService.getMinerPage(pageVO);
        return success(BeanUtils.toBean(pageResult, MinerProductRespVO.class));
    }

    @PostMapping("/create/order")
    @Operation(summary = "创建挖矿订单")
    public CommonResult<Boolean> createOrder(@Valid @RequestBody MinerProductCreateOrderReqVo createReqVO) {
        return success(minerService.createOrder(createReqVO));
    }

    @PostMapping("/order/redemption")
    @Operation(summary = "挖矿订单赎回")
    public CommonResult<Boolean> orderRedemption(@Valid @RequestBody MinerRedemptionOrderReqVo createReqVO) {
        return success(minerService.redemptionOrder(createReqVO));
    }

    @PostMapping("/my/order")
    @Operation(summary = "我的挖矿订单")
    public CommonResult<PageResult<MinerProductOrderResp>> myOrder(@Valid @RequestBody MinerOrderPageReqVo pageVO) {
        return success(minerService.appMinerPageOrders(pageVO));
    }

    @PostMapping("/my/order/income")
    @Operation(summary = "挖矿订单收益")
    public CommonResult<PageResult<MinerProductIncomeResp>> myOrderIncome(@Valid @RequestBody MinerProductIncomePageReqVO pageVO) {
        PageResult<MinerProductIncome> pageResult = minerService.appMinerPageIncome(pageVO);
        return success(BeanUtils.toBean(pageResult, MinerProductIncomeResp.class));
    }


    @PostMapping("/info")
    @Operation(summary = "我的挖矿产品详情")
    public CommonResult<MinerProductRespVO> minerInfo(@Valid @RequestBody MinerProductInfoReqVo reqVo) {
        MinerProduct minerInfo = minerService.getMinerInfo(reqVo);
        return success(BeanUtils.toBean(minerInfo, MinerProductRespVO.class));
    }


}
