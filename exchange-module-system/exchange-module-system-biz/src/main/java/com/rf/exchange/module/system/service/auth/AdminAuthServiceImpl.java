package com.rf.exchange.module.system.service.auth;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.util.servlet.ServletUtils.getClientIP;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

import java.time.Duration;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.google.auth.Authenticator;
import com.rf.exchange.framework.common.util.monitor.TracerUtils;
import com.rf.exchange.framework.common.util.servlet.ServletUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.rf.exchange.module.system.api.sms.SmsCodeApi;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.controller.admin.auth.vo.*;
import com.rf.exchange.module.system.convert.auth.AuthConvert;
import com.rf.exchange.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.module.system.enums.logger.LoginLogTypeEnum;
import com.rf.exchange.module.system.enums.logger.LoginResultEnum;
import com.rf.exchange.module.system.enums.oauth2.OAuth2ClientConstants;
import com.rf.exchange.module.system.service.logger.LoginLogService;
import com.rf.exchange.module.system.service.member.MemberService;
import com.rf.exchange.module.system.service.oauth2.OAuth2TokenService;
import com.rf.exchange.module.system.service.user.AdminUserService;
import com.xingyuv.captcha.service.CaptchaService;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;

/**
 * Auth Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private MemberService memberService;
    @Resource
    private Validator validator;
    @Resource
    private CaptchaService captchaService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private TenantDictDataApi tenantDictDataApi;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${exchange.captcha.enable:false}")
    private Boolean captchaEnable;

    @Override
    public AdminUserDO authenticate(String username, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }

        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        // 图形验证码校验验证码
        // validateCaptcha(reqVO);
        // 使用谷歌验证码进行验证
        validateGoogleCode(reqVO.getUsername(), reqVO.getAuthCode());
        // 使用账号密码，进行登录
        AdminUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    @Override
    public void sendSmsCode(AuthSmsSendReqVO reqVO) {
        // 登录场景，验证是否存在
        if (userService.getUserByMobile(reqVO.getMobile()) == null) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }
        // 发送验证码
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }

    @Override
    public AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) {
        // FIXME 校验验证码
        // smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.ADMIN_MEMBER_LOGIN.getScene(),
        // getClientIP()));

        // 获得用户信息
        AdminUserDO user = userService.getUserByMobile(reqVO.getMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_MOBILE);
    }

    private void createLoginLog(Long userId, String username, LoginLogTypeEnum logTypeEnum,
        LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, ServletUtils.getClientIP());
        }
    }

    // @VisibleForTesting
    // void validateCaptcha(AuthLoginReqVO reqVO) {
    // // 如果验证码关闭，则不进行校验
    // if (!captchaEnable) {
    // return;
    // }
    // // 校验验证码
    // ValidationUtils.validate(validator, reqVO, AuthLoginReqVO.CodeEnableGroup.class);
    // CaptchaVO captchaVO = new CaptchaVO();
    // captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
    // ResponseModel response = captchaService.verification(captchaVO);
    // // 验证不通过
    // if (!response.isSuccess()) {
    // // 创建登录失败日志（验证码不正确)
    // createLoginLog(null, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.CAPTCHA_CODE_ERROR);
    // throw exception(AUTH_LOGIN_CAPTCHA_CODE_ERROR, response.getRepMsg());
    // }
    // }

    private AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(),
            OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO =
            oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
            reqDTO.setUsername(getUsername(userId));
        } else {
            reqDTO.setUsername(memberService.getMemberUserMobile(userId));
        }
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(Long userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUser(userId);
        return user != null ? user.getUsername() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }

    @Override
    public AuthValidBindingGoogleSecretRespVO validBindingGoogleSecret(String username, String sessionId) {
        AuthValidBindingGoogleSecretRespVO resp = new AuthValidBindingGoogleSecretRespVO();
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (Objects.isNull(user)) {
            throw exception(ACCOUNT_IS_ERROR);
        }
        // 查看配置，是否开启谷歌验证登录,未开启直接返回
        Boolean enableGoogleSecret = tenantDictDataApi.getTenantGoogleSecretConfig(TenantContextHolder.getTenantId());
        if (!enableGoogleSecret) {
            resp.setEnableGoogleValidator(Boolean.FALSE);
            return resp;
        }
        resp.setEnableGoogleValidator(Boolean.TRUE);
        // 判断用户是否已绑定谷歌密钥，未绑定谷歌密钥，则生成绑定的二维码
        if (StrUtil.isBlank(user.getSecret())) {
            resp.setBinding(Boolean.FALSE);
            String secretKey = Authenticator.getSecretKey();
            String qrCodeText = Authenticator.getQrCodeText(secretKey, user.getUsername(), null);
            // 生成图片并转base64
            String base64GoogleSecretQr =
                "data:image/png;base64," + Base64.encode(QrCodeUtil.generatePng(qrCodeText, 400, 400));
            resp.setBase64GoogleSecretQr(base64GoogleSecretQr);
            // 随机uuid转大写加上sessionId组成authorization 返回
            String authorization = IdUtil.fastSimpleUUID().toUpperCase() + sessionId;
            resp.setAuthorization(authorization);

            // 缓存密钥信息
            // sessionId与username关系 5分钟
            String usernameCacheKey = this.getUsernameCacheKey(authorization);
            RBucket<String> usernameBucket = this.redissonClient.getBucket(usernameCacheKey);
            usernameBucket.set(username, Duration.ofMinutes(5L));

            // username与googleSecret关系 5分钟
            String googleSecretCacheKey = this.getGoogleSecretCacheKey(username);
            RBucket<String> googleSecretBucket = this.redissonClient.getBucket(googleSecretCacheKey);
            googleSecretBucket.set(secretKey, Duration.ofMinutes(5L));

        } else {
            resp.setBinding(Boolean.TRUE);
        }
        return resp;

    }

    @Override
    public boolean bindingGoogleSecret(String authorization, String googleCode) {

        String usernameCacheKey = this.getUsernameCacheKey(authorization);
        RBucket<String> usernameBucket = this.redissonClient.getBucket(usernameCacheKey);
        if (!usernameBucket.isExists() || StringUtils.isEmpty(usernameBucket.get())) {
            throw exception(GOOGLE_SECRET_BINDING_EXPIRED);
        }
        String username = usernameBucket.get();
        String googleSecretCacheKey = this.getGoogleSecretCacheKey(username);
        RBucket<String> googleSecretBucket = this.redissonClient.getBucket(googleSecretCacheKey);
        if (!googleSecretBucket.isExists() || StringUtils.isEmpty(googleSecretBucket.get())) {
            throw exception(GOOGLE_SECRET_BINDING_EXPIRED);
        }
        String googleSecret = googleSecretBucket.get();
        // 验证 验证通过进行绑定
        if (!Authenticator.checkCode(googleSecret, transformCode(googleCode), System.currentTimeMillis())) {
            throw exception(GOOGLE_CODE_ERROR);
        }
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (Objects.isNull(user)) {
            throw exception(ACCOUNT_IS_ERROR);
        }
        // 保存用户谷歌密钥
        return userService.updateUserSecret(user.getId(), googleSecret);
    }

    private String getUsernameCacheKey(String authorization) {
        return AdminAuthService.class.getSimpleName() + ":getUsernameCacheKey:" + TenantContextHolder.getTenantId()
            + ":" + authorization;
    }

    private String getGoogleSecretCacheKey(String username) {
        return AdminAuthService.class.getSimpleName() + ":getGoogleSecretCacheKey:" + TenantContextHolder.getTenantId()
            + ":" + username;
    }

    private void validateGoogleCode(String username, String googleCode) {
        // 是否开启谷歌验证码验证
        if (!tenantDictDataApi.getTenantGoogleSecretConfig(TenantContextHolder.getTenantId())) {
            return;
        }
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (Objects.isNull(user)) {
            throw exception(ACCOUNT_IS_ERROR);
        }
        // 为空代表没绑定
        if (StringUtils.isEmpty(user.getSecret())) {
            throw exception(GOOGLE_SECRET_IS_NOT_BINDING);
        }
        // 验证 谷歌验证码
        if (!Authenticator.checkCode(user.getSecret(), transformCode(googleCode), System.currentTimeMillis())) {
            throw exception(GOOGLE_CODE_ERROR);
        }
    }

    private Long transformCode(String googleCode) {
        try {
            return Long.parseLong(googleCode);
        } catch (Exception e) {
            throw exception(GOOGLE_CODE_ERROR);
        }

    }
}
