package com.rf.exchange.module.system.controller.app.miner.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "app - 查看我的挖矿订单")
@Data
public class MinerOrderPageReqVo extends PageParam {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String orderNo;

    @Schema(description = "订单状态 1-锁单 2提前赎回 3自动结束", requiredMode = Schema.RequiredMode.REQUIRED, example = "文字内容")
    private Integer orderStatus;

    @Schema(description = "创建时间")
    private Long[] createTime;

    @Schema(description = "用户ID")
    private Long userId;

}
