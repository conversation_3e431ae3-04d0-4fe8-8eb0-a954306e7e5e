package com.rf.exchange.module.system.controller.admin.miner;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductRespVO;
import com.rf.exchange.module.system.controller.admin.sms.vo.channel.SmsChannelPageReqVO;
import com.rf.exchange.module.system.controller.admin.sms.vo.channel.SmsChannelRespVO;
import com.rf.exchange.module.system.controller.admin.sms.vo.channel.SmsChannelSaveReqVO;
import com.rf.exchange.module.system.controller.admin.sms.vo.channel.SmsChannelSimpleRespVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerProductInfoReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.sms.SmsChannelDO;
import com.rf.exchange.module.system.service.miner.MinerService;
import com.rf.exchange.module.system.service.sms.SmsChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 挖矿系统")
@RestController
@RequestMapping("system/miner")
public class MinerController {

    @Resource
    private MinerService minerService;

    @PostMapping("/create")
    @Operation(summary = "创建挖矿产品")
    @PreAuthorize("@ss.hasPermission('system:miner:create')")
    public CommonResult<Boolean> create(@Valid @RequestBody MinerProductReqVO createReqVO) {
        return success(minerService.createMinerProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新挖矿产品")
    @PreAuthorize("@ss.hasPermission('system:miner:update')")
    public CommonResult<Boolean> update(@Valid @RequestBody MinerProductReqVO updateReqVO) {
        return success(minerService.updateMinerProduct(updateReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除挖矿产品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:miner:delete')")
    public CommonResult<Boolean> deleteMinerProduct(@RequestParam("id") Long id) {
        return success(minerService.deleteMinerProduct(id));
    }

    @PostMapping("/page")
    @Operation(summary = "获得挖矿产品分页")
    @PreAuthorize("@ss.hasPermission('system:miner:query')")
    public CommonResult<PageResult<MinerProductRespVO>> getMinerPage(@Valid @RequestBody MinerProductPageReqVO pageVO) {
        PageResult<MinerProduct> pageResult = minerService.getMinerPage(pageVO);
        return success(BeanUtils.toBean(pageResult, MinerProductRespVO.class));
    }

    @PostMapping("/info")
    @Operation(summary = "获得挖矿详情")
    @PreAuthorize("@ss.hasPermission('system:miner:info')")
    public CommonResult<MinerProductRespVO> getMinerInfo(@Valid @RequestBody MinerProductInfoReqVo pageVO) {
        MinerProduct minerInfo = minerService.getMinerInfo(pageVO);
        return success(BeanUtils.toBean(minerInfo, MinerProductRespVO.class));
    }


}
