package com.rf.exchange.module.system.config;

import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.util.tenantdict.TenantDictUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024-07-12
 */
@Configuration
public class SystemConfiguration {

    @Bean
    @SuppressWarnings("InstantiationOfUtilityClass")
    public TenantDictUtils tenantDictUtils(TenantDictDataApi dictDataApi) {
        TenantDictUtils.init(dictDataApi);
        return new TenantDictUtils();
    }

}
