package com.rf.exchange.module.system.service.tenantdict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.collection.CollectionUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.api.permission.RoleApi;
import com.rf.exchange.module.system.api.tenant.TenantApi;
import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveBatchReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.dal.mysql.tenantdict.TenantDictDataMapper;
import com.rf.exchange.module.system.dal.redis.tenant.TenantDictDataRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.DICT_DATA_VALUE_DUPLICATE;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.TENANT_DICT_DATA_NOT_EXISTS;

/**
 * 租户字典数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantDictDataServiceImpl implements TenantDictDataService {

    @Resource
    private TenantDictDataRedisDAO tenantDictDataRedisDAO;
    @Resource
    private TenantDictDataMapper tenantDictDataMapper;
    @Resource
    private TenantApi tenantApi;
    @Resource
    @Lazy
    private RoleApi roleApi;

    @Override
    public void validateDictDataList(Long tenantId, String dictType, Collection<String> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        Map<String, TenantDictDataDO> dictDataMap = CollectionUtils.convertMap(tenantDictDataMapper.selectByDictTypeAndValues(tenantId, dictType, values), TenantDictDataDO::getValue);
        // 校验
        values.forEach(value -> {
            TenantDictDataDO dictData = dictDataMap.get(value);
            if (dictData == null) {
                throw exception(TENANT_DICT_DATA_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(dictData.getStatus())) {
                throw exception(TENANT_DICT_DATA_NOT_EXISTS, dictData.getLabel());
            }
        });
    }

    @Override
    public TenantDictDataDO getDictData(Long tenantId, String dictType, String value) {
        return tenantDictDataMapper.selectByDictTypeAndValue(tenantId, dictType, value);
    }

    @Override
    public TenantDictDataDO parseDictData(Long tenantId, String dictType, String label) {
        return TenantUtils.execute(tenantId, () -> tenantDictDataMapper.selectByDictTypeAndLabel(tenantId, dictType, label));
    }

    @Override
    public List<TenantDictDataDO> getDictDataListByDictType(Long tenantId, String dictType) {
        final List<TenantDictDataDO> dataList = tenantDictDataRedisDAO.getDataList(tenantId, dictType);
        if (CollUtil.isNotEmpty(dataList)) {
            return dataList;
        }

        LambdaQueryWrapperX<TenantDictDataDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(TenantDictDataDO::getTenantId, tenantId);
        queryWrapper.eq(TenantDictDataDO::getDictType, dictType);
        queryWrapper.eq(TenantDictDataDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        List<TenantDictDataDO> list = tenantDictDataMapper.selectList(queryWrapper);
        list.sort(Comparator.comparing(TenantDictDataDO::getSort));

        tenantDictDataRedisDAO.saveDataList(tenantId, dictType, list);
        return list;
    }

    @Override
    public List<TenantDictDataDO> getDictDataList(Integer status, String dictType) {
        return tenantDictDataMapper.selectListByStatusAndDictType(status, dictType);
    }

    @Override
    @DSTransactional
    @Master
    public Long createTenantDictData(TenantDictDataSaveReqVO createReqVO) {
        LambdaQueryWrapperX<TenantDictDataDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TenantDictDataDO::getTenantId, createReqVO.getTenantId()).eq(TenantDictDataDO::getDictType, createReqVO.getDictType()).eq(TenantDictDataDO::getId, createReqVO.getLabel());
        long count = tenantDictDataMapper.selectCount(queryWrapperX);
        if (count > 0) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
        // 插入
        TenantDictDataDO tenantDictData = BeanUtils.toBean(createReqVO, TenantDictDataDO.class);
        tenantDictDataMapper.insert(tenantDictData);
        //如果是租户1新增，其它租户也要增加配置
        if (createReqVO.getTenantId() == 1) {
            //新增时，同时插入其它租户
            TenantUtils.executeIgnore(() -> {
                queryWrapperX.clear();
                queryWrapperX.eq(TenantDictDataDO::getLabel, createReqVO.getLabel()).eq(TenantDictDataDO::getDictType, tenantDictData.getDictType());
                List<TenantDictDataDO> allTenantTypeList = tenantDictDataMapper.selectList(queryWrapperX);
                List<Long> hasTypeTenantIdList = allTenantTypeList.parallelStream().map(TenantDictDataDO::getTenantId).toList();
                List<Long> tenantIdList = tenantApi.getTenantIdList();

                // 找出所有租户中没有新增的类型的租户
                List<Long> result = tenantIdList.stream()
                        .filter(num -> !hasTypeTenantIdList.contains(num))
                        .collect(Collectors.toList());
                result.forEach(t -> {
                    tenantDictData.setId(null);
                    tenantDictData.setTenantId(t);
                    tenantDictDataMapper.insert(tenantDictData);
                });
            });
        }
        // 返回
        return tenantDictData.getId();
    }

    @Override
    public void batchCreateTenantDictData(TenantDictDataSaveBatchReqVO saveReqVO, LoginUser loginUser) {
        final boolean isSuperAdmin = roleApi.hasAnySuperAdminOf(loginUser.getRoleIds());
        if (!isSuperAdmin) {
            throw exception(FORBIDDEN);
        }

        final List<TenantRespDTO> tenantList = tenantApi.getTenantList();
        if (CollectionUtil.isNotEmpty(tenantList)) {
            List<TenantDictDataDO> typeDataDOList = new ArrayList<>();
            for (TenantRespDTO tenantRespDTO : tenantList) {
                final TenantDictDataDO dataDO = new TenantDictDataDO();
                dataDO.setDictType(saveReqVO.getDictType());
                dataDO.setLabel(saveReqVO.getLabel());
                dataDO.setValue(saveReqVO.getValue());
                dataDO.setStatus(Integer.valueOf(saveReqVO.getStatus()));
                dataDO.setRemark(saveReqVO.getRemark());
                dataDO.setColorType(saveReqVO.getColorType());
                dataDO.setCssClass(saveReqVO.getCssClass());
                dataDO.setTenantId(tenantRespDTO.getId());
                typeDataDOList.add(dataDO);
            }
            tenantDictDataMapper.insertBatch(typeDataDOList);
        }
    }

    @Override
    @DSTransactional
    @Master
    public void updateTenantDictData(TenantDictDataSaveReqVO updateReqVO) {
        LambdaQueryWrapperX<TenantDictDataDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TenantDictDataDO::getTenantId, updateReqVO.getTenantId()).eq(TenantDictDataDO::getDictType, updateReqVO.getDictType()).eq(TenantDictDataDO::getId, updateReqVO.getLabel()).ne(TenantDictDataDO::getId, updateReqVO.getId());
        long count = tenantDictDataMapper.selectCount(queryWrapperX);
        if (count > 0) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
        // 校验存在
        TenantDictDataDO entity = validateTenantDictDataExists(updateReqVO.getId());
        // 更新
        TenantDictDataDO updateObj = BeanUtils.toBean(updateReqVO, TenantDictDataDO.class);
//        //如果是租户1修改的，其它租户也要修改配置
//        if (entity.getTenantId() == 1) {
//            TenantUtils.executeIgnore(() -> {
//                queryWrapperX.clear();
//                queryWrapperX.eq(TenantDictDataDO::getLabel, entity.getLabel()).eq(TenantDictDataDO::getDictType,updateReqVO.getDictType());
//                List<TenantDictDataDO> allTenantTypeList = tenantDictDataMapper.selectList(queryWrapperX);
//                allTenantTypeList.forEach(t -> {
//                    updateObj.setId(t.getId());
//                    tenantDictDataMapper.updateById(updateObj);
//                });
//            });
//        }
        tenantDictDataMapper.updateById(updateObj);
    }

    @Override
    @DSTransactional
    @Master
    public void deleteTenantDictData(Long id) {
        TenantDictDataDO entity = validateTenantDictDataExists(id);
        //如果是租户1删除的，其它租户也要删除
        if (entity.getTenantId() == 1) {
            TenantUtils.executeIgnore(() -> {
                LambdaQueryWrapperX<TenantDictDataDO> queryWrapperX = new LambdaQueryWrapperX<>();
                queryWrapperX.eq(TenantDictDataDO::getLabel, entity.getLabel()).eq(TenantDictDataDO::getDictType, entity.getDictType());
                tenantDictDataMapper.delete(queryWrapperX);
            });
        }
        // 删除
        tenantDictDataMapper.deleteById(id);
    }

    private TenantDictDataDO validateTenantDictDataExists(Long id) {
        TenantDictDataDO entity = tenantDictDataMapper.selectById(id);
        if (entity == null) {
            throw exception(TENANT_DICT_DATA_NOT_EXISTS);
        }
        return entity;
    }

    @Override
    public TenantDictDataDO getTenantDictData(Long id) {
        return tenantDictDataMapper.selectById(id);
    }

    @Override
    public PageResult<TenantDictDataDO> getTenantDictDataPage(TenantDictDataPageReqVO pageReqVO) {
        return tenantDictDataMapper.selectPage(pageReqVO);
    }

}