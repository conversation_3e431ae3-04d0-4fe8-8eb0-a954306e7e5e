package com.rf.exchange.module.system.controller.app.agreement.vo;

import com.rf.exchange.framework.i18n.annotation.I18n;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 协议类型 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Schema(description = "用户 App - 协议类型 Response VO")
@Data
public class AgreementTypeRespVO {

    @Schema(description = "协议类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "协议类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    @I18n
    private String name;

}
