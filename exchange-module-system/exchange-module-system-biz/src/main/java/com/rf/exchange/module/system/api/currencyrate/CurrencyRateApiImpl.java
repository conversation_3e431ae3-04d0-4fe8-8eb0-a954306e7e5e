package com.rf.exchange.module.system.api.currencyrate;

import com.fhs.trans.advice.ReleaseTransCacheAdvice;
import com.rf.exchange.module.system.api.currency.CurrencyApi;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
import com.rf.exchange.module.system.dal.redis.currencyrate.CurrencyRateRedisDAO;
import com.rf.exchange.module.system.enums.DictTypeConstants;
import com.rf.exchange.module.system.util.tenantdict.TenantDictUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_RATE;

/**
 * 系统汇率 API 实现类
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Service
public class CurrencyRateApiImpl implements CurrencyRateApi {

    @Resource
    private CurrencyRateRedisDAO currencyRateRedisDAO;
    @Resource
    private TenantDictDataApi tenantDictDataApi;
    @Resource
    private CurrencyApi currencyApi;
    @Autowired
    private ReleaseTransCacheAdvice releaseTransCacheAdvice;

    @Override
    public Collection<CurrencyRateDTO> getCurrencyRateList() {
        Map<String, CurrencyRateDTO> all = currencyRateRedisDAO.getAll();
        return all.values();
    }

    @Override
    public Collection<CurrencyRateDTO> getTenantCurrencyRateList(Long tenantId) {
        // key格式为基础货币/报价货币，比如美元/日元
        Map<String, CurrencyRateDTO> allRate = currencyRateRedisDAO.getAll();
        // 租户字典中配置的固定汇率 字典的key格式必须符合格式： 基础货币/报价货币
        List<TenantDictDataRespDTO> dictDataList =
            TenantDictUtils.getDictDataDtoList(tenantId, DictTypeConstants.TENANT_CURRENCY_RATE);
        Map<String, String> fixedRateMap = dictDataList.stream()
            .collect(Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));

        List<CurrencyRateDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, CurrencyRateDTO> entry : allRate.entrySet()) {
            CurrencyRateDTO rateDto = entry.getValue();
            String currencyPair = entry.getKey();
            // 如果租户设置了固定汇率则设置
            if (fixedRateMap.containsKey(currencyPair)) {
                String fixedRate = fixedRateMap.get(currencyPair);
                rateDto.setFixedRate(fixedRate);
            }
            resultList.add(rateDto);
        }
        return resultList;
    }

    @Override
    public CurrencyRateDTO getTenantCurrencyRate(Long tenantId, String baseCurrency, String quoteCurrency) {
        if (baseCurrency.equalsIgnoreCase(quoteCurrency)) {
            CurrencyRateDTO rate = new CurrencyRateDTO();
            rate.setBaseCurrency(baseCurrency);
            rate.setQuoteCurrency(quoteCurrency);
            rate.setFixedRate("1");
            rate.setRate(BigDecimal.ONE);
            return rate;
        }
        String aliasBaseCurrency = baseCurrency;
        if ("USDT".equalsIgnoreCase(baseCurrency)) {
            aliasBaseCurrency = "USD";
        }
        String aliasQuoteCurrency = quoteCurrency;
        if ("USDT".equalsIgnoreCase(quoteCurrency)) {
            aliasQuoteCurrency = "USD";
        }
        Collection<CurrencyRateDTO> rateList = getTenantCurrencyRateList(tenantId);
        for (CurrencyRateDTO rate : rateList) {
            if (rate.getBaseCurrency().equalsIgnoreCase(aliasBaseCurrency)
                && rate.getQuoteCurrency().equals(aliasQuoteCurrency)) {
                return rate;
            } else if (rate.getBaseCurrency().equalsIgnoreCase(aliasQuoteCurrency)
                && rate.getQuoteCurrency().equals(aliasBaseCurrency)) {
                // 如果基础货币和报价货币刚好相反则使用1除以汇率
                return getReversedCurrencyRateDTO(aliasQuoteCurrency, aliasBaseCurrency, rate);
            }
        }
        // 如果base和quote都不是USDT或者USD，则需要先转换成对USD的汇率
        if (!aliasBaseCurrency.equalsIgnoreCase("USD") && !aliasQuoteCurrency.equalsIgnoreCase("USD")) {
            // USD/baseCurrency汇率
            BigDecimal baseRateToUSD = getTenantCurrencyRateToUSD(tenantId, aliasBaseCurrency);
            if (null == baseRateToUSD) {
                return null;
            }
            // USD/quoteCurrency汇率
            BigDecimal quoteRateToUSD = getTenantCurrencyRateToUSD(tenantId, aliasQuoteCurrency);
            if (null == quoteRateToUSD) {
                return null;
            }
            CurrencyRateDTO rate = new CurrencyRateDTO();
            rate.setBaseCurrency(baseCurrency);
            rate.setQuoteCurrency(quoteCurrency);
            rate.setFixedRate("0");
            // 返回baseCurrency/quoteCurrency
            BigDecimal exchangeRate =
                BigDecimal.ONE.divide(baseRateToUSD, 4, RoundingMode.HALF_UP).multiply(quoteRateToUSD);
            rate.setRate(exchangeRate);
            return rate;
        }
        return null;
    }

    @Override
    public BigDecimal getTenantCurrencyRateToUSD(Long tenantId, String quoteCurrency) {
        if (quoteCurrency.equalsIgnoreCase("USD")) {
            return BigDecimal.ONE;
        }
        Collection<CurrencyRateDTO> rateList = getTenantCurrencyRateList(tenantId);
        for (CurrencyRateDTO rate : rateList) {
            if (rate.getQuoteCurrency().equalsIgnoreCase(quoteCurrency)
                && rate.getBaseCurrency().equalsIgnoreCase("USD")) {
                // 如果固定汇率不为0则表明租户设置了该法币和美金之间的固定汇率则需要使用固定汇率值
                if (!rate.getFixedRate().equals("0")) {
                    return new BigDecimal(rate.getFixedRate());
                }
                return rate.getRate();
            } else if (rate.getBaseCurrency().equalsIgnoreCase(quoteCurrency)
                && rate.getQuoteCurrency().equalsIgnoreCase("USD")) {
                CurrencyRateDTO reversedRateDTO =
                    getReversedCurrencyRateDTO(rate.getQuoteCurrency(), rate.getBaseCurrency(), rate);
                if (!reversedRateDTO.getFixedRate().equals("0")) {
                    return new BigDecimal(reversedRateDTO.getFixedRate());
                }
                return reversedRateDTO.getRate();
            }
        }
        return null;
    }

    private static CurrencyRateDTO getReversedCurrencyRateDTO(String baseCurrency, String quoteCurrency,
        CurrencyRateDTO rate) {
        CurrencyRateDTO reversedRate = new CurrencyRateDTO();
        reversedRate.setBaseCurrency(baseCurrency);
        reversedRate.setQuoteCurrency(quoteCurrency);
        if (!rate.getFixedRate().equals("0")) {
            BigDecimal reversedFixedRate =
                BigDecimal.ONE.divide(new BigDecimal(rate.getFixedRate()), 2, RoundingMode.HALF_UP);
            reversedRate.setFixedRate(reversedFixedRate.toPlainString());
        }
        reversedRate.setRate(BigDecimal.ONE.divide(rate.getRate(), 2, RoundingMode.HALF_UP));
        return reversedRate;
    }

    @Override
    public void updateCurrencyRateList(Collection<CurrencyRateDTO> dtoList) {
        currencyRateRedisDAO.set(dtoList);
    }

    @Override
    public void updateCurrencyRate(CurrencyRateDTO dto) {
        currencyRateRedisDAO.set(dto);
    }

    @Override
    public BigDecimal getTenantCurrencyRate(Long tenantId, String srcCurrencyCode) {
        // 对于用户来说，只有美元的余额，所以充值、提现到不同币种都是与美元进行计算
        Collection<CurrencyRateDTO> rateList = getTenantCurrencyRateList(tenantId);
        // 提交的币种对应USD的汇率
        Optional<CurrencyRateDTO> reqRate = rateList.stream().filter(r -> r.getQuoteCurrency().equals(srcCurrencyCode)).findFirst();
        if (reqRate.isEmpty()) {
            throw exception(CURRENCY_NOT_RATE);
        }
        return reqRate.get().getRateValue();
    }
}
