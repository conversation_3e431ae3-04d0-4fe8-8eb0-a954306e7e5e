package com.rf.exchange.module.system.dal.mysql.agent;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentStatisticDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-06-16
 */
@Mapper
public interface AgentStatisticMapper extends BaseMapperX<AgentStatisticDO> {

    @Update("update system_agent_statistic set register_count=register_count+1 where agent_id=#{id}")
    void incRegister(@Param("id")Long id);

    @Update("update system_agent_statistic set recharge_count=recharge_count+1 where agent_id=#{id}")
    void incRecharge(@Param("id")Long id);

    @Update("update system_agent_statistic set total_deposit_amount=total_deposit_amount+#{amount} where agent_id=#{id}")
    void incRechargeAmount(@Param("id")Long id, @Param("amount")BigDecimal amount);

    @Update("update system_agent_statistic set withdraw_count=withdraw_count+1 where agent_id=#{id}")
    void incWithdraw(@Param("id")Long id);

    @Update("update system_agent_statistic set total_withdraw_amount=total_withdraw_amount+#{amount} where agent_id=#{id}")
    void incWithdrawAmount(@Param("id")Long id, @Param("amount")BigDecimal amount);

@Update("update system_agent_statistic set total_balance=#{totalBalance} where agent_id=#{id}")
    void setTotalBalance(@Param("id")Long id,@Param("totalBalance")BigDecimal totalBalance);

    default PageResult<AgentStatisticDO> selectPage(AgentStatisticPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgentStatisticDO>()
                .likeIfPresent(AgentStatisticDO::getAgentName, reqVO.getAgentName())
                .orderByDesc(AgentStatisticDO::getId));
    }
}
