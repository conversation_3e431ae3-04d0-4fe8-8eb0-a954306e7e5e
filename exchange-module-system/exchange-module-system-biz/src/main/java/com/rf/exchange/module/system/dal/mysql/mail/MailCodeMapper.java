package com.rf.exchange.module.system.dal.mysql.mail;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.dal.dataobject.mail.MailCodeDO;
import com.rf.exchange.module.system.dal.dataobject.sms.SmsCodeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 邮箱验证码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MailCodeMapper extends BaseMapperX<MailCodeDO> {
    /**
     * 获得邮箱的最后一个手机验证码
     *
     * @param mail 邮箱
     * @param scene 发送场景，选填
     * @param code 验证码 选填
     * @return 手机验证码
     */
    default MailCodeDO selectLastByMail(String mail, String code, Integer scene) {
        return selectOne(new LambdaQueryWrapperX<MailCodeDO>().eq(MailCodeDO::getMail,mail)
                .eqIfPresent(MailCodeDO::getScene,scene)
                .eqIfPresent(MailCodeDO::getCode,code).orderByDesc(MailCodeDO::getId)
                .last("limit 1"));
    }

}