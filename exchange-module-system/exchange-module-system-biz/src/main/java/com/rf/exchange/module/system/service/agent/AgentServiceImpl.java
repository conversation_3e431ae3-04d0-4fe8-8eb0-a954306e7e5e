package com.rf.exchange.module.system.service.agent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.datapermission.core.util.DataPermissionUtils;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import com.rf.exchange.module.system.controller.admin.agent.vo.*;
import com.rf.exchange.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import com.rf.exchange.module.system.convert.agent.AgentConvert;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentStatisticDO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentTreeDO;
import com.rf.exchange.module.system.dal.dataobject.permission.RoleDO;
import com.rf.exchange.module.system.dal.dataobject.user.AdminUserDO;
import com.rf.exchange.module.system.dal.mysql.agent.AgentMapper;
import com.rf.exchange.module.system.dal.mysql.agent.AgentStatisticMapper;
import com.rf.exchange.module.system.dal.mysql.agent.AgentTreeMapper;
import com.rf.exchange.module.system.enums.permission.RoleCodeEnum;
import com.rf.exchange.module.system.service.permission.PermissionService;
import com.rf.exchange.module.system.service.permission.RoleService;
import com.rf.exchange.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.RandomUtil.BASE_CHAR_NUMBER_LOWER;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.system.enums.LogRecordConstants.*;
import static java.util.Collections.singleton;

/**
 * 代理 Service 实现
 *
 * <AUTHOR>
 * @since 2024-06-08
 */
@Service
@Valid
@Slf4j
public class AgentServiceImpl implements AgentService {

    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentTreeMapper agentTreeMapper;
    @Resource
    private AgentStatisticMapper agentStatisticMapper;
    @Resource
    private AdminUserService adminUserService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;

    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_AGENT_TYPE, subType = SYSTEM_AGENT_CREATE_SUB_TYPE, bizNo = "{{#agent.id}}",
            success = SYSTEM_AGENT_CREATE_SUCCESS)
    public Long createAgent(Long tenantId, AgentSaveReqVO createReqVO, AdminUserDO loginUser) {
        //如果不是默认代理必须有上级
        if (!createReqVO.isFirst()) {
            if (createReqVO.getAncestorId() == null || createReqVO.getAncestorId() <= 0) {
                throw exception(AGENT_ANCESTOR_NOT_AVAILABLE);
            }
        }
        final AgentDO ancestorAgent = validateAgentExists(createReqVO.getAncestorId());
        // 校验正确性
        validateAgentForCreateOrUpdate(null, createReqVO.getName(), createReqVO.getCode());
        // 插入代理
        AgentDO agent = BeanUtils.toBean(createReqVO, AgentDO.class);
        //如果有上级
        if (ancestorAgent != null) {
            agent.setAncestorId(ancestorAgent.getId());
        }
        // 如果没有手动输入的邀请码则自动生成邀请码
        if (StrUtil.isEmpty(agent.getCode())) {
            final String uniqueCode = genUniqueRandomCode();
            if (StrUtil.isEmpty(uniqueCode)) {
                throw exception(AGENT_INVITE_CODE_EXISTS);
            }
            agent.setCode(uniqueCode);
        }

        //----------------设置代理名称和登陆账号--------------------
        agent.setTenantId(tenantId);
        agent.setStatus(CommonStatusEnum.ENABLE.getStatus());
        if (agent.getAncestorId() > 0) {
            AgentDO ancestor = validateAgentExists(agent.getAncestorId());//agentMapper.selectById(updateObj.getAncestorId());
            agent.setAncestorName(ancestor.getName());
        }

//        AdminUserDO adminUserDO = adminUserService.getUser(agent.getUserId());
//        if (adminUserDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//        agent.setUserName(adminUserDO.getUsername());

        // 校验代理登录账号是否已经存在
        if (userService.validUsernameIsExist(createReqVO.getUsername())) {
            throw exception(USER_USERNAME_EXISTS, "代理");
        }

        // 如果不绑定到已经创建的后台用户账号则需要创建一个后台用户的账号作为代理的登录账号
        if (null != loginUser) {
            agent.setUserId(loginUser.getId());
            agent.setUserName(loginUser.getUsername());
        } else {
            // 创建代理登陆账号
            Long userId = createUser(agent.getTenantId(), createReqVO);
            agent.setUserId(userId);
            agent.setUserName(createReqVO.getUsername());
        }

        //----------------设置代理名称和登陆账号--------------------
        agentMapper.insert(agent);

        //----------------创建代理关系树--------------------
        //只有上级id大于0才需要复制层级关系
        if (agent.getAncestorId() > 0) {
            agentTreeMapper.insertAgentPath(agent.getId(), agent.getAncestorId());
        }
        agentTreeMapper.insertSelfLink(agent.getId());
        //----------------创建代理关系树--------------------


        //----------------创建代理统计记录--------------------
        AgentStatisticDO agentStatisticDO = new AgentStatisticDO();
        agentStatisticDO.setAgentId(agent.getId());
        agentStatisticDO.setAgentName(agent.getName());
        agentStatisticMapper.insert(agentStatisticDO);
        //----------------创建代理统计记录--------------------
        // 记录操作日志上下文
        LogRecordContext.putVariable("agent", agent);
        return agent.getId();
    }

    /**
     * 生成唯一随机邀请码
     * @return 邀请码
     */
    private String genUniqueRandomCode() {
        // 尝试生成5次
        for (int i = 0; i < 5; i++) {
            final String code = RandomUtil.randomString(BASE_CHAR_NUMBER_LOWER, 6);
            final AgentDO existsAgent = agentMapper.selectByCode(code);
            if (existsAgent == null) {
                return code;
            }
        }
        return null;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_AGENT_TYPE, subType = SYSTEM_AGENT_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
            success = SYSTEM_AGENT_UPDATE_SUCCESS)
    public void updateAgent(AgentSaveReqVO updateReqVO) {
//        // 邀请码不更新
//        updateReqVO.setCode(null);
//        // 校验正确性
//        AgentDO oldAgent = validateAgentForCreateOrUpdate(updateReqVO.getId(), updateReqVO.getCode(), updateReqVO.getCode());

        TenantUtils.executeIgnore(() -> {
            Integer existCount = agentMapper.checkCodeExist(updateReqVO.getCode(), updateReqVO.getId());
            if (existCount > 0) {
                throw exception(AGENT_INVITE_CODE_EXISTS);
            }
        });
        AgentDO oldAgent = agentMapper.selectById(updateReqVO.getId());
        //如果不是根代理，必须有上级
        if (oldAgent.getAncestorId() != null && oldAgent.getAncestorId() > 0) {
            if (updateReqVO.getAncestorId() <= 0) {
                throw exception(AGENT_ANCESTOR_NOT_AVAILABLE);
            }
        }
        // 更新代理
        AgentDO updateObj = BeanUtils.toBean(updateReqVO, AgentDO.class);

        //----------------设置代理名称和登陆账号--------------------
//        AdminUserDO adminUserDO = adminUserService.getUser(updateReqVO.getUserId());
//        if (adminUserDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//        updateObj.setUserName(adminUserDO.getUsername());

        //if (!Objects.equals(updateObj.getAncestorId(), oldAgent.getAncestorId())) {
        //    if (updateObj.getAncestorId() == 0) {
        //        updateObj.setAncestorName("");
        //    } else {
        //        AgentDO ancestor = validateAgentExists(updateObj.getAncestorId());//agentMapper.selectById(updateObj.getAncestorId());
        //        updateObj.setAncestorName(ancestor.getName());
        //    }
        //    //设置新的上级
        //    setAgentNewAncestor(updateObj.getId(), updateObj.getAncestorId());
        //}
        //----------------设置代理名称和登陆账号--------------------

        agentMapper.updateById(updateObj);
        // 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldAgent, AgentSaveReqVO.class));
        LogRecordContext.putVariable("agent", oldAgent);
    }

    @Override
    @Master
    @DSTransactional
    @LogRecord(type = SYSTEM_AGENT_TYPE, subType = SYSTEM_AGENT_UPDATE_STATUS_SUB_TYPE, bizNo = "{{#id}}",
            success = SYSTEM_AGENT_UPDATE_STATUS_SUCCESS)
    public void updateAgentStatus(Long id, Integer status) {
        // 校验代理存在
        validateAgentExists(id);
        // 更新状态
        AgentDO agent = new AgentDO();
        agent.setId(id);
        agent.setStatus(status);
        agentMapper.updateById(agent);
        // 记录操作日志上下文
        LogRecordContext.putVariable("agent", agent);
    }

    @Override
    @Master
    @DSTransactional
    @LogRecord(type = SYSTEM_AGENT_TYPE, subType = SYSTEM_AGENT_MOVE_SUB_TYPE, bizNo = "{{#moveReqVO.agentId}}",
            success = SYSTEM_AGENT_MOVE_SUCCESS)
    public boolean updateAgentAncestor(AgentAncestorUpdateReqVO moveReqVO, LoginUser loginUser) {
        final AgentDO willMovedAgentDO = validateAgentExists(moveReqVO.getAgentId());
        final AgentDO newAncestorAgentDO = validateAgentExists(moveReqVO.getNewAncestorId());
        // 旧父代理
        final AgentDO oldAncestorAgentDO = agentTreeMapper.selectAncestor(moveReqVO.getAgentId(), 1);
        if (oldAncestorAgentDO == null) {
            throw exception(AGENT_HAS_NOT_ANCESTOR);
        }
        // 如果新旧父代理相同则不执行任何操作
        if (Objects.equals(oldAncestorAgentDO.getId(), moveReqVO.getNewAncestorId())) {
            return true;
        }
        // 判断新的父代理是否是可以移动的代理节点
        final List<AgentSimpleInfoRespVO> availableAncestorList = getMoveAvailableList(loginUser, moveReqVO.getAgentId());
        final Set<Long> availableAncestorIds = availableAncestorList.stream().map(AgentSimpleInfoRespVO::getId).collect(Collectors.toSet());
        if (!availableAncestorIds.contains(moveReqVO.getNewAncestorId())) {
            throw exception(AGENT_ANCESTOR_NOT_AVAILABLE);
        }

        // 更新被移动代理system_agent表中的父代理id
        willMovedAgentDO.setAncestorId(newAncestorAgentDO.getId());
        willMovedAgentDO.setAncestorName(newAncestorAgentDO.getName());
        agentMapper.updateById(willMovedAgentDO);

        // 移除被移动代理之前的代理关系
        agentTreeMapper.deleteLeafAgent(moveReqVO.getAgentId());
        // 插入与新的父代理之间的关系
        agentTreeMapper.insertAgentPath(moveReqVO.getAgentId(), moveReqVO.getNewAncestorId());
        agentTreeMapper.insertSelfLink(moveReqVO.getAgentId());

        // 被移动代理的所有子代理 (不包括被移动的代理本身)
        final List<AgentTreeDO> descendantList = agentTreeMapper.selectDescendantPathList(moveReqVO.getAgentId(), 1);
        // 移除所有子代理的之前的路径关系
        for (AgentTreeDO agentTreeDO : descendantList) {
            agentTreeMapper.deleteGrandAncestor(agentTreeDO.getDescendant());
        }

        // 获取所有新的父代理的祖先代理路径关系（包括新的父代理本身）
        final List<AgentTreeDO> ancestorAgentList = agentTreeMapper.selectAncestorPathList(moveReqVO.getNewAncestorId(), 0);

        // 重做子节点与新的祖父代理的关系
        List<AgentTreeDO> newPathList = new ArrayList<>();
        for (AgentTreeDO agentTreeDO : descendantList) {
            for (AgentTreeDO ancestorAgentTreeDO : ancestorAgentList) {
                AgentTreeDO path = new AgentTreeDO();
                path.setAncestor(ancestorAgentTreeDO.getAncestor());
                path.setDescendant(agentTreeDO.getDescendant());
                path.setDept(agentTreeDO.getDept() + ancestorAgentTreeDO.getDept() + 1);
                newPathList.add(path);
            }
        }
        if (CollectionUtil.isNotEmpty(newPathList)) {
            agentTreeMapper.insertMoveAgentDescendantWithNewAncestorPath(newPathList);
        }

        // 日志操作上下文
        LogRecordContext.putVariable("oldAncestor", oldAncestorAgentDO.getName());
        LogRecordContext.putVariable("newAncestor", newAncestorAgentDO.getName());
        LogRecordContext.putVariable("agentName", willMovedAgentDO.getName());
        return true;
    }

    @Override
    @Master
    @DSTransactional
    @LogRecord(type = SYSTEM_AGENT_TYPE, subType = SYSTEM_AGENT_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = SYSTEM_AGENT_DELETE_SUCCESS)
    public void deleteAgent(Long id) {
        // 校验代理是否存在
        final AgentDO agent = validateAgentExists(id);
        //是否有子节点
        List<Long> descedantIdList = agentTreeMapper.selectAllDescendantIds(id);
        if (CollectionUtil.isNotEmpty(descedantIdList)) {
            throw exception(AGENT_HAS_DESCENDANT);
        }
        // 删除代理
        agentMapper.deleteById(id);
        // 更新代理的删除时间
        final AgentDO updateAgent = new AgentDO();
        updateAgent.setId(agent.getId());
        updateAgent.setDeleteTime(DateUtil.current());
        agentMapper.updateById(updateAgent);
        agentTreeMapper.deleteLeafAgent(id);
        // 删除代理账号对应的用户账号
        adminUserService.deleteUser(agent.getUserId());
        // 记录操作日志上下文
        LogRecordContext.putVariable("agent", agent);
    }

    @Override
    public PageResult<AgentRespVO> getAgentPage(AgentPageReqVO reqVO, LoginUser loginUser) {
        // 获取当前登录用户的角色
        boolean isSuperAdmin = roleService.hasAnySuperAdminOf(loginUser);

        // 子代理id的集合
        List<Long> descendantIdList = new ArrayList<>();
        // 只有管理员和超管可以查所有的代理层级关系，非管理员和超管只能查当前登录用户绑定的代理账号的子代理树
        if (isSuperAdmin) {
            TenantContextHolder.setIgnore(true);

            LambdaQueryWrapperX<AgentDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eqIfPresent(AgentDO::getStatus, reqVO.getStatus())
                    .eqIfPresent(AgentDO::getId, reqVO.getId())
                    .eqIfPresent(AgentDO::getUserId, reqVO.getUserId())
                    .eqIfPresent(AgentDO::getUserName, reqVO.getUsername())
                    .likeIfPresent(AgentDO::getName, reqVO.getName())
                    .likeIfPresent(AgentDO::getCode, reqVO.getCode())
                    .eqIfPresent(AgentDO::getAncestorId, reqVO.getAncestorId())
                    .likeIfPresent(AgentDO::getAncestorName, reqVO.getAncestorName())
                    .orderByDesc(AgentDO::getId);
            PageResult<AgentDO> pageResult = agentMapper.selectPage(reqVO, queryWrapper);

            TenantContextHolder.clear();
            return BeanUtils.toBean(pageResult, AgentRespVO.class);
        }
        // 如果是租户管理员则只能获取其租户下面的所有代理
        boolean isTenantAdmin = roleService.hasAnyTenantAdminOf(loginUser);
        Long tenantId = null;
        if (isTenantAdmin) {
            tenantId = loginUser.getTenantId();
            if (reqVO.getAncestorId() != null || StrUtil.isNotEmpty(reqVO.getAncestorName())) {
                int dept = reqVO.getDept() == null ? 0 : reqVO.getDept();
                if (reqVO.getDept() != null && dept > 0) {
                    descendantIdList = agentTreeMapper.selectDescendantIds(reqVO.getAncestorId(), dept);
                } else {
                    descendantIdList = agentTreeMapper.selectAllDescendantIds(reqVO.getAncestorId());
                }
            } else {
                final AgentDO defaultRootAgent = agentMapper.getDefault(loginUser.getTenantId());
                descendantIdList.add(defaultRootAgent.getId());
                //这里应该要显示的呢代理，无论在第几级
                //final List<AgentTreeDO> tenantAllDescendantList = agentTreeMapper.selectDescendantPathList(defaultRootAgent.getId(), 0);
                List<Long> tenantAllDescendantList = agentTreeMapper.selectAllDescendantIds(defaultRootAgent.getId());
                if (CollectionUtil.isNotEmpty(tenantAllDescendantList)) {
                    //descendantIdList.addAll(tenantAllDescendantList.stream().map(AgentTreeDO::getDescendant).collect(Collectors.toSet()));
                    descendantIdList.addAll(tenantAllDescendantList);
                }
            }
        } else if (loginUser.getAgentId() != null) {
            tenantId = loginUser.getTenantId();
            // 非超管账号只能查自己绑定代理账号的所有下级代理id
            descendantIdList = agentTreeMapper.selectAllDescendantIds(loginUser.getAgentId());
            descendantIdList.add(loginUser.getAgentId());
        }

        if (CollectionUtil.isNotEmpty(descendantIdList)) {
            LambdaQueryWrapperX<AgentDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eqIfPresent(AgentDO::getStatus, reqVO.getStatus())
                    .eqIfPresent(AgentDO::getId, reqVO.getId())
                    .eqIfPresent(AgentDO::getUserName, reqVO.getUsername())
                    .eqIfPresent(AgentDO::getUserId, reqVO.getUserId())
                    .likeIfPresent(AgentDO::getName, reqVO.getName()).likeIfPresent(AgentDO::getCode, reqVO.getCode())
                    .eqIfPresent(AgentDO::getAncestorId, reqVO.getAncestorId()).likeIfPresent(AgentDO::getAncestorName, reqVO.getAncestorName())
                    .inIfPresent(AgentDO::getId, descendantIdList)
                    .orderByDesc(AgentDO::getId);
            PageResult<AgentDO> pageResult = agentMapper.selectPage(reqVO, queryWrapper);
            TenantContextHolder.clear();
            return BeanUtils.toBean(pageResult, AgentRespVO.class);
        } else {
            TenantContextHolder.clear();
            return PageResult.empty();
        }
    }

    @Override
    public List<AgentBaseRespDTO> getUserMoveAvailableAgents(long agentId) {
        final AgentDO rootAgent = getRootAncestor(agentId);
        List<AgentDO> allDescendants = getAllDescendants(rootAgent.getId());
        return BeanUtils.toBean(allDescendants, AgentBaseRespDTO.class);
    }

    @Override
    public AgentDO getAgent(Long id) {
        return agentMapper.selectById(id);
    }

    @Override
    public Map<Long, AgentDO> getAgentsByIds(Set<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Map.of();
        }
        LambdaQueryWrapperX<AgentDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.in(AgentDO::getId, ids);
        final List<AgentDO> agentDOS = agentMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(agentDOS)) {
            return agentDOS.stream().collect(Collectors.toMap(AgentDO::getId, agentDO -> agentDO));
        }
        return Map.of();
    }

    @Override
    public List<AgentDO> getAllDescendants(Long ancestor) {
        return agentTreeMapper.selectAllDescendant(ancestor);
    }

    @Override
    public AgentDO getRootAncestor(Long agentId) {
        return agentTreeMapper.selectRootAncestor(agentId);
    }

    @Override
    public List<AgentDO> getAllSimpleList() {
        return agentMapper.selectList(new LambdaQueryWrapperX<AgentDO>().select(AgentDO::getId, AgentDO::getName, AgentDO::getCode, AgentDO::getStatus, BaseDO::getCreateTime));
    }

    @Override
    public List<AgentSimpleInfoRespVO> getMoveAvailableList(LoginUser loginUser, Long agentId) {
        // 验证被移动的代理是否存在
        final AgentDO willMovedAgentDO = validateAgentExists(agentId);
        // 被移动代理的父代理
        final AgentDO currentAncestor = agentTreeMapper.selectAncestor(agentId, 1);
        if (currentAncestor == null) {
            throw exception(AGENT_HAS_NOT_ANCESTOR);
        }
        // 获取所有相同租户下除了agentId自身以及它的子代理
        final List<Long> allAgentIds = agentMapper.selectAllAvailableAgentIds(willMovedAgentDO.getTenantId());
        final Set<Long> allAgentIdSet = new HashSet<>(allAgentIds);
        // 所有agentId的子孙代理
        final List<Long> descendantIds = agentTreeMapper.selectAllDescendantIds(agentId);
        descendantIds.forEach(allAgentIdSet::remove);
        allAgentIdSet.remove(agentId);

        LambdaQueryWrapperX<AgentDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.in(AgentDO::getId, allAgentIdSet);
        final List<AgentDO> agentDOList = agentMapper.selectList(wrapper);
        return AgentConvert.INSTANCE.convertList2(agentDOList);
    }

    @Override
    public List<AgentDO> getAllDescendants(Long ancestor, int dept) {
        return agentTreeMapper.selectDescendantByDept(ancestor, dept);
    }

    @Override
    public List<AgentDO> getAllAncestors(Long descendant) {
        return agentTreeMapper.selectAllAncestor(descendant);
    }

    @Override
    public Map<Long, AgentBaseRespDTO> getAncestorListByIds(Set<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new HashMap<>();
        }

        LambdaQueryWrapperX<AgentDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(AgentDO::getId, idList);
        List<AgentDO> agentList = agentMapper.selectList(queryWrapperX);
        List<AgentBaseRespDTO> agentBaseList = BeanUtils.toBean(agentList, AgentBaseRespDTO.class);
        return agentBaseList.stream().collect(Collectors.toMap(AgentBaseRespDTO::getId, agent -> agent));
    }

    @Override
    public Map<Long, AgentDO> getAncestorMapOfAgent(Long descendantId) {
        final List<Map<String, Object>> agents = agentTreeMapper.selectAncestorMapOfAgent(descendantId);
        TreeMap<Long, AgentDO> treeMap = new TreeMap<>(Comparator.reverseOrder());
        for (Map<String, Object> agentObj : agents) {
            long dept = (long) agentObj.get("dept");
            final AgentDO agentDO = BeanUtils.toBean(agentObj, AgentDO.class);
            treeMap.put(dept, agentDO);
        }
        return treeMap;
    }

    @Override
    public AgentDO getAncestorByDept(Long descendant, int dept) {
        return agentTreeMapper.selectAncestor(descendant, dept);
    }

    @Override
    public List<AgentDO> getAncestorsByDept(List<Long> descendants, int dept) {
        return agentTreeMapper.selectAncestorsByDescendants(descendants, dept);
    }

    @Override
    public int getDepth(Long descendant, Long ancestor) {
        return agentTreeMapper.selectDepth(descendant, ancestor);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void insertAgent(AgentDO descendant, AgentDO ancestor) {
        agentTreeMapper.insertAgentPath(descendant.getId(), ancestor.getId());
        agentTreeMapper.insertSelfLink(descendant.getId());
    }

    private Long createUser(Long tenantId, AgentSaveReqVO createReqVO) {
        // 创建用户
        UserSaveReqVO userSaveReqVO = AgentConvert.INSTANCE.convert(createReqVO);
        userSaveReqVO.setDeptId(102L);
        userSaveReqVO.setIsAgentUser(true);
        Long userId = userService.createUser(tenantId, userSaveReqVO);
        // 分配角色
        RoleDO role = roleService.getRoleByCode(RoleCodeEnum.TENANT_AGENT.getCode());
        if (role == null) {
            throw exception(ROLE_NOT_EXISTS);
        }
        permissionService.assignUserRole(userId, singleton(role.getId()));
        return userId;
    }

    private AgentDO validateAgentForCreateOrUpdate(Long id, String name, String code) {
        // 关闭数据权限，避免因为没有数据权限，查询不到数据，进而导致唯一校验不正确
        return DataPermissionUtils.executeIgnore(() -> {
            // 校验代理是否存在
            AgentDO agent = validateAgentExists(id);
            // 校验code唯一
            validateAgentCodeUnique(id, code);
            return agent;
        });
    }

    /**
     * 校验代理是否存在
     *
     * @param id 代理id
     * @return 代理对象
     */
    AgentDO validateAgentExists(Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        AgentDO agent = agentMapper.selectById(id);
        if (agent == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        return agent;
    }

    /**
     * 校验代理邀请码是否唯一
     *
     * @param id   代理id
     * @param code 邀请码
     */
    void validateAgentCodeUnique(Long id, String code) {
        if (StrUtil.isBlank(code)) {
            return;
        }
//        if (id == null) {
//            return;
//        }
        AgentDO agent = agentMapper.selectByCode(code);
//        if (agent == null) {
//            throw exception(AGENT_NOT_EXISTS);
//        }
        if (agent != null && !agent.getId().equals(id)) {
            throw exception(AGENT_INVITE_CODE_EXISTS);
        }
    }

    @Override
    public AgentDO getAgentByCode(String code) {
        AgentDO agentDO = agentMapper.selectByCode(code);
        if (agentDO == null) {
            agentDO = agentMapper.selectById(1);
        }
        return agentDO;
    }

    @Override
    public Long getAgentIdByUserId(Long userId) {
        return agentMapper.selectIdByUserId(userId);
    }

    @Override
    public List<Long> selectAllDescendantIdList(Long agentId) {
        return agentTreeMapper.selectAllDescendantIds(agentId);
    }


    @Override
    public AgentDO getDefault(Long tenantId) {
        return agentMapper.getDefault(tenantId);
    }

    @Override
    public List<AgentTreeDO> getAllTree() {
        return agentTreeMapper.selectList();
    }
}

