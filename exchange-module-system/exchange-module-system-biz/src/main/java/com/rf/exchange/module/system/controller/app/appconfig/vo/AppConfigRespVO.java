package com.rf.exchange.module.system.controller.app.appconfig.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.i18n.annotation.I18n;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppConfigRespVO {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "租户logo")
    private String tenantName;

    @Schema(description = "租户logo")
    private String tenantLogo;

    @Schema(description = "国家地区")
    private List<Country> country;

    @Schema(description = "支持语言")
    private List<Language> language;

    @Schema(description = "法币列表")
    private List<Currency> currency;

    @Schema(description = "资产类型")
    private List<TradeAssetType> assetType;

    @Schema(description = "汇率列表")
    private List<ExchangeRate> exchangeRates;

    @Schema(description = "租户的默认交易对")
    private DefaultTradePair defaultTradePair;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Language {

        @Schema(description = "语言名称", example = "English")
        private String localName;

        @Schema(description = "语言代码", example = "en")
        @I18n
        private String code;

        @Schema(description = "排序")
        private Integer sort;

        @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED, example = "86")
        private String ico;

        @Schema(description = "是否默认语言")
        private Boolean isDefault;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Country {

        @Schema(description = "国家id", requiredMode = Schema.RequiredMode.REQUIRED, example = "en")
        private Long areaId;

        @Schema(description = "国家代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "en")
        @I18n
        private String code;

        @Schema(description = "法定货币", requiredMode = Schema.RequiredMode.REQUIRED, example = "en")
        private String currency;

        @Schema(description = "手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "86")
        private String mobile;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Currency {
        @Schema(description = "币种代码", example = "CNY")
        @I18n
        private String code;

        @Schema(description = "币种符合", example = "¥")
        private String symbol;

        @Schema(description = "币种类型 0法币 1加密货币")
        private Integer type;

        @Schema(description = "币种排序")
        private Integer sort;

        @Schema(description = "是否是租户的默认法币币种")
        private Boolean isDefault;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeAssetType {

        @Schema(description = "资产类型 0加密货币 1股票 2大宗商品/贵金属 3外汇", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer type;

        @Schema(description = "名称 0加密货币 1股票 2大宗商品/贵金属 3外汇", requiredMode = Schema.RequiredMode.REQUIRED)
        @I18n
        private String name;

        @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer sort;

        @JsonIgnore
        private String nameI18n;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExchangeRate {
        @Schema(description = "基础货币")
        private String baseCurrency;

        @Schema(description = "报价货币")
        private String quoteCurrency;

        @Schema(description = "汇率")
        private String rate;

        //@Schema(description = "固定汇率")
        //private String fixedRate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DefaultTradePair {

        @Schema(description = "交易对id")
        private Long id;

        @Schema(description = "交易对代码")
        private String code;

        @Schema(description = "交易对名称")
        private String name;
    }
}
