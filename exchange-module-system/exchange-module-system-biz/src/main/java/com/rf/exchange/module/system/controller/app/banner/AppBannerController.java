package com.rf.exchange.module.system.controller.app.banner;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.system.api.dict.DictDataApi;
import com.rf.exchange.module.system.controller.app.banner.vo.AppBannerRespVO;
import com.rf.exchange.module.system.dal.dataobject.banner.BannerDO;
import com.rf.exchange.module.system.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 获取BANNER列表")
@RestController
@RequestMapping("/system/banner")
@Validated
public class AppBannerController {
    @Resource
    private BannerService bannerService;
    @Resource
    private CandleDataApi candleDataApi;
    @Resource
    private DictDataApi dictDataApi;

    @PermitAll
    @GetMapping("/get")
    @Operation(summary = "获取banner列表")
    public CommonResult<List<AppBannerRespVO>> get() {
        List<BannerDO> list = bannerService.getList();
        //获取bannerid和交易对编码字典
        Set<String> tradePairCodeSet = list.stream().map(BannerDO::getTradeCode)
                .filter(StringUtils::hasText).collect(Collectors.toSet());
        ;
        //通过交易对获取价格
        Map<String, CurrentPriceRespDTO> tradePriceMap = candleDataApi.getCurrentPriceListByIdSet(tradePairCodeSet);
        List<AppBannerRespVO> result = BeanUtils.toBean(list, AppBannerRespVO.class);
        result.forEach(banner -> {
            if (tradePriceMap.containsKey(banner.getTradeCode())) {
                CurrentPriceRespDTO priceRespDTO = tradePriceMap.get(banner.getTradeCode());
                banner.setCurrentPrice(priceRespDTO.getCurrentPrice());
                banner.setPercentage(NumberUtils.formatPercent(priceRespDTO.getPercentage().doubleValue(), 2));
            }
        });
        dictDataApi.fillS3Host(result,AppBannerRespVO.class);
        //FIXME 这边要优化
        //I18nHelper.getContent(result, AppBannerRespVO.class);
        result.sort((o1, o2) -> o1.getSort().compareTo(o2.getSort()));

        return success(result);
    }
}
