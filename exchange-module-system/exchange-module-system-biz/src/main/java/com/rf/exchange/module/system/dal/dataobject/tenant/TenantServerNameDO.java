package com.rf.exchange.module.system.dal.dataobject.tenant;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import lombok.*;

/**
 * 租户和域名的映射关系
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@TableName("system_tenant_server_name")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantServerNameDO extends BaseNoDeleteDO {
    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 租户id
     */
    private Long tenId;
    /**
     * 域名
     */
    private String serverName;
    /**
     * 启用状态 (0开启 1关闭)
     */
    private Integer status;
    /**
     * 是否作为分享链接域名
     */
    private boolean isShare;
}
