package com.rf.exchange.module.system.service.mail;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.mail.vo.account.MailAccountPageReqVO;
import com.rf.exchange.module.system.controller.admin.mail.vo.account.MailAccountSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import com.rf.exchange.module.system.dal.mysql.mail.MailAccountMapper;
import com.rf.exchange.module.system.dal.redis.RedisKeyConstants;
import com.rf.exchange.module.system.dal.redis.mail.MailAccountRedisDAO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.MAIL_ACCOUNT_NOT_EXISTS;

/**
 * 邮箱账号 Service 实现类
 *
 * 
 * @since 2022-03-21
 */
@Service
@Validated
@Slf4j
public class MailAccountServiceImpl implements MailAccountService {

    @Resource
    private MailAccountMapper mailAccountMapper;
    @Resource
    private MailAccountRedisDAO mailAccountRedisDAO;

    @Override
    public Long createMailAccount(MailAccountSaveReqVO createReqVO) {
        MailAccountDO account = BeanUtils.toBean(createReqVO, MailAccountDO.class);
        account.setTemplateCodes(createReqVO.getTemplateCodes());
        mailAccountMapper.insert(account);
        // 删除缓存
        mailAccountRedisDAO.delete(createReqVO.getTenantId());
        return account.getId();
    }

    @Override
    public void updateMailAccount(MailAccountSaveReqVO updateReqVO) {
        // 校验是否存在
        validateMailAccountExists(updateReqVO.getId());
        // 更新
        MailAccountDO updateObj = BeanUtils.toBean(updateReqVO, MailAccountDO.class);
        updateObj.setTemplateCodes(updateObj.getTemplateCodes());
        mailAccountMapper.updateById(updateObj);
        // 删除缓存
        mailAccountRedisDAO.delete(updateReqVO.getTenantId());
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.MAIL_ACCOUNT, key = "#id")
    public void deleteMailAccount(Long id) {
        // 校验是否存在账号
        final MailAccountDO mailAccount = validateMailAccountExists(id);
        // 删除
        if (mailAccountMapper.deleteById(id) > 0) {
            mailAccount.setDeleteTime(DateUtils.getUnixTimestampNow());
            mailAccountMapper.updateById(mailAccount);
        }
        // 删除缓存
        mailAccountRedisDAO.delete(mailAccount.getTenantId());
    }

    private MailAccountDO validateMailAccountExists(Long id) {
        final MailAccountDO mailAccountDO = mailAccountMapper.selectById(id);
        if (mailAccountDO == null) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
        return mailAccountDO;
    }

    @Override
    public MailAccountDO getMailAccount(Long id) {
        return mailAccountMapper.selectById(id);
    }

    @Override
    public MailAccountDO getMailAccountFromCache(Long id) {
        final MailAccountDO mailAccount = getMailAccount(id);
        mailAccountRedisDAO.set(mailAccount.getTenantId(), mailAccount);
        return mailAccount;
    }

    @Override
    public MailAccountDO getMailAccountByMail(String mail) {
        return mailAccountMapper.selectByMail(mail);
    }

    @Override
    public PageResult<MailAccountDO> getMailAccountPage(MailAccountPageReqVO pageReqVO) {
        return mailAccountMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MailAccountDO> getMailAccountListFromCache(Long tenantId, String domain) {
        final List<MailAccountDO> cachedAccountList = mailAccountRedisDAO.getAll(tenantId);
        if (CollectionUtil.isNotEmpty(cachedAccountList)) {
            return cachedAccountList;
        }
        final List<MailAccountDO> accountList = getMailAccountList(tenantId);
        mailAccountRedisDAO.setAll(tenantId, accountList);
        return accountList;
    }

    @Override
    public List<MailAccountDO> getMailAccountList(Long tenantId) {
        LambdaQueryWrapperX<MailAccountDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MailAccountDO::getTenantId, tenantId);
        return mailAccountMapper.selectList(wrapper);
    }
}
