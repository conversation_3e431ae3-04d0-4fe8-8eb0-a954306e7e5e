package com.rf.exchange.module.system.controller.admin.currency;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.system.controller.admin.currency.vo.*;
import com.rf.exchange.module.system.dal.dataobject.currency.CurrencyDO;
import com.rf.exchange.module.system.service.currency.CurrencyService;

@Tag(name = "管理后台 - 系统货币")
@RestController
@RequestMapping("/system/currency")
@Validated
public class CurrencyController {

    @Resource
    private CurrencyService currencyService;

    @PostMapping("/create")
    @Operation(summary = "创建系统货币")
    @PreAuthorize("@ss.hasPermission('system:currency:create')")
    public CommonResult<Long> createCurrency(@Valid @RequestBody CurrencySaveReqVO createReqVO) {
        return success(currencyService.createCurrency(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统货币")
    @PreAuthorize("@ss.hasPermission('system:currency:update')")
    public CommonResult<Boolean> updateCurrency(@Valid @RequestBody CurrencySaveReqVO updateReqVO) {
        currencyService.updateCurrency(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统货币")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:currency:delete')")
    public CommonResult<Boolean> deleteCurrency(@RequestParam("id") Long id) {
        currencyService.deleteCurrency(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统货币")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:currency:query')")
    public CommonResult<CurrencyRespVO> getCurrency(@RequestParam("id") Long id) {
        CurrencyDO currency = currencyService.getCurrency(id);
        return success(BeanUtils.toBean(currency, CurrencyRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得系统货币分页")
    @PreAuthorize("@ss.hasPermission('system:currency:query')")
    public CommonResult<PageResult<CurrencyRespVO>> getCurrencyPage(@Valid @RequestBody CurrencyPageReqVO pageReqVO) {
        PageResult<CurrencyDO> pageResult = currencyService.getCurrencyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CurrencyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统货币 Excel")
    @PreAuthorize("@ss.hasPermission('system:currency:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCurrencyExcel(@Valid CurrencyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CurrencyDO> list = currencyService.getCurrencyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "系统货币.xls", "数据", CurrencyRespVO.class,
                        BeanUtils.toBean(list, CurrencyRespVO.class));
    }

}