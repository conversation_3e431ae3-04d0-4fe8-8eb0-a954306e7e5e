package com.rf.exchange.module.system.framework.mail.core.client;

import com.rf.exchange.module.system.framework.mail.core.property.MailChannelProperties;

public interface MailClientFactory {
    /**
     * 获得短信 Client
     *
     * @param channelId 渠道编号
     * @return 短信 Client
     */
    MailClient getMailClient(Long channelId);

    /**
     * 获得短信 Client
     *
     * @param channelCode 渠道编码
     * @return 短信 Client
     */
    MailClient getMailClient(String channelCode);

    /**
     * 创建短信 Client
     *
     * @param properties 配置对象
     */
    void createOrUpdateMailClient(MailChannelProperties properties);
}
