package com.rf.exchange.module.system.controller.admin.lang.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 系统语种配置新增/修改 Request VO")
@Data
public class LangSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long id;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED,example = "FR")
    @NotEmpty(message = "编码不能为空")
    private String code;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "法语")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Short status;

    @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "图标不能为空")
    private String ico;
}