package com.rf.exchange.module.system.controller.admin.agent.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-06-08
 */
@Schema(description = "管理后台 - 代理 分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentPageReqVO extends PageParam {

    @Schema(description = "代理id", example = "1")
    private Long id;

    @Schema(description = "代理用户id")
    private String userId;

    @Schema(description = "代理账号")
    private String username;

    @Schema(description = "代理名称, 模糊匹配", example = "1")
    private String name;

    @Schema(description = "邀请码，模糊匹配", example = "1")
    private String code;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举", example = "1")
    private Integer status;

    @Schema(description = "上级代理id", example = "1")
    private Long ancestorId;

    @Schema(description = "上级代理名称", example = "abc")
    private String ancestorName;

    @Schema(description = "级差", example = "1")
    private Integer dept = -1;
}
