package com.rf.exchange.module.system.controller.admin.tenant.vo.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 租户字典数据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantDictDataRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3872")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "标签名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标签名")
    private String label;

    @Schema(description = "标签值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标签值")
    private String value;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private String dictType;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Short sort;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Short status;

    @Schema(description = "颜色类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("颜色类型")
    private String colorType;

    @Schema(description = "样式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("样式")
    private String cssClass;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}