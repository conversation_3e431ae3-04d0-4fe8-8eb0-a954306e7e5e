package com.rf.exchange.module.system.controller.admin.auth.vo;

import java.io.Serial;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 谷歌绑定验证返回参数
 * @author: Petter
 * @create: 2024-08-13
 **/
@Schema(description = "谷歌绑定验证返回参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthValidBindingGoogleSecretRespVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -6900305254251841869L;

    @Schema(description = "是否开启谷歌验证码 - 开启后判断是否绑定谷歌密钥")
    private Boolean enableGoogleValidator;

    @Schema(description = "是否绑定谷歌密钥 - 未绑定则展示绑定二维码")
    private Boolean binding;

    @Schema(description = "如果未绑定 返回的谷歌密钥二维码的base64编码")
    private String base64GoogleSecretQr;

    @Schema(description = "如果未绑定 返回的Authorization，调用绑定接口时需要传递此参数")
    private String authorization;
}
