package com.rf.exchange.module.system.dal.redis;

import com.rf.exchange.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    String PRIVATE_KEY_PREFIX = "exch:";

    /**
     * 用户收藏交易对的缓存
     * <p>
     * KEY 格式: member_favorite_trade_pair:{id}
     * VALUE 数据格式：String 模版信息 *
     */
    String SYSTEM_LANG_CONTENT =PRIVATE_KEY_PREFIX+ "system_lang_content";

    /**
     * 域名和租户id的缓存
     * <p>
     * KEY 格式：tenant_servername
     * VALUE 数据类型：String 租户编号
     */
    String TENANT_KEY_SERVERNAME = "tenant_servername:all";

    /**
     * 指定部门的所有子部门编号数组的缓存
     * <p>
     * KEY 格式：dept_children_ids:{id}
     * VALUE 数据类型：String 子部门编号集合
     */
    String DEPT_CHILDREN_ID_LIST = PRIVATE_KEY_PREFIX + "dept_children_ids";

    /**
     * 角色的缓存
     * <p>
     * KEY 格式：role:{id}
     * VALUE 数据类型：String 角色信息
     */
    String ROLE = PRIVATE_KEY_PREFIX + "role";

    /**
     * 用户拥有的角色编号的缓存
     * <p>
     * KEY 格式：user_role_ids:{userId}
     * VALUE 数据类型：String 角色编号集合
     */
    String USER_ROLE_ID_LIST = PRIVATE_KEY_PREFIX + "user_role_ids";

    /**
     * 拥有指定菜单的角色编号的缓存
     * <p>
     * KEY 格式：user_role_ids:{menuId}
     * VALUE 数据类型：String 角色编号集合
     */
    String MENU_ROLE_ID_LIST = PRIVATE_KEY_PREFIX + "menu_role_ids";

    /**
     * 拥有权限对应的菜单编号数组的缓存
     * <p>
     * KEY 格式：permission_menu_ids:{permission}
     * VALUE 数据类型：String 菜单编号数组
     */
    String PERMISSION_MENU_ID_LIST = PRIVATE_KEY_PREFIX + "permission_menu_ids";

    /**
     * OAuth2 客户端的缓存
     * <p>
     * KEY 格式：oauth_client:{id}
     * VALUE 数据类型：String 客户端信息
     */
    String OAUTH_CLIENT = PRIVATE_KEY_PREFIX + "oauth_client";

    /**
     * 访问令牌的缓存
     * <p>
     * KEY 格式：oauth2_access_token:{token}
     * VALUE 数据类型：String 访问令牌信息 {@link OAuth2AccessTokenDO}
     * <p>
     * 由于动态过期时间，使用 RedisTemplate 操作
     */
    String OAUTH2_ACCESS_TOKEN = PRIVATE_KEY_PREFIX + "oauth2_access_token:%s";

    /**
     * 站内信模版的缓存
     * <p>
     * KEY 格式：notify_template:{code}
     * VALUE 数据格式：String 模版信息
     */
    String NOTIFY_TEMPLATE = PRIVATE_KEY_PREFIX + "notify_template";

    /**
     * 邮件账号的缓存
     * <p>
     * KEY 格式：mail_account:{tenantId}
     * VALUE 数据格式：String 账号信息
     */
    String MAIL_ACCOUNT = PRIVATE_KEY_PREFIX + "mail_account";

    /**
     * 邮件模版的缓存
     * <p>
     * KEY 格式：mail_template:{code}
     * VALUE 数据格式：String 模版信息
     */
    String MAIL_TEMPLATE = PRIVATE_KEY_PREFIX + "mail_template";

    /**
     * 短信模版的缓存
     * <p>
     * KEY 格式：sms_template:{id}
     * VALUE 数据格式：String 模版信息
     */
    String SMS_TEMPLATE = PRIVATE_KEY_PREFIX + "sms_template";

    /**
     * 系统支持的法币
     *
     * <p>
     * KEY 格式：sms_template:{id}
     * VALUE 数据格式：String 模版信息
     */
    String SYS_CURRENCY = PRIVATE_KEY_PREFIX + "sys_currency";

    /**
     * 系统汇率
     */
    String SYS_CURRENCY_RATE = PRIVATE_KEY_PREFIX + "sys_currency_rate";

    /**
     * APP系统配置信息
     */
    String APP_CONFIG = PRIVATE_KEY_PREFIX + "app_config#30m";

    /**
     * APP的汇率信息
     */
    String APP_EXCHANGE_RATE = PRIVATE_KEY_PREFIX + "app_rate#10m";
}
