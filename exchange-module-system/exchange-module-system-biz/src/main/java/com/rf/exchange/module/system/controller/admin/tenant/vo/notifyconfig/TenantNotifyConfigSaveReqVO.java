package com.rf.exchange.module.system.controller.admin.tenant.vo.notifyconfig;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Schema(description = "管理后台 - 修改通知配置")
@Data
public class TenantNotifyConfigSaveReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3897")
    @Positive(message = "id不能为空")
    private Long id;
    @Schema(description = "标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "register")
    @NotNull(message = "标签不能为空")
    private String cmd;
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "注册通知的语音url")
    @NotNull(message = "名称不能为空")
    private String name;
    @Schema(description = "状态：0启用，1禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Short status;
    @Schema(description = "声音路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "http://asdfasf.com")
    private String url;
}
