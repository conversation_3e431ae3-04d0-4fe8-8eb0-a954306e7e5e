package com.rf.exchange.module.system.service.tenant;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNamePageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNameSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
public interface TenantServerNameService {
    /**
     * 创建系统租户域名
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantServerName(@Valid TenantServerNameSaveReqVO createReqVO);

    /**
     * 更新系统租户域名
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantServerName(@Valid TenantServerNameSaveReqVO updateReqVO);

    /**
     * 删除系统租户域名
     *
     * @param id 编号
     */
    void deleteTenantServerName(Long id);

    /**
     * 获得系统租户域名
     *
     * @param id 编号
     * @return 系统租户域名
     */
    TenantServerNameDO getTenantServerName(Long id);

    /**
     * 获得系统租户域名分页
     *
     * @param pageReqVO 分页查询
     * @return 系统租户域名分页
     */
    PageResult<TenantServerNameDO> getTenantServerNamePage(TenantServerNamePageReqVO pageReqVO);

    /**
     * 获取所有缓存的租户的servername
     *
     * @return 租户域名列表
     */
    List<TenantServerNameDO> getAllServerNameListFromCached();

    /**
     * 获取租户域名和租户id映射关系map
     * @return map
     */
    Map<String, Long> getServerNameCachedMap();
}
