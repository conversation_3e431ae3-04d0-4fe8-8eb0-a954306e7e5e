package com.rf.exchange.module.system.controller.admin.auth.vo;

import java.io.Serial;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @description: 谷歌绑定请求参数
 * @author: Petter
 * @create: 2024-08-13
 **/
@Data
public class AuthBindingGoogleSecretReqVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -4105467394991734159L;

    @Schema(description = "Authorization  google验证码开启需要回传", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Authorization 不能为空")
    private String authorization;

    @Schema(description = "google验证器上的验证码,验证码开启时，需要传递", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "googleCode 不能为空")
    @Length(min = 6, max = 6, message = "请输入6位的验证码")
    private String googleCode;
}
