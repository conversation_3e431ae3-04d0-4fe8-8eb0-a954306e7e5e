package com.rf.exchange.module.system.dal.dataobject.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 会员代理树 DO
 *
 * <AUTHOR>
 * @since 2024-06-08
 */
@TableName(value = "system_agent_tree", autoResultMap = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentTreeDO implements Serializable, TransPojo {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 祖先代理 ID
     */
    private Long ancestor;
    /**
     * 代理 ID
     */
    private Long descendant;
    /**
     * 级差
     */
    private Integer dept;
}
