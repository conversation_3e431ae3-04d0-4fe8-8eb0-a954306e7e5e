package com.rf.exchange.module.system.service.banner;

import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.rf.exchange.module.system.controller.admin.banner.vo.*;
import com.rf.exchange.module.system.dal.dataobject.banner.BannerDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.system.dal.mysql.banner.BannerMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.*;

/**
 * banner Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BannerServiceImpl implements BannerService {

    @Resource
    private BannerMapper bannerMapper;
    @Resource
    private TradePairApi tradePairApi;

    @Override
    public Long createBanner(BannerSaveReqVO createReqVO) {
        // 插入
        BannerDO banner = BeanUtils.toBean(createReqVO, BannerDO.class);
        if (StringUtils.hasText(createReqVO.getTradeCode())) {
            banner.setTradeName(tradePairApi.getNameByCode(createReqVO.getTradeCode()));
        }
        bannerMapper.insert(banner);
        // 返回
        return banner.getId();
    }

    @Override
    public void updateBanner(BannerSaveReqVO updateReqVO) {
        // 校验存在
        validateBannerExists(updateReqVO.getId());
        // 更新
        BannerDO updateObj = BeanUtils.toBean(updateReqVO, BannerDO.class);
        if (StringUtils.hasText(updateObj.getTradeCode())) {
            updateObj.setTradeName(tradePairApi.getNameByCode(updateObj.getTradeCode()));
        }
        //如果交易对是null,updateById不会更新，所以置空
        if (!StringUtils.hasText(updateObj.getTradeCode())) {
            updateObj.setTradeCode("");
        }
        bannerMapper.updateById(updateObj);
    }

    @Override
    public void deleteBanner(Long id) {
        // 校验存在
        validateBannerExists(id);
        // 删除
        bannerMapper.deleteById(id);
    }

    private void validateBannerExists(Long id) {
        if (bannerMapper.selectById(id) == null) {
            throw exception(BANNER_NOT_EXISTS);
        }
    }

    @Override
    public BannerDO getBanner(Long id) {
        return bannerMapper.selectById(id);
    }

    @Override
    public PageResult<BannerDO> getBannerPage(BannerPageReqVO pageReqVO) {
        return bannerMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BannerDO> getList() {
        return bannerMapper.getList();
    }
}