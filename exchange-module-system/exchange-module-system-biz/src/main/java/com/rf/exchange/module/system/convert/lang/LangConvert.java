package com.rf.exchange.module.system.convert.lang;

import com.rf.exchange.module.system.api.lang.dto.LangRespDTO;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Mapper
public interface LangConvert {

    LangConvert instance = Mappers.getMapper(LangConvert.class);

    LangRespDTO convert(LangDO lang);

    List<LangRespDTO> convertList(List<LangDO> lang);
}
