package com.rf.exchange.module.system.api.mail;

import com.rf.exchange.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import com.rf.exchange.module.system.service.mail.MailSendService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

/**
 * 邮件发送 API 实现类
 */
@Service
@Validated
public class MailSendApiImpl implements MailSendApi {

    @Resource
    private MailSendService mailSendService;

    @Override
    public Long sendSingleMailToAdmin(MailSendSingleToUserReqDTO reqDTO) {
        return mailSendService.sendSingleMailToAdmin(reqDTO.getMail(), reqDTO.getLang(),
                reqDTO.getUserId(), reqDTO.getTenantId(), reqDTO.getTemplateCode(), reqDTO.getDomain(), reqDTO.getTemplateParams());
    }

    @Override
    public Long sendSingleMailToMember(MailSendSingleToUserReqDTO reqDTO) {
        return mailSendService.sendSingleMailToMember(reqDTO.getMail(), reqDTO.getLang(),
                reqDTO.getUserId(), reqDTO.getTenantId(), reqDTO.getTemplateCode(), reqDTO.getDomain(), reqDTO.getTemplateParams());
    }

}
