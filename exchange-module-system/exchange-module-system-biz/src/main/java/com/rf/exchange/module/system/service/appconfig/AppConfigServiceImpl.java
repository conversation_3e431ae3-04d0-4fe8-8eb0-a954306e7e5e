package com.rf.exchange.module.system.service.appconfig;

import com.rf.exchange.framework.i18n.I;
import com.rf.exchange.module.exc.api.tradeassettype.TradeAssetTypeApi;
import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairTenantApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.system.api.areainfo.AreaInfoApi;
import com.rf.exchange.module.system.api.areainfo.dto.AreaInfoDTO;
import com.rf.exchange.module.system.api.currency.CurrencyApi;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.system.api.lang.LangApi;
import com.rf.exchange.module.system.api.lang.dto.LangRespDTO;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppConfigRespVO;
import com.rf.exchange.module.system.convert.appconfig.AppConfigConvert;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import com.rf.exchange.module.system.dal.redis.RedisKeyConstants;
import com.rf.exchange.module.system.service.tenant.TenantService;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CachePut;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class AppConfigServiceImpl implements AppConfigService {

    @Resource
    private AreaInfoApi areaInfoApi;
    @Resource
    private CurrencyApi currencyApi;
    @Resource
    private LangApi langApi;
    @Resource
    private TradeAssetTypeApi tradeAssetTypeApi;
    @Resource
    private CurrencyRateApi currencyRateApi;
    @Resource
    private TradePairTenantApi tradePairTenantApi;
    @Resource
    private TenantService tenantService;
    @Resource
    @Lazy
    private TenantDictDataApi tenantDictDataApi;

    @Override
    @CachePut(cacheNames = RedisKeyConstants.APP_CONFIG, key = "'all'", unless = "#result == null")
    public AppConfigRespVO getAppConfigs(Long tenantId) {
        AppConfigRespVO resp = new AppConfigRespVO();
        List<CurrencyBaseRespDTO> currencyList = currencyApi.getCurrencyList();
        List<AreaInfoDTO> areaInfoList = areaInfoApi.getAreaInfoList();
        List<LangRespDTO> langList = langApi.getLanguages();
        List<TenantTradeAssetTypeRespDTO> tradeTypeList = tradeAssetTypeApi.getTradeTypes(tenantId);
        TradePairRespDTO defaultTradePairDTO = tradePairTenantApi.getDefaultTradePair(tenantId);

        AppConfigRespVO.DefaultTradePair defaultTradePair = AppConfigConvert.INSTANCE.convert4(defaultTradePairDTO);
        List<AppConfigRespVO.Country> countries = AppConfigConvert.INSTANCE.convertList(areaInfoList);
        List<AppConfigRespVO.Currency> currencies = AppConfigConvert.INSTANCE.convertList2(currencyList);
        List<AppConfigRespVO.Language> languages = AppConfigConvert.INSTANCE.convertList3(langList);
        // 获取租户的默认语言数据字典配置
        final String defaultLanguageCode = tenantService.getTenantDefaultLang(tenantId);
        for (AppConfigRespVO.Language language : languages) {
            language.setIsDefault(language.getCode().equals(defaultLanguageCode));
        }
        List<AppConfigRespVO.TradeAssetType> tradeAssetTypes = AppConfigConvert.INSTANCE.convertList4(tradeTypeList);
        for (AppConfigRespVO.TradeAssetType assetType : tradeAssetTypes) {
            assetType.setName(I.n(assetType.getNameI18n()));
        }
        // 租户的默认显示的法币币种
        String defaultCurrencyCode = tenantDictDataApi.getTenantCurrencyCode(tenantId);
        for (AppConfigRespVO.Currency currency : currencies) {
            currency.setIsDefault(currency.getCode().equalsIgnoreCase(defaultCurrencyCode));
        }
        TenantDO tenantDo = tenantService.getTenant(tenantId);
        resp.setTenantId(tenantId);
        resp.setTenantLogo(tenantDo.getLogo());
        resp.setTenantName(tenantDo.getName());
        resp.setCountry(countries);
        resp.setCurrency(currencies);
        resp.setLanguage(languages);
        resp.setAssetType(tradeAssetTypes);
        resp.setExchangeRates(getCurrencyRates(tenantId));
        resp.setDefaultTradePair(defaultTradePair);
        return resp;
    }

    @Override
    @CachePut(cacheNames = RedisKeyConstants.APP_EXCHANGE_RATE, key = "#tenantId", unless = "#result == null || #result.isEmpty()")
    public List<AppConfigRespVO.ExchangeRate> getCurrencyRates(Long tenantId) {
        List<AppConfigRespVO.ExchangeRate> exchangeRates = new ArrayList<>();

        final List<CurrencyBaseRespDTO> enabledCurrencyList = currencyApi.getCurrencyList();
        Set<String> enabledCurrencyCodes = new HashSet<>();
        if (null != enabledCurrencyList) {
             enabledCurrencyCodes = enabledCurrencyList.stream().map(CurrencyBaseRespDTO::getCode).collect(Collectors.toSet());
        }

        Collection<CurrencyRateDTO> tenantCurrencyRateList = currencyRateApi.getTenantCurrencyRateList(tenantId);
        for (CurrencyRateDTO rateDTO : tenantCurrencyRateList) {
            if (!enabledCurrencyCodes.contains(rateDTO.getQuoteCurrency())) {
                continue;
            }
            final AppConfigRespVO.ExchangeRate exchangeRate = new AppConfigRespVO.ExchangeRate();
            exchangeRate.setRate(rateDTO.getFormattedRate());
            exchangeRate.setBaseCurrency(rateDTO.getBaseCurrency());
            exchangeRate.setQuoteCurrency(rateDTO.getQuoteCurrency());
            exchangeRates.add(exchangeRate);
        }

        return exchangeRates;
    }

    @Override
    public List<AppConfigRespVO.Currency> getCurrencyList() {
        List<CurrencyBaseRespDTO> currencyList = currencyApi.getCurrencyList();
        return AppConfigConvert.INSTANCE.convertList2(currencyList);
    }

    @Override
    public List<AppConfigRespVO.Country> getAreaList() {
        List<AreaInfoDTO> areaInfoList = areaInfoApi.getAreaInfoList();
        return AppConfigConvert.INSTANCE.convertList(areaInfoList);
    }

    @Override
    public List<AppConfigRespVO.Language> getLangList() {
        List<LangRespDTO> langList = langApi.getLanguages();
        return AppConfigConvert.INSTANCE.convertList3(langList);
    }

    @Override
    public List<AppConfigRespVO.TradeAssetType> getTradeTypeList(Long tenantId) {
        List<TenantTradeAssetTypeRespDTO> tradeTypeList = tradeAssetTypeApi.getTradeTypes(tenantId);
        return AppConfigConvert.INSTANCE.convertList4(tradeTypeList);
    }

    @Override
    public AppConfigRespVO.DefaultTradePair getDefaultTradePair(Long tenantId) {
        TradePairRespDTO dto = tradePairTenantApi.getDefaultTradePair(tenantId);
        return AppConfigConvert.INSTANCE.convert4(dto);
    }
}
