package com.rf.exchange.module.system.controller.admin.banner.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.module.system.enums.banner.BannerLocationEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - banner Response VO")
@Data
@ExcelIgnoreUnannotated
public class BannerRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25704")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图片")
    @ImageUrl
    private String img;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "链接路径 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.xxx.xx")
    @ExcelProperty("链接路径 ")
    private String linkUrl;

    @Schema(description = "链接类型，0内网，1外网", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("链接类型，0内网，1外网")
    private Integer linkType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "交易对编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对编码")
    private String tradeCode;
    @Schema(description = "位置数值", requiredMode = Schema.RequiredMode.REQUIRED)
    private int location;

    @Schema(description = "位置", requiredMode = Schema.RequiredMode.REQUIRED)
    public String getLocationStr() {
        return BannerLocationEnum.get(location).getLabel();
    }
}
