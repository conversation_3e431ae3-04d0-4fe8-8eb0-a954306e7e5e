package com.rf.exchange.module.system.service.mail;

import com.rf.exchange.module.system.mq.message.mail.MailSendMessage;

import java.util.Map;

/**
 * 邮件发送 Service 接口
 *
 * @since 2022-03-21
 */
public interface MailSendService {

    /**
     * 测试发送模版发送邮件
     *
     * @param mail           收件邮箱账号
     * @param lang
     * @param tenantId       租户id
     * @param templateCode   模板编码
     * @param domain
     * @param templateParams 模板参数
     * @return 发送日志编号
     */
    Long testSendMailTo(String mail, String lang, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams);

    /**
     * 发送单条邮件给管理后台的用户
     *
     * @param mail           收件邮箱
     * @param lang
     * @param userId         用户编码
     * @param tenantId       租户id
     * @param templateCode   邮件模版编码
     * @param domain
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMailToAdmin(String mail, String lang, Long userId,
                               Long tenantId, String templateCode, String domain, Map<String, Object> templateParams);

    /**
     * 发送单条邮件给用户 APP 的用户
     *
     * @param mail           收件邮箱
     * @param lang
     * @param userId         用户编码
     * @param tenantId       租户id
     * @param templateCode   邮件模版编码
     * @param domain
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMailToMember(String mail, String lang, Long userId,
                                Long tenantId, String templateCode, String domain, Map<String, Object> templateParams);

    /**
     * 发送单条邮件给用户
     *
     * @param mail           邮箱
     * @param lang           语言
     * @param userId         用户编码
     * @param userType       用户类型
     * @param tenantId       租户id
     * @param templateCode   邮件模版编码
     * @param domain         邮箱账号的域
     * @param templateParams 邮件模版参数
     * @return 发送日志编号
     */
    Long sendSingleMail(String mail, String lang, Long userId, Integer userType, Long tenantId, String templateCode, String domain, Map<String, Object> templateParams);

    /**
     * 执行真正的邮件发送
     * 注意，该方法仅仅提供给 MQ Consumer 使用
     *
     * @param message 邮件
     */
    void doSendMail(MailSendMessage message);

}
