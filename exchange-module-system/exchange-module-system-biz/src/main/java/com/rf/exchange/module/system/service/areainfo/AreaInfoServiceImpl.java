package com.rf.exchange.module.system.service.areainfo;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.dict.core.DictFrameworkUtils;
import com.rf.exchange.module.system.api.areainfo.dto.AreaInfoDTO;
import com.rf.exchange.module.system.api.dict.dto.DictDataRespDTO;
import com.rf.exchange.module.system.enums.DictTypeConstants;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运行地区的配置信息 service
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class AreaInfoServiceImpl implements AreaInfoService {

    @Override
    public List<AreaInfoDTO> getAreaInfoList() {
        List<AreaInfoDTO> resultList = new ArrayList<>();
        List<DictDataRespDTO> areaList = DictFrameworkUtils.getDictDataDtoList(DictTypeConstants.SUPPORTED_AREA);
        List<DictDataRespDTO> areaInfoList = DictFrameworkUtils.getDictDataDtoList(DictTypeConstants.AREA_INFO);
        Map<String, Integer> areaMap = areaList.stream().collect(Collectors.toMap(DictDataRespDTO::getLabel, dictDataRespDTO -> Integer.parseInt(dictDataRespDTO.getValue())));
        Map<String, String> areaInfoMap = areaInfoList.stream().collect(Collectors.toMap(DictDataRespDTO::getLabel, DictDataRespDTO::getValue));
        for (Map.Entry<String, Integer> entry : areaMap.entrySet()) {
            if (areaInfoMap.containsKey(entry.getKey())) {
                String json = areaInfoMap.get(entry.getKey());
                AreaInfoDTO areaInfoDTO = JsonUtils.parseObject(json, AreaInfoDTO.class);
                if (areaInfoDTO != null) {
                    areaInfoDTO.setCode(entry.getKey());
                    areaInfoDTO.setAreaId(entry.getValue());
                    resultList.add(areaInfoDTO);
                }
            }
        }
        return resultList;
    }
}
