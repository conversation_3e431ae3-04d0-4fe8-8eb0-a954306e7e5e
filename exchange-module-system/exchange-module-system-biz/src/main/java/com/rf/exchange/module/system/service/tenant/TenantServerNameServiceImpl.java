package com.rf.exchange.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNamePageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.servername.TenantServerNameSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import com.rf.exchange.module.system.dal.mysql.tenant.TenantServerNameMapper;
import com.rf.exchange.module.system.dal.redis.tenant.TenantServerNameRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.TENANT_SERVER_NAME_NOT_EXISTS;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
@Service
@Validated
public class TenantServerNameServiceImpl implements TenantServerNameService {

    @Resource
    private TenantServerNameMapper tenantServerNameMapper;
    @Resource
    private TenantServerNameRedisDAO tenantServerNameRedisDAO;

    @Override
    public Long createTenantServerName(TenantServerNameSaveReqVO createReqVO) {
        // 保证自增id
        createReqVO.setId(null);
        // 插入
        TenantServerNameDO tenantServerName = BeanUtils.toBean(createReqVO, TenantServerNameDO.class);
        tenantServerNameMapper.insert(tenantServerName);
        // 返回
        return tenantServerName.getId();
    }

    @Override
    public void updateTenantServerName(TenantServerNameSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantServerNameExists(updateReqVO.getId());
        // 更新
        TenantServerNameDO updateObj = BeanUtils.toBean(updateReqVO, TenantServerNameDO.class);
        tenantServerNameMapper.updateById(updateObj);
        tenantServerNameRedisDAO.delete();
    }

    @Override
    public void deleteTenantServerName(Long id) {
        // 校验存在
        validateTenantServerNameExists(id);
        // 删除
        tenantServerNameMapper.deleteById(id);
    }

    private void validateTenantServerNameExists(Long id) {
        if (tenantServerNameMapper.selectById(id) == null) {
            throw exception(TENANT_SERVER_NAME_NOT_EXISTS);
        }
    }

    @Override
    public TenantServerNameDO getTenantServerName(Long id) {
        return tenantServerNameMapper.selectById(id);
    }

    @Override
    public PageResult<TenantServerNameDO> getTenantServerNamePage(TenantServerNamePageReqVO pageReqVO) {
        return tenantServerNameMapper.selectPage(pageReqVO);
    }

    @Override
    @Slave
    public List<TenantServerNameDO> getAllServerNameListFromCached() {
        List<TenantServerNameDO> servernameList = tenantServerNameRedisDAO.get();
        if (CollUtil.isNotEmpty(servernameList)) {
            return servernameList;
        }
        final List<TenantServerNameDO> names = tenantServerNameMapper.selectServerNameList();
        tenantServerNameRedisDAO.set(names);
        return names;
    }

    @Override
    public Map<String, Long> getServerNameCachedMap() {
        List<TenantServerNameDO> servernameList = getAllServerNameListFromCached();
        Map<String, Long> map = new HashMap<>();
        for (TenantServerNameDO tenantServerNameDO : servernameList) {
            map.putIfAbsent(tenantServerNameDO.getServerName(), tenantServerNameDO.getTenId());
        }
        return map;
    }
}
