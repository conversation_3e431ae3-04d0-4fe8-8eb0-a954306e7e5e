package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 协议多语言保存 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Schema(description = "管理后台 - 协议多语言保存 Request VO")
@Data
public class AgreementMultiLangSaveReqVO {

    @Schema(description = "协议ID（更新时必填）", example = "1")
    private Long agreementId;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "协议类型不能为空")
    private Integer type;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "状态：0-禁用 1-启用", example = "1")
    private Integer status;

    @Schema(description = "生效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime effectiveTime;

    @Schema(description = "备注", example = "多语言协议")
    private String remark;

    @Schema(description = "多语言内容列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "多语言内容不能为空")
    @Valid
    private List<AgreementContentSaveReqVO> contents;

    /**
     * 协议内容保存请求VO
     */
    @Schema(description = "协议内容保存请求VO")
    @Data
    public static class AgreementContentSaveReqVO {

        @Schema(description = "内容ID（更新时填写）", example = "1")
        private Long id;

        @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
        @NotBlank(message = "语言代码不能为空")
        private String languageCode;

        @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
        @NotBlank(message = "协议标题不能为空")
        private String title;

        @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "协议内容不能为空")
        private String content;
    }

    // ========== 便利方法 ==========

    /**
     * 是否为新增操作
     */
    public boolean isCreate() {
        return this.agreementId == null;
    }

    /**
     * 是否为更新操作
     */
    public boolean isUpdate() {
        return this.agreementId != null;
    }

    /**
     * 获取语言代码列表
     */
    public List<String> getLanguageCodes() {
        if (contents == null) {
            return List.of();
        }
        return contents.stream()
                .map(AgreementContentSaveReqVO::getLanguageCode)
                .distinct()
                .toList();
    }

    /**
     * 根据语言代码获取内容
     */
    public AgreementContentSaveReqVO getContentByLanguage(String languageCode) {
        if (contents == null) {
            return null;
        }
        return contents.stream()
                .filter(content -> languageCode.equals(content.getLanguageCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 是否包含指定语言
     */
    public boolean hasLanguage(String languageCode) {
        return getContentByLanguage(languageCode) != null;
    }

    /**
     * 获取默认语言内容（优先中文，其次英文）
     */
    public AgreementContentSaveReqVO getDefaultContent() {
        if (contents == null || contents.isEmpty()) {
            return null;
        }
        
        // 优先返回中文
        AgreementContentSaveReqVO zhContent = getContentByLanguage("zh-CN");
        if (zhContent != null) {
            return zhContent;
        }
        
        // 其次返回英文
        AgreementContentSaveReqVO enContent = getContentByLanguage("en");
        if (enContent != null) {
            return enContent;
        }
        
        // 最后返回第一个
        return contents.get(0);
    }
}
