package com.rf.exchange.module.system.dal.dataobject.mail;

import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 邮件日志详情
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@TableName(value = "system_mail_log_detail", autoResultMap = true)
@Data
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailLogDetailDO extends BaseNoDeleteDO {
    /**
     * 日志编号，自增
     */
    private Long id;
    /**
     * 日志id
     */
    private Long logId;
    /**
     * 三方的消息id
     */
    private String messageId;
    /**
     * 三方的消息事件
     */
    private String event;
    /**
     * 三方的邮件事件消息
     */
    private String detail;
    /**
     * 收到事件的时间
     */
    private LocalDateTime time;
}
