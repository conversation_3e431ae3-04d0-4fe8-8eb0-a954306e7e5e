package com.rf.exchange.module.system.api.lang;

import com.rf.exchange.module.system.api.lang.dto.LangRespDTO;
import com.rf.exchange.module.system.convert.lang.LangConvert;
import com.rf.exchange.module.system.dal.dataobject.lang.LangDO;
import com.rf.exchange.module.system.service.lang.LangService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class LangApiImpl implements LangApi {

    @Resource
    private LangService langService;

    @Override
    public List<LangRespDTO> getLanguages() {
        List<LangDO> langList = langService.getLangList();
        return LangConvert.instance.convertList(langList);
    }
}
