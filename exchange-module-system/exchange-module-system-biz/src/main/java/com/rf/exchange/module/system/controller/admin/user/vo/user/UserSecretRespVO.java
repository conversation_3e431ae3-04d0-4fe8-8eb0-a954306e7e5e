package com.rf.exchange.module.system.controller.admin.user.vo.user;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 谷歌认证密钥及二维码内容")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSecretRespVO {
    @Schema(description = "密钥", example = "DSFEWFDFDFSFSDFSDF")
    private String secret;
    @Schema(description = "密钥二维码内容", example = "DSFEWFDFDFSFSDFSDF")
    private String qrcode;
}
