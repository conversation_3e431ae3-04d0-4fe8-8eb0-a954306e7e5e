package com.rf.exchange.module.system.dal.mysql.mail;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.query.QueryWrapperX;
import com.rf.exchange.module.system.controller.admin.mail.vo.account.MailAccountPageReqVO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MailAccountMapper extends BaseMapperX<MailAccountDO> {

    default PageResult<MailAccountDO> selectPage(MailAccountPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<MailAccountDO>()
                .likeIfPresent(MailAccountDO::getMail, pageReqVO.getMail())
                .likeIfPresent(MailAccountDO::getUsername , pageReqVO.getUsername()));
    }

    default MailAccountDO selectByMail(String mail) {
        LambdaQueryWrapperX<MailAccountDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MailAccountDO::getMail , mail);
        wrapper.eq(MailAccountDO::getDeleted, false);
        return selectOne(wrapper);
    }
}
