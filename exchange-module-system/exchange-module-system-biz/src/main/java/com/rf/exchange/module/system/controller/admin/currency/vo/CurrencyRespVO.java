package com.rf.exchange.module.system.controller.admin.currency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 系统货币 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CurrencyRespVO {

    @Schema(description = "币种编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2579")
    @ExcelProperty("币种编号")
    private Long id;

    @Schema(description = "币种名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("币种名称")
    private String name;

    @Schema(description = "货币代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("货币代码")
    private String code;

    @Schema(description = "币种符号如$，¥", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种符号如$，¥")
    private String symbol;

    @Schema(description = "货币类型 0:法币 1:加密货币", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("货币类型 0:法币 1:加密货币")
    private Integer type;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("开启状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}