package com.rf.exchange.module.system.controller.admin.miner;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductIncomePageReqVO;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductIncomeResp;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductPageReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductIncome;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;
import com.rf.exchange.module.system.service.miner.MinerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 挖矿订单")
@RestController
@RequestMapping("system/miner/order")
public class MinerOrderController {

    @Resource
    private MinerService minerService;

    @PostMapping("/page")
    @Operation(summary = "查询挖矿订单")
    @PreAuthorize("@ss.hasPermission('system:miner:order')")
    public CommonResult<PageResult<MinerProductIncomeResp>> getMinerOrder(@Valid @RequestBody MinerOrderPageReqVo pageVO) {
        PageResult<MinerProductOrder> pageResult = minerService.getMinerPageOrders(pageVO);
        return success(BeanUtils.toBean(pageResult, MinerProductIncomeResp.class));
    }

    @PostMapping("/page/income")
    @Operation(summary = "查询挖矿订单收益")
    @PreAuthorize("@ss.hasPermission('system:miner:order:income')")
    public CommonResult<PageResult<MinerProductIncomeResp>> getMinerIncome(@Valid @RequestBody MinerProductIncomePageReqVO pageVO) {
        PageResult<MinerProductIncome> pageResult = minerService.getMinerPageIncome(pageVO);
        return success(BeanUtils.toBean(pageResult, MinerProductIncomeResp.class));
    }


}
