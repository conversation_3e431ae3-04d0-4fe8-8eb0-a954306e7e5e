package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 挖矿收益列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MinerProductIncomePageReqVO extends PageParam {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "xs123")
    private String orderNo;

    @Schema(description = "创建时间")
    private Long[] createTime;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    private Long userId;

}
