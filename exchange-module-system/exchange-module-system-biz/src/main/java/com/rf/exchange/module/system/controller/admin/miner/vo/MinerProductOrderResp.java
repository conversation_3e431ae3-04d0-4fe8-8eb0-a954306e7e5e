package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 挖矿产品订单
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 挖矿订单返回")
@Data
public class MinerProductOrderResp {

    private Long id;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "xs123")
    private String orderNo;

    @Schema(description = "挖矿产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1912")
    private Long productId;

    @Schema(description = "产品标题")
    private String title;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String username;

    @Schema(description = "周期", requiredMode = Schema.RequiredMode.REQUIRED, example = "7")
    private Integer cycle;

    @Schema(description = "订单金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11.1")
    private BigDecimal orderAmount;

    @Schema(description = "累计收益", requiredMode = Schema.RequiredMode.REQUIRED, example = "128.8")
    private BigDecimal orderIncome;

    @Schema(description = "订单状态 1-锁单 2提前赎回 3自动结束", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer orderStatus;

    @Schema(description = "违约金", requiredMode = Schema.RequiredMode.REQUIRED, example = "22.2")
    private BigDecimal liquidatedDamages;

    @Schema(description = "违约金", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.2")
    private BigDecimal liquidatedDamagesRatio;

    @Schema(description = "到期时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1753348368377")
    private Long expireTime;

    @Schema(description = "到期时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-25")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate rewardDate;

    @Schema(description = "租户id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;
}