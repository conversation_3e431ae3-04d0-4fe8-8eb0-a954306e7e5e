package com.rf.exchange.module.system.controller.app.agreement;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementMultiLangRespVO;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementRespVO;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementSimpleRespVO;
import com.rf.exchange.module.system.controller.app.agreement.vo.AgreementTypeRespVO;
import com.rf.exchange.module.system.controller.admin.agreement.vo.AgreementContentRespVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import com.rf.exchange.module.system.enums.agreement.AgreementTypeEnum;
import com.rf.exchange.module.system.service.agreement.AgreementService;
import com.rf.exchange.framework.i18n.core.I18nHelper;
import com.rf.exchange.framework.i18n.I;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.AGREEMENT_NOT_EXISTS;

/**
 * 语言选项
 */
class LanguageOption {
    private String languageCode;
    private String languageDisplayName;
    private Boolean isCurrent;

    public LanguageOption() {}

    public LanguageOption(String languageCode, String languageDisplayName, Boolean isCurrent) {
        this.languageCode = languageCode;
        this.languageDisplayName = languageDisplayName;
        this.isCurrent = isCurrent;
    }

    public String getLanguageCode() { return languageCode; }
    public void setLanguageCode(String languageCode) { this.languageCode = languageCode; }

    public String getLanguageDisplayName() { return languageDisplayName; }
    public void setLanguageDisplayName(String languageDisplayName) { this.languageDisplayName = languageDisplayName; }

    public Boolean getIsCurrent() { return isCurrent; }
    public void setIsCurrent(Boolean isCurrent) { this.isCurrent = isCurrent; }
}

/**
 * 用户 App - 协议管理
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Tag(name = "用户 App - 协议管理")
@RestController
@RequestMapping("/system/agreement")
@Validated
public class AppAgreementController {

    @Resource
    private AgreementService agreementService;


    @GetMapping("/get-by-type")
    @Operation(summary = "根据类型获取协议", description = "自动根据请求头中的语言信息返回对应语言版本的协议内容，支持智能语言回退")
    @Parameter(name = "type", description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", required = true, example = "1")
    public CommonResult<?> getAgreementByType(
            @RequestParam("type") Integer type,
            @RequestParam(value = "simple", defaultValue = "false") Boolean simple) {

        // 获取当前租户ID和语言
        Long tenantId = TenantContextHolder.getTenantId();
        String language = I.getLang();

        // 获取协议内容，支持智能语言回退
        AgreementContentDO content = getAgreementContentWithFallback(tenantId, type, language);

        if (content == null) {
            throw exception(AGREEMENT_NOT_EXISTS);
        }

        AppAgreementSimpleRespVO simpleRespVO = new AppAgreementSimpleRespVO();
        simpleRespVO.setLanguageDisplayName(getLanguageDisplayName(content.getLanguageCode()));
        simpleRespVO.setTitle(content.getTitle());
        simpleRespVO.setContent(content.getContent());
        return success(simpleRespVO);

    }





    /**
     * 获取协议内容，支持智能语言回退
     * 回退顺序：指定语言 -> 中文 -> 英文 -> 任意可用语言
     */
    private AgreementContentDO getAgreementContentWithFallback(Long tenantId, Integer type, String language) {
        // 1. 尝试获取指定语言
        AgreementContentDO content = agreementService.getAgreementContent(tenantId, type, language);
        if (content != null) {
            return content;
        }

        // 2. 如果指定语言不是中文，尝试获取中文
        if (!"zh-CN".equals(language)) {
            content = agreementService.getAgreementContent(tenantId, type, "zh-CN");
            if (content != null) {
                return content;
            }
        }

        // 3. 如果中文不存在，尝试获取英文
        if (!"en".equals(language)) {
            content = agreementService.getAgreementContent(tenantId, type, "en");
            if (content != null) {
                return content;
            }
        }

        // 4. 如果都不存在，获取该协议的任意可用语言
        AgreementDO agreement = agreementService.getAgreementByTenantIdAndType(tenantId, type);
        if (agreement != null) {
            List<AgreementContentDO> contents = agreementService.getAgreementContents(agreement.getId());
            if (!contents.isEmpty()) {
                return contents.get(0); // 返回第一个可用的语言版本
            }
        }

        return null;
    }

    /**
     * 获取语言显示名称
     */
    private String getLanguageDisplayName(String languageCode) {
        if (languageCode == null) {
            return "";
        }
        return switch (languageCode) {
            case "zh-CN", "zh" -> "简体中文";
            case "zh-TW" -> "繁体中文";
            case "en", "en-US" -> "English";
            case "ja", "ja-JP" -> "日本語";
            case "ko", "ko-KR" -> "한국어";
            case "es" -> "Español";
            case "fr" -> "Français";
            case "de" -> "Deutsch";
            case "ru" -> "Русский";
            case "ar" -> "العربية";
            case "pt" -> "Português";
            case "it" -> "Italiano";
            case "th" -> "ไทย";
            case "vi" -> "Tiếng Việt";
            case "id" -> "Bahasa Indonesia";
            case "ms" -> "Bahasa Melayu";
            case "hi" -> "हिन्दी";
            default -> languageCode;
        };
    }
}
