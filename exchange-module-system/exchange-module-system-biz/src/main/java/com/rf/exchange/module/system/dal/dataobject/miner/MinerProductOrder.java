package com.rf.exchange.module.system.dal.dataobject.miner;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 挖矿产品表 DO
 *
 * <AUTHOR>
 */
@TableName(value = "miner_product_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MinerProductOrder extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单收益
     */
    private BigDecimal orderIncome;

    /**
     * 订单状态 1-锁单 2提前赎回 3自动结束
     */
    private Integer orderStatus;

    /**
     * 违约金
     */
    private BigDecimal liquidatedDamages;

    /**
     * 违约金比例
     */
    private BigDecimal liquidatedDamagesRatio;

    /**
     * 到期时间
     */
    private Long expireTime;

    /**
     * 奖励发送日期
     */
    private LocalDate rewardDate;
}