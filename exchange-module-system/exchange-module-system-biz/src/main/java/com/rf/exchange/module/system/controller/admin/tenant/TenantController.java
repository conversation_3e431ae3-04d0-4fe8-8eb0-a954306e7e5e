package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.image.ImageUrlUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantDO;
import com.rf.exchange.module.system.service.tenant.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.error;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 租户")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;


    @GetMapping("/get-id-by-code")
    @PermitAll
    @Operation(summary = "使用租户码，获得租户编号", description = "登录界面，根据用户的租户码，获得租户编号")
    @Parameter(name = "code", description = "租户码", required = true, example = "tenant01")
    public CommonResult<Long> getTenantIdByCode(@RequestParam("code") String code) {
        TenantDO tenant = tenantService.getTenantByCode(code);
        return success(tenant != null ? tenant.getId() : null);
    }

//    @GetMapping("/get-by-website")
//    @PermitAll
//    @Operation(summary = "使用域名，获得租户信息", description = "登录界面，根据用户的域名，获得租户信息")
//    @Parameter(name = "website", description = "域名", required = true, example = "www.xxxx.xx")
//    public CommonResult<TenantSimpleRespVO> getTenantByWebsite(@RequestParam("website") String website) {
//        TenantDO tenant = tenantService.getTenantByWebsite(website);
//        return success(BeanUtils.toBean(tenant, TenantSimpleRespVO.class));
//    }

    @PostMapping("/create")
    @Operation(summary = "创建租户")
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    @TenantIgnore
    public CommonResult<Long> createTenant(@Valid @RequestBody TenantSaveReqVO createReqVO) {
        if(!StringUtils.hasText(createReqVO.getFrontUrl())){
            return error(0,"域名为必填项");
        }
        return success(tenantService.createTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantSaveReqVO updateReqVO) {
        tenantService.updateTenant(updateReqVO);
        return success(true);
    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除租户")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
//    public CommonResult<Boolean> deleteTenant(@RequestParam("id") Long id) {
//        tenantService.deleteTenant(id);
//        return success(true);
//    }

    @GetMapping("/get")
    @Operation(summary = "获得租户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") Long id) {
        TenantDO tenant = tenantService.getTenant(id);

        return success(BeanUtils.toBean(tenant, TenantRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得租户分页")
    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid @RequestBody TenantPageReqVO pageVO) {
        PageResult<TenantDO> pageResult = tenantService.getTenantPage(pageVO);
        return success(BeanUtils.toBean(pageResult, TenantRespVO.class));
    }

    @GetMapping({"/simple-list"})
    @Operation(summary = "获取租户的精简信息列表")
    public CommonResult<List<TenantRespVO>> getList() {
        List<TenantDO> list = tenantService.getTenantList();
        return success(BeanUtils.toBean(list, TenantRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantExcel(@Valid TenantPageReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantDO> list = tenantService.getTenantPage(exportReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户.xls", "数据", TenantRespVO.class,
                BeanUtils.toBean(list, TenantRespVO.class));
    }

}
