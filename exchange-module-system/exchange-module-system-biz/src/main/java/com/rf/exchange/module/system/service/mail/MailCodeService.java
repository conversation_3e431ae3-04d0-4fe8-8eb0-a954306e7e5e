package com.rf.exchange.module.system.service.mail;

import com.rf.exchange.framework.common.exception.ServiceException;
import com.rf.exchange.module.system.api.mail.dto.MailCodeSendReqDTO;
import com.rf.exchange.module.system.api.mail.dto.MailCodeUseReqDTO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailCodeDO;
import jakarta.validation.Valid;

/**
 * 邮箱验证码 Service 接口
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface MailCodeService {

    /**
     * 创建邮箱验证码，并进行发送
     *
     * @param reqDTO 发送请求
     */
    void sendMailCode(@Valid MailCodeSendReqDTO reqDTO);

    /**
     * 检查验证码是否有效
     *
     * @param reqDTO 校验请求
     */
    MailCodeDO validateMailCode(@Valid MailCodeUseReqDTO reqDTO);

    /**
     * 验证邮箱验证码，并进行使用
     * 如果正确，则将验证码标记成已使用
     * 如果错误，则抛出 {@link ServiceException} 异常
     *
     * @param reqDTO 使用请求
     */
    void useMailCode(@Valid MailCodeUseReqDTO reqDTO);
}
