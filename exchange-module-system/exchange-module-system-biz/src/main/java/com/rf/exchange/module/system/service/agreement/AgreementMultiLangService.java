package com.rf.exchange.module.system.service.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.agreement.vo.*;
import com.rf.exchange.module.system.controller.app.agreement.vo.AppAgreementMultiLangRespVO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;

import java.util.List;

/**
 * 协议多语言 Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
public interface AgreementMultiLangService {

    /**
     * 创建/更新多语言协议
     *
     * @param createReqVO 创建/更新信息
     * @return 协议ID
     */
    Long saveMultiLangAgreement(AgreementMultiLangSaveReqVO createReqVO);

    /**
     * 删除多语言协议（包括所有语言版本）
     *
     * @param agreementId 协议ID
     */
    void deleteMultiLangAgreement(Long agreementId);

    /**
     * 获得多语言协议详情
     *
     * @param agreementId 协议ID
     * @return 多语言协议详情
     */
    AgreementMultiLangRespVO getMultiLangAgreement(Long agreementId);

    /**
     * 获得协议多语言内容分页
     *
     * @param pageReqVO 分页查询
     * @return 协议多语言内容分页
     */
    PageResult<AgreementContentRespVO> getAgreementContentPage(AgreementContentPageReqVO pageReqVO);

    /**
     * 根据协议ID获取所有语言版本
     *
     * @param agreementId 协议ID
     * @return 语言版本列表
     */
    List<AgreementContentDO> getAgreementContentsByAgreementId(Long agreementId);

    /**
     * 根据协议ID和语言代码获取内容
     *
     * @param agreementId  协议ID
     * @param languageCode 语言代码
     * @return 协议内容
     */
    AgreementContentDO getAgreementContentByLanguage(Long agreementId, String languageCode);

    /**
     * 批量创建协议内容
     *
     * @param agreementId 协议ID
     * @param contents    内容列表
     */
    void batchCreateAgreementContents(Long agreementId, List<AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO> contents);

    /**
     * 批量更新协议内容
     *
     * @param agreementId 协议ID
     * @param contents    内容列表
     */
    void batchUpdateAgreementContents(Long agreementId, List<AgreementMultiLangSaveReqVO.AgreementContentSaveReqVO> contents);

    /**
     * 删除协议的所有语言版本
     *
     * @param agreementId 协议ID
     */
    void deleteAgreementContents(Long agreementId);

    /**
     * 复制协议的多语言内容到新协议
     *
     * @param sourceAgreementId 源协议ID
     * @param targetAgreementId 目标协议ID
     */
    void copyAgreementContents(Long sourceAgreementId, Long targetAgreementId);

    /**
     * 获取协议的语言完整性报告
     *
     * @param tenantId 租户ID
     * @return 语言完整性报告
     */
    List<AgreementLanguageCompletenessVO> getLanguageCompletenessReport(Long tenantId);

    /**
     * 为指定协议添加缺失的语言版本（使用默认内容）
     *
     * @param agreementId   协议ID
     * @param languageCodes 要添加的语言代码列表
     */
    void addMissingLanguageVersions(Long agreementId, List<String> languageCodes);

    // ========== APP端接口 ==========

    /**
     * APP端根据类型和语言获取协议
     *
     * @param tenantId     租户ID
     * @param type         协议类型
     * @param languageCode 语言代码
     * @return 协议内容
     */
    AppAgreementMultiLangRespVO getAgreementByTypeAndLanguage(Long tenantId, Integer type, String languageCode);

    /**
     * APP端获取协议的所有可用语言
     *
     * @param tenantId 租户ID
     * @param type     协议类型
     * @return 可用语言列表
     */
    List<AppAgreementMultiLangRespVO.LanguageOption> getAvailableLanguages(Long tenantId, Integer type);

    /**
     * 获取系统支持的所有语言
     *
     * @return 支持的语言列表
     */
    List<AppAgreementMultiLangRespVO.LanguageOption> getSupportedLanguages();

    /**
     * 根据用户偏好语言获取最佳匹配的协议内容
     *
     * @param tenantId           租户ID
     * @param type               协议类型
     * @param preferredLanguages 用户偏好语言列表（按优先级排序）
     * @return 协议内容
     */
    AppAgreementMultiLangRespVO getBestMatchAgreement(Long tenantId, Integer type, List<String> preferredLanguages);

    /**
     * 语言完整性VO
     */
    class AgreementLanguageCompletenessVO {
        private Long agreementId;
        private Integer agreementType;
        private String agreementTitle;
        private Long tenantId;
        private List<String> availableLanguages;
        private List<String> missingLanguages;
        private Integer languageCount;
        private Double completenessPercentage;

        // getters and setters
        public Long getAgreementId() { return agreementId; }
        public void setAgreementId(Long agreementId) { this.agreementId = agreementId; }

        public Integer getAgreementType() { return agreementType; }
        public void setAgreementType(Integer agreementType) { this.agreementType = agreementType; }

        public String getAgreementTitle() { return agreementTitle; }
        public void setAgreementTitle(String agreementTitle) { this.agreementTitle = agreementTitle; }

        public Long getTenantId() { return tenantId; }
        public void setTenantId(Long tenantId) { this.tenantId = tenantId; }

        public List<String> getAvailableLanguages() { return availableLanguages; }
        public void setAvailableLanguages(List<String> availableLanguages) { this.availableLanguages = availableLanguages; }

        public List<String> getMissingLanguages() { return missingLanguages; }
        public void setMissingLanguages(List<String> missingLanguages) { this.missingLanguages = missingLanguages; }

        public Integer getLanguageCount() { return languageCount; }
        public void setLanguageCount(Integer languageCount) { this.languageCount = languageCount; }

        public Double getCompletenessPercentage() { return completenessPercentage; }
        public void setCompletenessPercentage(Double completenessPercentage) { this.completenessPercentage = completenessPercentage; }
    }
}
