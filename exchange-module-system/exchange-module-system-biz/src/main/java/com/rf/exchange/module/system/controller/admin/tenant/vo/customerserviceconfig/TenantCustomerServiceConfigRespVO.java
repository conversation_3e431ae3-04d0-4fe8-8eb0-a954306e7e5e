package com.rf.exchange.module.system.controller.admin.tenant.vo.customerserviceconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 客服配置")
@Data
@Builder
public class TenantCustomerServiceConfigRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long id;
    @Schema(description = "客服地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "http://google.com/")
    private String serviceUrl;
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "whatsapp")
    private String name;
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;
    @Schema(description = "图标路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://1000logos.net/wp-content/uploads/2021/04/WhatsApp-logo.png")
    private String ico;
}
