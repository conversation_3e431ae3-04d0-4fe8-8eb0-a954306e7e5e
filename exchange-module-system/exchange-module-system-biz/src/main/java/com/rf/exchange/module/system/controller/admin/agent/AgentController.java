package com.rf.exchange.module.system.controller.admin.agent;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.api.user.MemberUserApi;
import com.rf.exchange.module.member.api.user.dto.MemberUserTotalRespVO;
import com.rf.exchange.module.system.api.permission.RoleApi;
import com.rf.exchange.module.system.controller.admin.agent.vo.*;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.dal.dataobject.tenant.TenantServerNameDO;
import com.rf.exchange.module.system.service.agent.AgentService;
import com.rf.exchange.module.system.service.tenant.TenantServerNameService;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

/**
 * <AUTHOR>
 * @since 2024-06-08
 */
@Tag(name = "管理后台 - 代理")
@RestController
@RequestMapping("/system/agent")
@Validated
public class AgentController {

    @Resource
    private AgentService agentService;
    @Resource
    private TenantDictDataService tenantDictDataService;
    @Resource
    @Lazy
    private MemberUserApi memberUserApi;
    @Resource
    private TenantServerNameService tenantServerNameService;
    @Resource
    @Lazy
    private RoleApi roleApi;

    @PostMapping("/create")
    @Operation(summary = "新增代理")
    @PreAuthorize("@ss.hasPermission('system:agent:create')")
    public CommonResult<Long> createAgent(@Valid @RequestBody AgentSaveReqVO reqVO) {
        return success(agentService.createAgent(TenantContextHolder.getTenantId(), reqVO, null));
    }

    @PostMapping("/update")
    @Operation(summary = "更新代理")
    @PreAuthorize("@ss.hasPermission('system:agent:update')")
    public CommonResult<Boolean> updateAgent(@Valid @RequestBody AgentSaveReqVO reqVO) {
        agentService.updateAgent(reqVO);
        return success(true);
    }

    @GetMapping("/move/available-list")
    @Operation(summary = "获取有效的父代理")
    @Parameter(name = "id", description = "需要移动的代理id", required = true)
    @PreAuthorize("@ss.hasPermission('system:agent:update:ancestor')")
    public CommonResult<List<AgentSimpleInfoRespVO>> getMoveAvailableAgents(@RequestParam("id") Long ancestor) {
        return success(agentService.getMoveAvailableList(getLoginUser(), ancestor));
    }

    @PostMapping("/move/ancestor")
    @Operation(summary = "移动代理的父代理")
    @PreAuthorize("@ss.hasPermission('system:agent:update:ancestor')")
    public CommonResult<Boolean> moveAgentAncestor(@Valid @RequestBody AgentAncestorUpdateReqVO reqVO) {
        return success(agentService.updateAgentAncestor(reqVO, getLoginUser()));
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新代理状态")
    @PreAuthorize("@ss.hasPermission('system:agent:update')")
    public CommonResult<Boolean> updateAgentStatus(@Valid @RequestBody AgentUpdateStatusReqVO reqVO) {
        agentService.updateAgentStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @PostMapping("/page")
    @Operation(summary = "获得代理分页")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<PageResult<AgentRespVO>> getAgentPage(@Valid @RequestBody AgentPageReqVO pageVO) {
        PageResult<AgentRespVO> pageResult = agentService.getAgentPage(pageVO, getLoginUser());
        List<TenantServerNameDO> serverNameList = tenantServerNameService.getAllServerNameListFromCached();
        for (AgentRespVO item : pageResult.getList()) {
            List<TenantServerNameDO> serverNameDOS = serverNameList.stream().filter(s -> Objects.equals(s.getTenId(), item.getTenantId())).collect(Collectors.toList());
            List<String> urls = new ArrayList<>(serverNameDOS.size());
            for (TenantServerNameDO serverNameDO : serverNameDOS) {
                if (!serverNameDO.isShare()) {
                    continue;
                }
                final String shareUrl = "https://" + serverNameDO.getServerName() + "/#/?ic=" + item.getCode();
                urls.add(shareUrl);
            }
            item.setShareUrls(urls);
        }
        return success(BeanUtils.toBean(pageResult, AgentRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理明细")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<AgentRespVO> get(@RequestParam("id") Long id) {
        AgentDO agentDO = agentService.getAgent(id);
        return success(BeanUtils.toBean(agentDO, AgentRespVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理")
    @PreAuthorize("@ss.hasPermission('system:agent:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        agentService.deleteAgent(id);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得所有代理列表")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<List<AgentRespVO>> getAgentList() {
        Long agentId = WebFrameworkUtils.getLoginAgentId();
        if (agentId == null || agentId <= 0) {
            List<AgentDO> pageResult = agentService.getAllSimpleList();
            return success(BeanUtils.toBean(pageResult, AgentRespVO.class));
        }
        List<AgentDO> descendantList = agentService.getAllDescendants(agentId);
        return success(BeanUtils.toBean(descendantList, AgentRespVO.class));
    }

    @GetMapping("/get-tree-list")
    @Operation(summary = "代理整个链条关系")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<List<AgentRespVO>> getTreeList(@RequestParam("id") Long id) {
        List<AgentDO> ancestorList = agentService.getAllAncestors(id);
        List<AgentDO> descendantList = agentService.getAllDescendants(id);
        descendantList.removeIf(a -> Objects.equals(a.getId(), id));
        ancestorList.addAll(descendantList);
        return success(BeanUtils.toBean(ancestorList, AgentRespVO.class));
    }

    @GetMapping("/agent-total")
    @Operation(summary = "代理首页统计")
    @PreAuthorize("@ss.hasPermission('system:agent:query')")
    public CommonResult<AgentIndexTotalRespVO> getAgentTotal() {
        final LoginUser loginUser = getLoginUser();
        Long agentId = WebFrameworkUtils.getLoginAgentId();
        List<Long> agentIdList = new ArrayList<>();
        if (agentId == null || agentId <= 0) {
            agentIdList = agentService.getAllSimpleList().parallelStream().map(AgentDO::getId).toList();
        } else {
            agentIdList.add(agentId);
        }
        boolean isSuperAdmin = roleApi.hasAnySuperAdminOf(loginUser.getRoleIds());
        MemberUserTotalRespVO memberUserTotalRespVO = memberUserApi.getTotalByAgentId(agentIdList);
        AgentIndexTotalRespVO resp = BeanUtils.toBean(memberUserTotalRespVO, AgentIndexTotalRespVO.class);
        if (agentId != null && agentId > 0) {
            AgentDO agentDO = agentService.getAgent(agentId);
            List<TenantServerNameDO> list = tenantServerNameService.getAllServerNameListFromCached();
            final ArrayList<String> shareUrls = new ArrayList<>(list.size());
            for (TenantServerNameDO serverNameDO : list) {
                if (!serverNameDO.isShare()) {
                    continue;
                }
                // 如果是超管用户或者当前登录用户的租户id和域名的租户id相同则返回
                if (isSuperAdmin || serverNameDO.getTenId().equals(loginUser.getTenantId())) {
                    final String shareUrl = "https://" + serverNameDO.getServerName() + "/#/?ic=" + agentDO.getCode();
                    shareUrls.add(shareUrl);
                }
            }
            resp.setShareUrls(shareUrls);
        }
        return success(resp);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出 代理报表 Excel")
    @PreAuthorize("@ss.hasPermission('system:agent:report:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAgentReportExcel(@Valid AgentPageReqVO exportReqVO,
                                       HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentRespVO> list = agentService.getAgentPage(exportReqVO, getLoginUser()).getList();
        ExcelUtils.write(response, "代理报表.xls", "数据", AgentRespVO.class,
                BeanUtils.toBean(list, AgentRespVO.class));
    }
}
