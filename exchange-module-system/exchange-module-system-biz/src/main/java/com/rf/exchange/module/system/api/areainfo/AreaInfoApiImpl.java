package com.rf.exchange.module.system.api.areainfo;

import com.rf.exchange.module.system.api.areainfo.dto.AreaInfoDTO;
import com.rf.exchange.module.system.service.areainfo.AreaInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class AreaInfoApiImpl implements AreaInfoApi {

    @Resource
    private AreaInfoService areaInfoService;

    @Override
    public List<AreaInfoDTO> getAreaInfoList() {
        return areaInfoService.getAreaInfoList();
    }
}
