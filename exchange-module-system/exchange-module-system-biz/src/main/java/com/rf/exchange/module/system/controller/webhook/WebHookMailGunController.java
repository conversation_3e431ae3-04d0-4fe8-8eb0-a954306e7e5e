package com.rf.exchange.module.system.controller.webhook;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.system.dal.dataobject.mail.MailAccountDO;
import com.rf.exchange.module.system.dal.dataobject.mail.MailLogDetailDO;
import com.rf.exchange.module.system.enums.maigun.MailGunEventEnum;
import com.rf.exchange.module.system.enums.mail.MailSendStatusEnum;
import com.rf.exchange.module.system.service.mail.MailAccountService;
import com.rf.exchange.module.system.service.mail.MailLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MailGun的webhook 控制器
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Slf4j
@RequestMapping("/mailgun")
@RestController
public class WebHookMailGunController {

    private static final String KEY_SIGNATURE = "signature";
    private static final String KEY_SIGNATURE_TIMESTAMP = "timestamp";
    private static final String KEY_SIGNATURE_TOKEN = "token";
    private static final String KEY_DATA = "event-data";
    private static final String KEY_DATA_EVENT = "event";
    private static final String KEY_DATA_MESSAGE_ID = "message-id";
    private static final String KEY_DATA_MESSAGE = "message";
    private static final String KEY_DATA_MESSAGE_HEADERS = "headers";
    private static final String KEY_DATA_ENVELOPE = "envelope";
    private static final String KEY_DATA_ENVELOPE_SENDER = "sender";

    @Resource
    private MailLogService mailLogService;
    @Resource
    private MailAccountService mailAccountService;
    private JsonUtils jsonUtils;

    @PostMapping("/event")
    @SuppressWarnings("unchecked")
    public ResponseEntity<String> index(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        log.debug("收到webhook请求:{} 参数:{}", request.getRequestURL(), param);
        if (CollectionUtil.isNotEmpty(param)) {
            final Object data = param.get(KEY_DATA);
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;

                // 获取envelope中的发件邮箱
                final Map<String, Object> envelopeMap = (Map<String, Object>) dataMap.get(KEY_DATA_ENVELOPE);
                final String sender = (String) envelopeMap.get(KEY_DATA_ENVELOPE_SENDER);

                // 获取邮箱账号
                final MailAccountDO mailAccount = TenantUtils.executeIgnore(() -> mailAccountService.getMailAccountByMail(sender));
                if (null == mailAccount) {
                    log.warn("没有获取到 {} 的邮箱账号", sender);
                    return ResponseEntity.status(HttpStatus.OK).build();
                }

                // 验证签名
                final Object signature = param.get(KEY_SIGNATURE);
                if (signature instanceof Map) {
                    if (!verifySignature((Map<String, Object>) signature, mailAccount.getWebhookKey())) {
                        return ResponseEntity.status(HttpStatus.OK).build();
                    }
                } else {
                    return ResponseEntity.status(HttpStatus.OK).build();
                }

                // 解析event-data
                Map<String, Object> message = (Map<String, Object>) dataMap.get(KEY_DATA_MESSAGE);
                Map<String, Object> headers = (Map<String, Object>) message.get(KEY_DATA_MESSAGE_HEADERS);
                String event = (String) dataMap.get(KEY_DATA_EVENT);
                String messageId = (String) headers.get(KEY_DATA_MESSAGE_ID);
                // 处理事件
                handleMailGunEvent(event, messageId, param);
            }
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    private boolean verifySignature(Map<String, Object> signature, String signKey) {
        if (CollectionUtil.isEmpty(signature)) {
            log.error("无法验证mailgun的webhook签名 signKey为空");
            return false;
        }
        final String token = (String) signature.get(KEY_SIGNATURE_TOKEN);
        final String timestamp = (String) signature.get(KEY_SIGNATURE_TIMESTAMP);
        final String sign = (String) signature.get(KEY_SIGNATURE);
        final HMac mac = new HMac(HmacAlgorithm.HmacSHA256, signKey.getBytes());
        final String s = mac.digestHex(timestamp + token);
        return s.equals(sign);
    }

    private void handleMailGunEvent(String event, String messageId, Map<String, Object> params) {
        TenantUtils.executeIgnore(() -> {
            MailGunEventEnum eventEnum = MailGunEventEnum.ofEvent(event);
            if (eventEnum == null) {
                log.error("收到webhook无法解析的event type:{}", event);
                return;
            }
            switch (eventEnum) {
                case COMPLAINED:
                    mailLogService.updateMailSendWebhookResult(messageId, MailSendStatusEnum.FAILURE.getStatus(), "收到垃圾邮箱报告");
                    saveLogDetail(event, messageId, JsonUtils.toJsonString(params));
                    break;
                case ACCEPTED, OPENED, DELIVERED:
                    mailLogService.updateMailSendWebhookResult(messageId, MailSendStatusEnum.SUCCESS.getStatus(), "发送成功");
                    saveLogDetail(event, messageId, "");
                    break;
                case FAIL:
                    mailLogService.updateMailSendWebhookResult(messageId, MailSendStatusEnum.FAILURE.getStatus(), "发送失败,请查看日志详情");
                    saveLogDetail(event, messageId, JsonUtils.toJsonString(params));
                    break;
                default:
            }
        });
    }

    private void saveLogDetail(String event, String messageId, String detail) {
        TenantUtils.executeIgnore(() -> {
            MailLogDetailDO detailDO = new MailLogDetailDO();
            detailDO.setMessageId(messageId);
            detailDO.setEvent(event);
            detailDO.setTime(LocalDateTime.now());
            detailDO.setUpdater("webhook");
            detailDO.setDetail(detail);
            mailLogService.createMailDetailLog(detailDO);
        });
    }
}
