package com.rf.exchange.module.system.dal.mysql.miner;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.controller.admin.miner.vo.MinerProductIncomePageReqVO;
import com.rf.exchange.module.system.controller.app.miner.vo.MinerOrderPageReqVo;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProduct;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductIncome;
import com.rf.exchange.module.system.dal.dataobject.miner.MinerProductOrder;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MinerProductIncomeMapper extends BaseMapperX<MinerProductIncome> {

    default PageResult<MinerProductIncome> selectPage(MinerProductIncomePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MinerProductIncome>()
                .betweenIfPresent(MinerProductIncome::getCreateTime, reqVO.getCreateTime())
                .eq(reqVO.getUserId() != null, MinerProductIncome::getUserId, reqVO.getUserId())
                .eq(reqVO.getOrderNo()!=null, MinerProductIncome::getOrderNo, reqVO.getOrderNo())
                .orderByDesc(MinerProductIncome::getId));
    }

}
