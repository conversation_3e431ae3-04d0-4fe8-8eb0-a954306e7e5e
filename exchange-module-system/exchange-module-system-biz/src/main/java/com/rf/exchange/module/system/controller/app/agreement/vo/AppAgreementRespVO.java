package com.rf.exchange.module.system.controller.app.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户 App - 协议管理 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Schema(description = "用户 App - 协议管理 Response VO")
@Data
public class AppAgreementRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    private String title;

    @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;
}
