package com.rf.exchange.module.system.controller.admin.miner.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 挖矿产品收益明细表 DO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 挖矿产品收益")
@Data
public class MinerProductIncomeResp extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 订单号
     */
    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "xs123")
    private String orderNo;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "110")
    private Long productId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String username;

    /**
     * 本金
     */
    @Schema(description = "本金", requiredMode = Schema.RequiredMode.REQUIRED, example = "12.2")
    private BigDecimal principal;

    /**
     * 利息
     */
    @Schema(description = "利息", requiredMode = Schema.RequiredMode.REQUIRED, example = "11.1")
    private BigDecimal income;

}