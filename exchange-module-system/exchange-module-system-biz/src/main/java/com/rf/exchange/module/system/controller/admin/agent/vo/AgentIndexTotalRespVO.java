package com.rf.exchange.module.system.controller.admin.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 会员用户 Response VO")
@Data
@ToString(callSuper = true)
public class AgentIndexTotalRespVO {
    @Schema(description = "分享地址")
    private List<String> shareUrls;
    @Schema(description = "总用户数")
    private Long totalMemberCount;
    @Schema(description = "用户总余额")
    private BigDecimal totalMemberBalance;
    @Schema(description = "用户总充值")
    private BigDecimal totalMemberRechargeAmount;
    @Schema(description = "用户总提现")
    private BigDecimal totalMemberWithdrawAmount;

    @Schema(description = "今日新用户数")
    private Integer todayNewMemberCount;
    @Schema(description = "今日新用户充值人数")
    private Integer todayNewMemberRechargeCount;
    @Schema(description = "今日新用户充值金额")
    private BigDecimal todayNewMemberRechargeAmount;
    @Schema(description = "今日新用户提现人数")
    private Integer todayNewMemberWithdrawCount;
    @Schema(description = "今日新用户提现金额")
    private BigDecimal todayNewMemberWithdrawAmount;

    @Schema(description = "今日总充值人数")
    private Integer todayMemberRechargeCount;
    @Schema(description = "今日总充值笔数")
    private Integer todayRechargeCount;
    @Schema(description = "今日总充值金额")
    private BigDecimal todayRechargeAmount;

    @Schema(description = "今日总提现人数")
    private Integer todayMemberWithdrawCount;
    @Schema(description = "今日总提现笔数")
    private Integer todayWithdrawCount;
    @Schema(description = "今日总提现金额")
    private BigDecimal todayWithdrawAmount;

}
