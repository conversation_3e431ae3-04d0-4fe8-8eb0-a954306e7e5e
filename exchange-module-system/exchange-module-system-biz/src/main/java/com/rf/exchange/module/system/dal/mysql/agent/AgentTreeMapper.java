package com.rf.exchange.module.system.dal.mysql.agent;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentTreeDO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 会员代理树 Mapper
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Mapper
public interface AgentTreeMapper extends BaseMapperX<AgentTreeDO> {

    /**
     * 在关系表中插入对自身的连接。
     * 该方法与 insertAgentPath 搭配使用，通过两条语句才能插入完整的路径。
     *
     * @param id 节点的 ID
     */
    @Insert("INSERT INTO system_agent_tree (ancestor, descendant, dept) VALUES(#{id}, #{id}, 0)")
    void insertSelfLink(long id);

    /**
     * 复制父代理的路径结构，并修改 descendant 和 dept
     * 该方法与 insertSelfLink 搭配使用，通过两条语句才能插入完整的路径。
     *
     * @param descendant 子代理的 ID
     * @param ancestor   父代理的 ID
     */
    @Insert("INSERT INTO system_agent_tree (ancestor, descendant, dept) " +
            "SELECT ancestor, #{descendant}, dept+1 FROM system_agent_tree WHERE descendant=#{ancestor}")
    void insertAgentPath(long descendant, long ancestor);

    /**
     * 插入被移动代理的子代理和新的父代理的路径结构
     * <p>
     *
     * @param newPathList 新的路径列表
     */
    void insertMoveAgentDescendantWithNewAncestorPath(@Param("pathList") List<AgentTreeDO> newPathList);

    /**
     * 查询子代理到祖先代理之间的级差
     *
     * @param descendant 子代理id
     * @param ancestor   祖先代理id
     * @return 级差
     */
    @Select("SELECT dept FROM system_agent_tree WHERE descendant=#{descendant} AND ancestor=#{ancestor}")
    int selectDepth(long descendant, long ancestor);

    /**
     * 查询子代理自身的级差深度
     *
     * @param descendant 子代理id
     * @return 级查
     */
    @Select("SELECT dept from system_agent_tree WHERE descendant=#{descendant} AND dept > 0 ORDER BY dept DESC LIMIT 1")
    int selectSelfDepth(long descendant);

    /**
     * 查询代理的根代理
     * @param descendant 子代理id
     * @return 根代理
     */
    @Select("SELECT sa.* FROM system_agent AS sa " +
            "JOIN system_agent_tree AS sat " +
            "ON sat.ancestor=sa.id " +
            "WHERE sat.descendant=#{descendant} ORDER BY sat.dept DESC LIMIT 1")
    AgentDO selectRootAncestor(long descendant);

    /**
     * 获取所有小于等于指定深度的代理
     *
     * @param descendant 排除的子代理
     * @param dept       指定的深度层级
     * @return 有效的代理列表
     */
    @Select("SELECT sa.* FROM system_agent AS sa " +
            "JOIN system_agent_tree AS sat " +
            "ON sat.descendant=sa.id " +
            "WHERE sat.descendant != #{descendant} AND sat.dept <= #{dept}")
    List<AgentDO> selectAncestorWithDept(long descendant, long dept);

    /**
     * 查询某个代理的所有的子代理包含自己
     *
     * @param ancestor 节点 ID
     * @return 子代理列表，包括自身。
     */
    @Select("SELECT ma.* FROM system_agent_tree AS mat " +
            "JOIN system_agent AS ma " +
            "ON mat.descendant=ma.id " +
            "WHERE mat.ancestor=#{ancestor}")
    List<AgentDO> selectAllDescendant(long ancestor);

    /**
     * 查询由 ID 指定子代理（含）到根代理（含）的路径。
     * 比下面的<code>selectAncestorToSpecial</code>简单些。
     *
     * @param descendant 子代理的 ID
     * @return 路径列表。如果代理不存在，则返回空列表
     */
    @Select("SELECT ma.* FROM system_agent_tree AS mat " +
            "JOIN system_agent AS ma ON mat.ancestor=ma.id " +
            "WHERE mat.descendant=#{descendant} " +
            "ORDER BY mat.dept DESC")
    List<AgentDO> selectAllAncestor(long descendant);

    /**
     * 查询某个代理的第 N 级子代理
     *
     * @param ancestor 祖先代理的 ID
     * @param dept     级差（0表示自己，1表示直属子节点）
     * @return 子代理列表
     */
    @Select("SELECT ma.* FROM system_agent_tree AS mat " +
            "JOIN system_agent AS ma " +
            "ON mat.descendant=ma.id " +
            "WHERE mat.ancestor=#{ancestor} AND mat.dept>=#{dept}")
    List<AgentDO> selectDescendantByDept(long ancestor, int dept);

    /**
     * 查找某代理下的所有直属子代理的 ID
     * <p>
     * 该方法与上面的<code>selectDescendant</code>不同，它只查询节点的 ID 效率高些。
     *
     * @param ancestor 祖先代理的 ID
     * @return 子代理的 ID 数组
     */
    @Select("SELECT descendant FROM system_agent_tree WHERE ancestor=#{ancestor} AND dept=#{dept}")
    List<Long> selectDescendantIds(long ancestor, int dept);

    /**
     * 查询某个代理的所有的子代理的 ID，不包括参数所指定的代理。
     *
     * @param ancestor 祖先代理 ID
     * @return 子代理 ID 列表
     */
    @Select("SELECT descendant FROM system_agent_tree WHERE ancestor=#{ancestor} AND dept>0")
    List<Long> selectAllDescendantIds(@Param("ancestor") long ancestor);

    /**
     * 查询某个代理的所有的子代理的关系,包括ancestor本身
     *
     * @param ancestor 父代理
     * @param dept     深度
     * @return 代理关系列表
     */
    @Select("SELECT id, ancestor, descendant, dept FROM system_agent_tree WHERE ancestor=#{ancestor} AND dept >= #{dept}")
    List<AgentTreeDO> selectDescendantPathList(long ancestor, long dept);

    /**
     * 获取指定代理所有上级路径, 包括descendant本身
     *
     * @param descendant 子代理id
     * @param dept       层级深度
     * @return 所有父代理的路径关系
     */
    @Select("SELECT id, ancestor, descendant, dept FROM system_agent_tree WHERE descendant=#{descendant} AND dept >= #{dept}")
    List<AgentTreeDO> selectAncestorPathList(long descendant, int dept);

    /**
     * 查询某个代理往上第 N 级祖先代理的 ID。
     *
     * @param descendant 代理的 ID
     * @param dept       祖先距离 N（0表示自己，1表示直接父代理）
     * @return 父代理的 ID，如果不存在则返回 null
     */
    @Select("SELECT ancestor FROM system_agent_tree WHERE descendant=#{descendant} AND dept=#{dept}")
    Long selectAncestorId(long descendant, int dept);

    /**
     * 查询某个代理往上第 N 级祖先代理
     *
     * @param descendant 子代理的 ID
     * @param dept       级差
     * @return 祖先代理对象
     */
    @Select("SELECT * FROM system_agent WHERE id = (SELECT ancestor FROM system_agent_tree WHERE descendant=#{descendant} AND dept=#{dept} LIMIT 1)")
    AgentDO selectAncestor(long descendant, int dept);


    /**
     * 批量获取子代理的指定层级的父代理
     * @param descendantIds 子代理id
     * @param dept 深度层级
     * @return 代理列表
     */
    List<AgentDO> selectAncestorsByDescendants(@Param("descendants") List<Long> descendantIds, int dept);

    /**
     * 查询由 ID 指定代理（含）到指定上级代理（不含）的路径。
     *
     * @param descendant  子代理的 ID
     * @param newAncestor 上级代理的 ID
     * @return 路径列表，如果代理不存在，或上级代理不存在，则返回空列表。
     */
    @Select("SELECT ma.* FROM system_agent_tree AS mat " +
            "JOIN system_agent AS ma ON mat.ancestor=ma.id " +
            "WHERE mat.descendant=#{id} AND " +
            "mat.dept < (SELECT dept FROM system_agent_tree WHERE descendant=#{descendant} AND ancestor=#{ancestor}) " +
            "ORDER BY mat.dept DESC")
    List<AgentDO> selectAncestorToSpecial(long descendant, int newAncestor);

    ///**
    // * 查询指定节点到它某个祖先节点的距离。
    // *
    // * @param id       节点的 ID
    // * @param ancestor 祖先节点的 ID
    // * @return 距离，如果 ancestor 并不是其祖先节点则返回 null。
    // */
    //@Select("SELECT dept FROM system_agent_tree WHERE descendant=#{id} AND ancestor=#{ancestor}")
    //Integer selectDistance(long id, long ancestor);


    /**
     * 删除末尾节点的代理
     *
     * <h2>注意</h2>
     * 节点可能有子树，而子树的节点在该节点之上的路径并没有改变，
     * 所以使用该方法后还必须手动修改子节点的路径以确保一致性。
     *
     * @param id 节点的 ID
     */
    @Delete("DELETE FROM system_agent_tree WHERE descendant=#{id}")
    void deleteLeafAgent(long id);

    /**
     * 删除除了父代理之外的所有祖先代理
     *
     * @param descendant 代理id
     */
    @Delete("DELETE FROM system_agent_tree WHERE descendant=#{descendant} AND dept > 1")
    void deleteGrandAncestor(long descendant);

    /**
     * 获取指定代理id所有上级id包含自己
     *
     * @param id 子代理id
     * @return 所有的父id
     */
    @Select("SELECT ancestor FROM system_agent_tree WHERE descendant=#{id}")
    List<Long> selectAllAncestorIdList(@Param("id") long id);

    /**
     * 获取指定子代理的所有祖先代理(即上级代理)
     *
     * @param descendant 子代理id
     * @return 子代理的所有上级代理
     */
    @Select("SELECT sa.*, sat.dept FROM system_agent AS sa " +
            "JOIN system_agent_tree AS sat " +
            "ON sat.ancestor=sa.id " +
            "WHERE sat.descendant=#{descendant}")
    List<Map<String, Object>> selectAncestorMapOfAgent(long descendant);
}
