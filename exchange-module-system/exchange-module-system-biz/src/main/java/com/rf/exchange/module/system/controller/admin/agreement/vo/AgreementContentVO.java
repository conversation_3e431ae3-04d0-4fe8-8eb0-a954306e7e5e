package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 协议内容 VO
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Schema(description = "管理后台 - 协议内容 VO")
@Data
public class AgreementContentVO {

    @Schema(description = "内容ID", example = "1")
    private Long id;

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long agreementId;

    @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
    private String languageCode;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    private String title;

    @Schema(description = "协议内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "这是隐私协议的详细内容...")
    private String content;

    @Schema(description = "内容摘要", example = "本协议规定了用户隐私保护相关条款")
    private String summary;

    @Schema(description = "关键词", example = "隐私,保护,用户数据")
    private String keywords;

    @Schema(description = "SEO描述", example = "用户隐私保护协议详细说明")
    private String seoDescription;

}
