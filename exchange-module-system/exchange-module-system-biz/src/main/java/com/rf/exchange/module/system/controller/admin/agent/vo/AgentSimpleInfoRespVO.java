package com.rf.exchange.module.system.controller.admin.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-31
 */
@Schema(description = "管理后台 - 移动代理 Request VO")
@Data
public class AgentSimpleInfoRespVO {

    @Schema(description = "代理id", example = "1024")
    private Long id;

    @Schema(description = "代理名称", example = "张三")
    private String name;

}