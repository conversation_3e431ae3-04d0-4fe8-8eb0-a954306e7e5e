package com.rf.exchange.module.system.service.appconfig;

import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;
import com.rf.exchange.module.system.api.areainfo.dto.AreaInfoDTO;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import com.rf.exchange.module.system.api.lang.dto.LangRespDTO;
import com.rf.exchange.module.system.controller.app.appconfig.vo.AppConfigRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
public interface AppConfigService {

    /**
     * 获取APP配置信息列表
     *
     * @param tenantId 租户编号
     * @return 配置列表
     */
    AppConfigRespVO getAppConfigs(Long tenantId);

    /**
     * 获取APP 汇率列表
     *
     * @param tenantId 租户编号
     * @return 汇率列表
     */
    List<AppConfigRespVO.ExchangeRate> getCurrencyRates(Long tenantId);


    List<AppConfigRespVO.Currency> getCurrencyList();

    List<AppConfigRespVO.Country> getAreaList();

    List<AppConfigRespVO.Language> getLangList();

    List<AppConfigRespVO.TradeAssetType> getTradeTypeList(Long tenantId);

    AppConfigRespVO.DefaultTradePair getDefaultTradePair(Long tenantId);
}
