package com.rf.exchange.module.system.controller.admin.tenant.vo.type;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 租户配置分类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantDictTypePageReqVO extends PageParam {

    @Schema(description = "名称", example = "赵六")
    private String name;

    @Schema(description = "编码类型", example = "2")
    private String type;

    @Schema(description = "状态", example = "2")
    private Short status;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}