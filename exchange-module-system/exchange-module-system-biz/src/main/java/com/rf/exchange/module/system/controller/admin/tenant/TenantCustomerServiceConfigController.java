package com.rf.exchange.module.system.controller.admin.tenant;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.system.controller.admin.tenant.vo.customerserviceconfig.TenantCustomerServiceConfigRespVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.customerserviceconfig.TenantCustomerServiceConfigSaveReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.data.TenantDictDataSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictDataDO;
import com.rf.exchange.module.system.service.tenantdict.TenantDictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "管理后台 - 多客服配置")
@RestController
@RequestMapping("/system/tenant-customer-service-config")
@Validated
public class TenantCustomerServiceConfigController {
    @Resource
    private TenantDictDataService tenantDictDataService;

    private static final String dictType = "customer_service_config";

    @Operation(summary = "获取多客服配置")
    @GetMapping("list")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:query')")
    public CommonResult<PageResult<TenantCustomerServiceConfigRespVO>> getList() {
        List<TenantDictDataDO> list = tenantDictDataService.getDictDataListByDictType(TenantContextHolder.getTenantId(), dictType);
        List<TenantCustomerServiceConfigRespVO> ret = new ArrayList<>();
        for (TenantDictDataDO item : list) {
            ret.add(TenantCustomerServiceConfigRespVO.builder().id(item.getId()).name(item.getLabel()).status(item.getStatus()).serviceUrl(item.getValue()).ico(item.getRemark()).build());
        }
        PageResult<TenantCustomerServiceConfigRespVO> pageList=new PageResult<>(ret,(long)ret.size(),1l,1l);
        return CommonResult.success(pageList);
    }

    @Operation(summary = "获取客服配置")
    @GetMapping("get")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:query')")
    public CommonResult<TenantCustomerServiceConfigRespVO> get(@Param("id")Long id) {
        TenantDictDataDO tenantDictData= tenantDictDataService.getTenantDictData(id);
        TenantCustomerServiceConfigRespVO rep=TenantCustomerServiceConfigRespVO.builder().id(tenantDictData.getId()).name(tenantDictData.getLabel()).status(tenantDictData.getStatus()).serviceUrl(tenantDictData.getValue()).ico(tenantDictData.getRemark()).build();
        return CommonResult.success(rep);
    }

    @Operation(summary = "添加多客服配置")
    @PostMapping("create")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:create')")
    public CommonResult<Boolean> create(@Valid @RequestBody TenantCustomerServiceConfigSaveReqVO reqVO) {
        TenantDictDataSaveReqVO updateObj = TenantDictDataSaveReqVO.builder()
                .dictType(dictType)
                .label(reqVO.getName())
                .remark(reqVO.getIco())
                .value(reqVO.getServiceUrl())
                .status(reqVO.getStatus()).build();
        tenantDictDataService.createTenantDictData(updateObj);
        return CommonResult.success(true);
    }

    @Operation(summary = "修改多客服配置")
    @PostMapping("update")
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:update')")
    public CommonResult<Boolean> update(@Valid @RequestBody TenantCustomerServiceConfigSaveReqVO reqVO) {
        TenantDictDataSaveReqVO updateObj = TenantDictDataSaveReqVO.builder().dictType(dictType).id(reqVO.getId()).label(reqVO.getName()).remark(reqVO.getIco()).value(reqVO.getServiceUrl()).status(reqVO.getStatus()).build();
        tenantDictDataService.updateTenantDictData(updateObj);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客服")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:tenant-dict-data:delete')")
    public CommonResult<Boolean> delete(@Param("id")Long id){
        tenantDictDataService.deleteTenantDictData(id);
        return CommonResult.success(true);
    }
}
