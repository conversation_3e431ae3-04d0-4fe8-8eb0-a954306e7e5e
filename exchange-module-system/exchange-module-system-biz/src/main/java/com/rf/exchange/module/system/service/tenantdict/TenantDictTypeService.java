package com.rf.exchange.module.system.service.tenantdict;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypePageReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypeSaveBatchReqVO;
import com.rf.exchange.module.system.controller.admin.tenant.vo.type.TenantDictTypeSaveReqVO;
import com.rf.exchange.module.system.dal.dataobject.tenantdict.TenantDictTypeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 租户配置分类 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantDictTypeService {

    /**
     * 创建租户配置分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantDictType(@Valid TenantDictTypeSaveReqVO createReqVO);

    /**
     * 批量创建租户字典类型
     *
     * @param reqVO     创建信息
     * @param loginUser 登录用户
     */
    void batchCreateTenantDictType(@Valid TenantDictTypeSaveBatchReqVO reqVO, LoginUser loginUser);

    /**
     * 更新租户配置分类
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantDictType(@Valid TenantDictTypeSaveReqVO updateReqVO);

    /**
     * 删除租户配置分类
     *
     * @param id 编号
     */
    void deleteTenantDictType(Long id);

    /**
     * 获得租户配置分类
     *
     * @param id 编号
     * @return 租户配置分类
     */
    TenantDictTypeDO getTenantDictType(Long id);

    /**
     * 获得租户配置分类分页
     *
     * @param pageReqVO 分页查询
     * @return 租户配置分类分页
     */
    PageResult<TenantDictTypeDO> getTenantDictTypePage(TenantDictTypePageReqVO pageReqVO);

    /**
     * 复制租户字典模板
     */
    void copyTenantDictTypeAndData(Long tenantId);

    /**
     * 获取所有字典数据
     */
    List<TenantDictTypeDO> getDictTypeList();

}