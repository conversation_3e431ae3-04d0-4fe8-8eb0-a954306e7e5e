package com.rf.exchange.module.system.service.agent;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticPageReqVO;
import com.rf.exchange.module.system.controller.admin.agent.vo.AgentStatisticRespVO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentDO;
import com.rf.exchange.module.system.dal.dataobject.agent.AgentStatisticDO;
import com.rf.exchange.module.system.dal.mysql.agent.AgentStatisticHistoryMapper;
import com.rf.exchange.module.system.dal.mysql.agent.AgentStatisticMapper;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-16
 */
@Service
@Valid
@Slf4j
public class AgentStatisticServiceImpl implements AgentStatisticService {

    @Resource
    private AgentStatisticMapper statisticMapper;
    @Resource
    private AgentStatisticHistoryMapper statisticHistoryMapper;
    @Resource
    private AgentService agentService;

    @Override
    @DSTransactional
    @Master
    public void incRegister(Long agentId) {
//        List<AgentDO> list = agentService.getAllAncestors(agentId);
//        for (AgentDO agentDo : list) {
//            statisticMapper.incRegister(agentDo.getId());
//        }
        statisticMapper.incRegister(agentId);
    }

    @Override
    public void incRecharge(Long agentId, boolean incCount, BigDecimal amount) {
//        List<Long> list = agentService.selectAllAncestorIdList(agentId);
//        for (Long id : list) {
//            if (incCount) {
//                statisticMapper.incRecharge(id);
//            }
//            statisticMapper.incRechargeAmount(id, amount);
//        }
        statisticMapper.incRechargeAmount(agentId,amount);
    }

    @Override
    public void incWithdraw(Long agentId, boolean incCount, BigDecimal amount) {
//        List<Long> list = agentService.selectAllAncestorIdList(agentId);
//        for (Long id : list) {
//            if (incCount) {
//                statisticMapper.incWithdraw(id);
//            }
//            statisticMapper.incRechargeAmount(id, amount);
//        }
        statisticMapper.incRechargeAmount(agentId, amount);
    }

    @Override
    public void setTotalBalance(Long agentId, BigDecimal totalBalance) {
        statisticMapper.setTotalBalance(agentId, totalBalance);
    }

    @Override
    public PageResult<AgentStatisticDO> getAgentPage(AgentStatisticPageReqVO reqVO) {
        return statisticMapper.selectPage(reqVO);
    }
}
