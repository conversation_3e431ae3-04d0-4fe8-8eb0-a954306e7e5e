package com.rf.exchange.module.system.dal.dataobject.miner;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 挖矿产品表 DO
 *
 * <AUTHOR>
 */
@TableName(value = "miner_product", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MinerProduct extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 周期
     */
    @ImageUrl
    private String img;

    /**
     * 最小收益
     */
    private BigDecimal minProfit;

    /**
     * 最大收益
     */
    private BigDecimal maxProfit;

    /**
     * 最低购买限制
     */
    private BigDecimal minPurchase;

    /**
     * 最高购买限制
     */
    private BigDecimal maxPurchase;

    /**
     * 违约金比例
     */
    private BigDecimal liquidatedDamagesRatio;

    /**
     * sort
     */
    private Integer sort;
}