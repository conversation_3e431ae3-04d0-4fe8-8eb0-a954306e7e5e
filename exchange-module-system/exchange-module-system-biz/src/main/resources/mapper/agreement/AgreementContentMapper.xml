<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.system.dal.mysql.agreement.AgreementContentMapper">

    <!-- 批量删除协议的所有语言版本 -->
    <update id="deleteByAgreementId">
        UPDATE system_agreement_content 
        SET deleted = 1,
            updater = #{updater},
            update_time = NOW()
        WHERE agreement_id = #{agreementId} 
          AND deleted = 0
    </update>

    <!-- 批量更新语言内容的协议ID -->
    <update id="updateAgreementId">
        UPDATE system_agreement_content 
        SET agreement_id = #{newAgreementId},
            updater = #{updater},
            update_time = NOW()
        WHERE agreement_id = #{oldAgreementId} 
          AND deleted = 0
    </update>

    <!-- 查询协议多语言统计信息 -->
    <select id="selectLanguageStatistics" resultType="com.rf.exchange.module.system.dal.mysql.agreement.AgreementContentMapper$AgreementContentStatistics">
        SELECT 
            language_code as languageCode,
            CASE language_code
                WHEN 'zh-CN' THEN '简体中文'
                WHEN 'zh-TW' THEN '繁体中文'
                WHEN 'en' THEN 'English'
                WHEN 'ja' THEN '日本語'
                WHEN 'ko' THEN '한국어'
                WHEN 'es' THEN 'Español'
                WHEN 'fr' THEN 'Français'
                WHEN 'de' THEN 'Deutsch'
                WHEN 'ru' THEN 'Русский'
                WHEN 'ar' THEN 'العربية'
                WHEN 'pt' THEN 'Português'
                WHEN 'it' THEN 'Italiano'
                WHEN 'th' THEN 'ไทย'
                WHEN 'vi' THEN 'Tiếng Việt'
                WHEN 'id' THEN 'Bahasa Indonesia'
                WHEN 'ms' THEN 'Bahasa Melayu'
                WHEN 'hi' THEN 'हिन्दी'
                ELSE language_code
            END as languageDisplayName,
            COUNT(DISTINCT agreement_id) as agreementCount,
            SUM(CHAR_LENGTH(content)) as totalContentLength
        FROM system_agreement_content 
        WHERE deleted = 0
        GROUP BY language_code
        ORDER BY agreementCount DESC, language_code ASC
    </select>

    <!-- 通用查询字段 -->
    <sql id="baseColumns">
        id, agreement_id, language_code, title, content,
        creator, create_time, updater, update_time, deleted
    </sql>

    <!-- 基础查询条件 -->
    <sql id="baseWhere">
        <where>
            deleted = 0
            <if test="agreementId != null">
                AND agreement_id = #{agreementId}
            </if>
            <if test="languageCode != null and languageCode != ''">
                AND language_code = #{languageCode}
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
        </where>
    </sql>

    <!-- 查询协议内容详情（包含协议基本信息） -->
    <select id="selectContentWithAgreement" resultType="com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO">
        SELECT 
            ac.id,
            ac.agreement_id,
            ac.language_code,
            ac.title,
            ac.content,
            ac.creator,
            ac.create_time,
            ac.updater,
            ac.update_time,
            ac.deleted,
            a.type as agreementType,
            a.status as agreementStatus
        FROM system_agreement_content ac
        INNER JOIN system_agreement a ON ac.agreement_id = a.id
        WHERE ac.deleted = 0 
          AND a.deleted = 0
          AND ac.agreement_id = #{agreementId}
          AND ac.language_code = #{languageCode}
    </select>

    <!-- 查询缺少特定语言版本的协议 -->
    <select id="selectAgreementsMissingLanguage" resultType="java.lang.Long">
        SELECT DISTINCT a.id
        FROM system_agreement a
        WHERE a.deleted = 0
          AND NOT EXISTS (
              SELECT 1 FROM system_agreement_content ac
              WHERE ac.agreement_id = a.id
                AND ac.language_code = #{languageCode}
                AND ac.deleted = 0
          )
        ORDER BY a.id
    </select>

    <!-- 查询协议的语言完整性报告 -->
    <select id="selectLanguageCompletenessReport" resultType="java.util.Map">
        SELECT 
            a.id as agreementId,
            a.type as agreementType,
            a.title as agreementTitle,
            a.tenant_id as tenantId,
            GROUP_CONCAT(ac.language_code ORDER BY ac.language_code) as availableLanguages,
            COUNT(ac.language_code) as languageCount
        FROM system_agreement a
        LEFT JOIN system_agreement_content ac ON a.id = ac.agreement_id AND ac.deleted = 0
        WHERE a.deleted = 0
        GROUP BY a.id, a.type, a.title, a.tenant_id
        ORDER BY a.tenant_id, a.type, a.id
    </select>

    <!-- 批量插入协议内容 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO system_agreement_content (
            agreement_id, language_code, title, content,
            creator, create_time, updater, update_time, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.agreementId},
                #{item.languageCode},
                #{item.title},
                #{item.content},
                #{item.creator},
                #{item.createTime},
                #{item.updater},
                #{item.updateTime},
                #{item.deleted}
            )
        </foreach>
    </insert>

</mapper>
