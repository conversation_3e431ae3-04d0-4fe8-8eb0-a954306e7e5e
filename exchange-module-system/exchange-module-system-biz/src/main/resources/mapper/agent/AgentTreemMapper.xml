<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.system.dal.mysql.agent.AgentTreeMapper">

    <select id="selectAncestorsByDescendants" resultType="com.rf.exchange.module.system.dal.dataobject.agent.AgentDO">
        SELECT sa.*
        FROM system_agent sa
        JOIN (
        SELECT descendant, MIN(ancestor) AS ancestor
        FROM system_agent_tree
        WHERE descendant IN
        <foreach item="descendant" index="index" collection="descendants" open="(" separator="," close=")">
            #{descendant}
        </foreach>
        AND dept = #{dept}
        GROUP BY descendant
        ) sat ON sa.id = sat.ancestor
    </select>

    <insert id="insertMoveAgentDescendantWithNewAncestorPath">
        INSERT INTO system_agent_tree (ancestor, descendant, dept)
        VALUES
        <foreach collection="pathList" item="item" index="index" separator=",">
            (#{item.ancestor}, #{item.descendant}, #{item.dept})
        </foreach>
    </insert>

</mapper>