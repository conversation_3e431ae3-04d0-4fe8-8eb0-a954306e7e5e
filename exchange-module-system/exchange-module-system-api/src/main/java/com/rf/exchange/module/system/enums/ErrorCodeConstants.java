package com.rf.exchange.module.system.enums;

import com.rf.exchange.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== AUTH 模块 1-002-000-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_002_000_000, "登录失败，账号密码不正确", "AUTH_LOGIN_BAD_CREDENTIALS");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_002_000_001, "登录失败，账号被禁用", "AUTH_LOGIN_USER_DISABLED");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR =
        new ErrorCode(1_002_000_004, "验证码不正确，原因：{}", "AUTH_LOGIN_CAPTCHA_CODE_ERROR");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND =
        new ErrorCode(1_002_000_005, "AUTH_THIRD_LOGIN_NOT_BIND", "AUTH_THIRD_LOGIN_NOT_BIND");
    ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1_002_000_006, "Token已经过期", "AUTH_TOKEN_EXPIRED");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1_002_000_007, "手机号不存在", "AUTH_MOBILE_NOT_EXISTS");
    ErrorCode GOOGLE_SECRET_BINDING_EXPIRED =
        new ErrorCode(1_002_000_008, "绑定的谷歌密钥已过期，请重新获取", "GOOGLE_SECRET_BINDING_EXPIRED");
    ErrorCode GOOGLE_CODE_ERROR = new ErrorCode(1_002_000_009, "谷歌验证码不正确", "GOOGLE_CODE_ERROR");
    ErrorCode ACCOUNT_IS_ERROR = new ErrorCode(1_002_000_010, "登录失败，账号不存在", "ACCOUNT_IS_ERROR");
    ErrorCode GOOGLE_SECRET_IS_NOT_BINDING =
        new ErrorCode(1_002_000_011, "未绑定谷歌密钥，请先绑定", "GOOGLE_SECRET_IS_NOT_BINDING");
    // ========== 菜单模块 1-002-001-000 ==========
    ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1_002_001_000, "已经存在该名字的菜单", "MENU_NAME_DUPLICATE");
    ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1_002_001_001, "父菜单不存在", "MENU_PARENT_NOT_EXISTS");
    ErrorCode MENU_PARENT_ERROR = new ErrorCode(1_002_001_002, "不能设置自己为父菜单", "MENU_PARENT_ERROR");
    ErrorCode MENU_NOT_EXISTS = new ErrorCode(1_002_001_003, "菜单不存在", "MENU_NOT_EXISTS");
    ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1_002_001_004, "存在子菜单，无法删除", "MENU_EXISTS_CHILDREN");
    ErrorCode MENU_PARENT_NOT_DIR_OR_MENU =
        new ErrorCode(1_002_001_005, "父菜单的类型必须是目录或者菜单", "MENU_PARENT_NOT_DIR_OR_MENU");

    // ========== 角色模块 1-002-002-000 ==========
    ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1_002_002_000, "角色不存在", "ROLE_NOT_EXISTS");
    ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1_002_002_001, "已经存在名为[{}]的角色", "ROLE_NAME_DUPLICATE");
    ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1_002_002_002, "已经存在编码为[{}]的角色", "ROLE_CODE_DUPLICATE");
    ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE =
        new ErrorCode(1_002_002_003, "不能操作类型为系统内置的角色", "ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE");
    ErrorCode ROLE_IS_DISABLE = new ErrorCode(1_002_002_004, "名字为[{}]的角色已被禁用", "ROLE_IS_DISABLE");
    ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1_002_002_005, "编码[{}]不能使用", "ROLE_ADMIN_CODE_ERROR");

    // ========== 用户模块 1-002-003-000 ==========
    ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1_002_003_000, "{}账号已存在", "USER_USERNAME_EXISTS");
    ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1_002_003_001, "手机号已经存在", "USER_MOBILE_EXISTS");
    ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1_002_003_002, "邮箱已经存在", "USER_EMAIL_EXISTS");
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_002_003_003, "用户不存在", "USER_NOT_EXISTS");
    ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_002_003_004, "导入用户数据不能为空", "USER_IMPORT_LIST_IS_EMPTY");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1_002_003_005, "用户密码校验失败", "USER_PASSWORD_FAILED");
    ErrorCode USER_IS_DISABLE = new ErrorCode(1_002_003_006, "名字为[{}]的用户已被禁用", "USER_IS_DISABLE");
    ErrorCode USER_COUNT_MAX = new ErrorCode(1_002_003_008, "创建用户失败，原因：超过租户最大租户配额[{}]", "USER_COUNT_MAX");

    // ========== 部门模块 1-002-004-000 ==========
    ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1_002_004_000, "已经存在该名字的部门", "DEPT_NAME_DUPLICATE");
    ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1_002_004_001, "父级部门不存在", "DEPT_PARENT_NOT_EXITS");
    ErrorCode DEPT_NOT_FOUND = new ErrorCode(1_002_004_002, "当前部门不存在", "DEPT_NOT_FOUND");
    ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1_002_004_003, "存在子部门，无法删除", "DEPT_EXITS_CHILDREN");
    ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1_002_004_004, "不能设置自己为父部门", "DEPT_PARENT_ERROR");
    ErrorCode DEPT_EXISTS_USER = new ErrorCode(1_002_004_005, "部门中存在员工，无法删除", "DEPT_EXISTS_USER");
    ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1_002_004_006, "部门[{}]不处于开启状态，不允许选择", "DEPT_NOT_ENABLE");
    ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1_002_004_007, "不能设置自己的子部门为父部门", "DEPT_PARENT_IS_CHILD");

    // ========== 岗位模块 1-002-005-000 ==========
    ErrorCode POST_NOT_FOUND = new ErrorCode(1_002_005_000, "当前岗位不存在", "POST_NOT_FOUND");
    ErrorCode POST_NOT_ENABLE = new ErrorCode(1_002_005_001, "岗位[{}]不处于开启状态，不允许选择", "POST_NOT_ENABLE");
    ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1_002_005_002, "已经存在该名字的岗位", "POST_NAME_DUPLICATE");
    ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1_002_005_003, "已经存在该标识的岗位", "POST_CODE_DUPLICATE");

    // ========== 字典类型 1-002-006-000 ==========
    ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1_002_006_001, "当前字典类型不存在", "DICT_TYPE_NOT_EXISTS");
    ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1_002_006_002, "字典类型不处于开启状态，不允许选择", "DICT_TYPE_NOT_ENABLE");
    ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1_002_006_003, "已经存在该名字的字典类型", "DICT_TYPE_NAME_DUPLICATE");
    ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1_002_006_004, "已经存在该类型的字典类型", "DICT_TYPE_TYPE_DUPLICATE");
    ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1_002_006_005, "无法删除，该字典类型还有字典数据", "DICT_TYPE_HAS_CHILDREN");

    // ========== 字典数据 1-002-007-000 ==========
    ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1_002_007_001, "当前字典数据不存在", "DICT_DATA_NOT_EXISTS");
    ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1_002_007_002, "字典数据[{}]不处于开启状态，不允许选择", "DICT_DATA_NOT_ENABLE");
    ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1_002_007_003, "已经存在该值的字典数据", "DICT_DATA_VALUE_DUPLICATE");

    // ========== 通知公告 1-002-008-000 ==========
    ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1_002_008_001, "当前通知公告不存在", "NOTICE_NOT_FOUND");

    // ========== 短信渠道 1-002-011-000 ==========
    ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1_002_011_000, "短信渠道不存在", "SMS_CHANNEL_NOT_EXISTS");
    ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1_002_011_001, "短信渠道不处于开启状态，不允许选择", "SMS_CHANNEL_DISABLE");
    ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1_002_011_002, "无法删除，该短信渠道还有短信模板", "SMS_CHANNEL_HAS_CHILDREN");

    // ========== 短信模板 1-002-012-000 ==========
    ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_012_000, "短信模板不存在", "SMS_TEMPLATE_NOT_EXISTS");
    ErrorCode SMS_TEMPLATE_CODE_DUPLICATE =
        new ErrorCode(1_002_012_001, "已经存在编码为[{}]的短信模板", "SMS_TEMPLATE_CODE_DUPLICATE");
    ErrorCode SMS_TEMPLATE_API_ERROR = new ErrorCode(1_002_012_002, "短信API模板调用失败，原因是：{}", "SMS_TEMPLATE_API_ERROR");
    ErrorCode SMS_TEMPLATE_API_AUDIT_CHECKING =
        new ErrorCode(1_002_012_003, "短信API模版无法使用，原因：审批中", "SMS_TEMPLATE_API_AUDIT_CHECKING");
    ErrorCode SMS_TEMPLATE_API_AUDIT_FAIL =
        new ErrorCode(1_002_012_004, "短信API模版无法使用，原因：审批不通过，{}", "SMS_TEMPLATE_API_AUDIT_FAIL");
    ErrorCode SMS_TEMPLATE_API_NOT_FOUND =
        new ErrorCode(1_002_012_005, "短信API模版无法使用，原因：模版不存在", "SMS_TEMPLATE_API_NOT_FOUND");

    // ========== 短信发送 1-002-013-000 ==========
    ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS =
        new ErrorCode(1_002_013_001, "模板参数[{}]缺失", "SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS");

    // ========== 短信验证码 1-002-014-000 ==========
    ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1_002_014_000, "验证码不存在", "SMS_CODE_NOT_FOUND");
    ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1_002_014_001, "验证码已过期", "SMS_CODE_EXPIRED");
    ErrorCode SMS_CODE_USED = new ErrorCode(1_002_014_002, "验证码已使用", "SMS_CODE_USED");
    ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode(1_002_014_003, "验证码不正确", "SMS_CODE_NOT_CORRECT");
    ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY =
        new ErrorCode(1_002_014_004, "超过每日短信发送数量", "SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY");
    ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1_002_014_005, "短信发送过于频繁", "SMS_CODE_SEND_TOO_FAST");

    // ========== 租户信息 1-002-015-000 ==========
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1_002_015_000, "租户不存在", "TENANT_NOT_EXISTS");
    ErrorCode TENANT_DISABLE = new ErrorCode(1_002_015_001, "名字为[{}]的租户已被禁用", "TENANT_DISABLE");
    ErrorCode TENANT_EXPIRE = new ErrorCode(1_002_015_002, "名字为[{}]的租户已过期", "TENANT_EXPIRE");
    ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM =
        new ErrorCode(1_002_015_003, "系统租户不能进行修改、删除等操作！", "TENANT_CAN_NOT_UPDATE_SYSTEM");
    ErrorCode TENANT_CODE_DUPLICATE = new ErrorCode(1_002_015_004, "租户码为[{}]的租户已存在", "TENANT_CODE_DUPLICATE");
    ErrorCode TENANT_WEBSITE_DUPLICATE = new ErrorCode(1_002_015_005, "域名为[{}]的租户已存在", "TENANT_WEBSITE_DUPLICATE");

    // ========== 租户套餐 1-002-016-000 ==========
    ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1_002_016_000, "租户套餐不存在", "TENANT_PACKAGE_NOT_EXISTS");
    ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1_002_016_001, "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除", "TENANT_PACKAGE_USED");
    ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1_002_016_002, "名字为[{}]的租户套餐已被禁用", "TENANT_PACKAGE_DISABLE");

    // ========== OAuth2 客户端 1-002-020-000 =========
    ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1_002_020_000, "OAuth2客户端不存在", "OAUTH2_CLIENT_NOT_EXISTS");
    ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1_002_020_001, "OAuth2客户端编号已存在", "OAUTH2_CLIENT_EXISTS");
    ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1_002_020_002, "OAuth2客户端已禁用", "OAUTH2_CLIENT_DISABLE");
    ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS =
        new ErrorCode(1_002_020_003, "不支持该授权类型", "OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS");
    ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1_002_020_004, "授权范围过大", "OAUTH2_CLIENT_SCOPE_OVER");
    ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH =
        new ErrorCode(1_002_020_005, "无效redirect_uri:[{}]", "OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH");
    ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR =
        new ErrorCode(1_002_020_006, "无效client_secret:{}", "OAUTH2_CLIENT_CLIENT_SECRET_ERROR");

    // ========== OAuth2 授权 1-002-021-000 =========
    ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH =
        new ErrorCode(1_002_021_000, "client_id不匹配", "OAUTH2_GRANT_CLIENT_ID_MISMATCH");
    ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH =
        new ErrorCode(1_002_021_001, "redirect_uri不匹配", "OAUTH2_GRANT_REDIRECT_URI_MISMATCH");
    ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(1_002_021_002, "state不匹配", "OAUTH2_GRANT_STATE_MISMATCH");
    ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode(1_002_021_003, "code不存在", "OAUTH2_GRANT_CODE_NOT_EXISTS");

    // ========== OAuth2 授权 1-002-022-000 =========
    ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(1_002_022_000, "code不存在", "OAUTH2_CODE_NOT_EXISTS");
    ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(1_002_022_001, "验证码过期", "OAUTH2_CODE_EXPIRE");

    // ========== 邮箱账号 1-002-023-000 ==========
    ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(1_002_023_000, "邮箱账号不存在", "MAIL_ACCOUNT_NOT_EXISTS");
    ErrorCode MAIL_ACCOUNT_TEMPLATE_NOT_MATCH =
        new ErrorCode(1_002_023_001, "邮箱账号和模板不匹配", "MAIL_ACCOUNT_TEMPLATE_NOT_MATCH");

    // ========== 邮件模版 1-002-024-000 ==========
    ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_024_000, "邮件模版不存在", "MAIL_TEMPLATE_NOT_EXISTS");
    ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(1_002_024_001, "邮件模版code[{}]已存在", "MAIL_TEMPLATE_CODE_EXISTS");

    // ========== 邮件发送 1-002-025-000 ==========
    ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS =
        new ErrorCode(1_002_025_000, "模板参数[{}]缺失", "MAIL_SEND_TEMPLATE_PARAM_MISS");
    ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(1_002_025_001, "邮箱不存在", "MAIL_SEND_MAIL_NOT_EXISTS");
    ErrorCode MAIL_IS_EXISTS = new ErrorCode(1_002_025_008, "邮箱号已被使用", "MAIL_IS_EXISTS");
    ErrorCode MAIL_CODE_SEND_TOO_FAST = new ErrorCode(1_002_025_002, "邮箱发送过于频繁", "MAIL_CODE_SEND_TOO_FAST");
    ErrorCode MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY =
        new ErrorCode(1_002_025_003, "超过每日邮件发送数量", "MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY");
    ErrorCode MAIL_CODE_NOT_FOUND = new ErrorCode(1_002_025_004, "验证码不存在", "MAIL_CODE_NOT_FOUND");
    ErrorCode MAIL_CODE_EXPIRED = new ErrorCode(1_002_025_005, "验证码已过期", "MAIL_CODE_EXPIRED");
    ErrorCode MAIL_CODE_USED = new ErrorCode(1_002_025_006, "验证码已使用", "MAIL_CODE_USED");
    ErrorCode MAIL_CODE_NOT_CORRECT = new ErrorCode(1_002_025_007, "验证码不正确", "MAIL_CODE_NOT_CORRECT");
    ErrorCode Mail_CODE_SEND_FAIL = new ErrorCode(1_002_025_100, "邮件发送失败", "Mail_CODE_SEND_FAIL");

    // ========== 站内信模版 1-002-026-000 ==========
    ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode(1_002_026_000, "站内信模版不存在", "NOTIFY_TEMPLATE_NOT_EXISTS");
    ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE =
        new ErrorCode(1_002_026_001, "已经存在编码为[{}]的站内信模板", "NOTIFY_TEMPLATE_CODE_DUPLICATE");

    // ========== 站内信发送 1-002-028-000 ==========
    ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS =
        new ErrorCode(1_002_028_000, "模板参数[{}]缺失", "NOTIFY_SEND_TEMPLATE_PARAM_MISS");

    // ========== 代理模块 1-002-029-000 ==========
    ErrorCode AGENT_NOT_EXISTS = new ErrorCode(1_002_029_000, "代理不存在", "AGENT_NOT_EXISTS");
    ErrorCode AGENT_INVITE_CODE_EXISTS = new ErrorCode(1_002_029_001, "邀请码已经存在", "AGENT_INVITE_CODE_EXISTS");
    ErrorCode AGENT_HAS_DESCENDANT = new ErrorCode(1_002_029_002, "代理下还有子级代理，不能删除", "AGENT_HAS_DESCENDANT");
    ErrorCode AGENT_ANCESTOR_NOT_AVAILABLE = new ErrorCode(1_002_029_003, "不是有效的父代理", "AGENT_ANCESTOR_NOT_AVAILABLE");
    ErrorCode AGENT_HAS_NOT_ANCESTOR = new ErrorCode(1_002_029_004, "被移动代理没有任何父代理", "AGENT_HAS_NOT_ANCESTOR");
    ErrorCode AGENT_LOGIN_ACCOUNT_NOT_AVAILABLE = new ErrorCode(1_002_029_005, "代理登录账号不可用", "AGENT_LOGIN_ACCOUNT_NOT_AVAILABLE");

    // ========== 代理模块 1-002-030-000 ==========
    ErrorCode AUTH_NOT_EXISTS = new ErrorCode(1_002_030_000, "会员认证不存在", "AUTH_NOT_EXISTS");

    // ========== 币种 1-002-031-000 ==========
    ErrorCode CURRENCY_NOT_EXISTS = new ErrorCode(1_002_031_000, "币种不存在", "CURRENCY_NOT_EXISTS");
    ErrorCode CURRENCY_NOT_RATE = new ErrorCode(1_002_032_000, "该币种暂无汇率", "CURRENCYNOTRATE");

    ErrorCode BANNER_NOT_EXISTS = new ErrorCode(1_002_033_000, "BANNER不存在", "BANNER_NOT_EXISTS");

    // ========== 挖矿产品 1-002-034-000 ==========
    ErrorCode MINER_HAS_ORDER = new ErrorCode(1_002_034_000, "该产品下还有订单未完成", "MINER_HAS_ORDER");
    ErrorCode MINER_NOT_EXISTS = new ErrorCode(1_002_034_000, "数据不存在", "MINER_NOT_EXISTS");
    ErrorCode MINER_CYCLE_ERR = new ErrorCode(1_002_035_000, "挖矿产品周期小于1天", "MINER_CYCLE_ERR");
    ErrorCode MINER_AMOUNT_LIMIT = new ErrorCode(1_002_036_000, "投资金额低于最低金额", "MINER_AMOUNT_LIMIT");
    ErrorCode MINER_AMOUNT_MAX_LIMIT = new ErrorCode(1_002_037_000, "投资金额大于最大金额", "MINER_AMOUNT_MAX_LIMIT");
    ErrorCode MINER_ORDER_STATUS_ERROR = new ErrorCode(1_002_038_000, "订单状态错误", "MINER_ORDER_STATUS_ERROR");

    // ========== 系统租户域名 ==========
    ErrorCode TENANT_SERVER_NAME_NOT_EXISTS =
        new ErrorCode(1_002_034_000, "系统租户域名不存在", "TENANT_SERVER_NAME_NOT_EXISTS");
    ErrorCode TENANT_DICT_DATA_NOT_EXISTS =
        new ErrorCode(1_002_035_001, "租户字典数据不存在", "MessageConstants.TENANT_DICT_DATA_NOT_EXISTS");

    // ========== 协议管理相关 1_002_038_000 ==========
    ErrorCode AGREEMENT_NOT_EXISTS = new ErrorCode(1_002_038_000, "协议不存在", "AGREEMENT_NOT_EXISTS");
    ErrorCode AGREEMENT_TYPE_DUPLICATE = new ErrorCode(1_002_038_001, "该租户下已存在相同类型的协议", "AGREEMENT_TYPE_DUPLICATE");
    ErrorCode AGREEMENT_TITLE_DUPLICATE = new ErrorCode(1_002_038_002, "协议标题已存在", "AGREEMENT_TITLE_DUPLICATE");
    ErrorCode AGREEMENT_LANGUAGE_CANNOT_DELETE_LAST = new ErrorCode(1_002_038_003, "不能删除最后一种语言版本", "AGREEMENT_LANGUAGE_CANNOT_DELETE_LAST");
    ErrorCode AGREEMENT_LANGUAGE_NOT_EXISTS = new ErrorCode(1_002_038_004, "协议语言版本不存在", "AGREEMENT_LANGUAGE_NOT_EXISTS");
    ErrorCode AGREEMENT_LANGUAGE_ALREADY_EXISTS = new ErrorCode(1_002_038_005, "协议语言版本已存在", "AGREEMENT_LANGUAGE_ALREADY_EXISTS");
}