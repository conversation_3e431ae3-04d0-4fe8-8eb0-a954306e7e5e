package com.rf.exchange.module.candle.service.candle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

@Schema(description = "管理后台 - 预览k线")
@Data
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class AdminCandleListReqVO {

    private String code;

    private String bar;

    private Long startTs;

    private Long endTs;

}
