package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-27
 */
@Data
public class ControlPlanPreviewRespVO {

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "临时key")
    private String tmpKey;

    @Schema(description = "k线列表")
    private List<CandleList> candleList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CandleList {

        @Schema(description = "时间")
        private Integer time;

        @Schema(description = "单位")
        private String unit;

        @Schema(description = "k线条数")
        private Integer count;

        @Schema(description = "k线列表")
        private List<CandleDO> candles;
    }
}
