package com.rf.exchange.module.candle.service.candlecontrol;

import com.rf.exchange.module.candle.controller.admin.vo.AdminCandlePreviewReqVo;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;

import java.math.BigDecimal;
import java.util.List;

public interface CandleControlService {

    List<CandleDO> genControlCandleOneMinute(TradePairRespDTO tradePairDO,
                                             BigDecimal controlOneMinuteTurnover,
                                             long startTime,
                                             BigDecimal startPrice,
                                             long endTime,
                                             BigDecimal endPrice);

    void saveControlCandleList(AdminCandlePreviewReqVo adminCandlePreviewReqVo, List<CandleDO> list);

    ///**
    // * 保存复制币的控盘信息
    // *
    // * @param controlReqVO 复制币的控制信息
    // */
    //void saveCopyTradeControl(AdminCandleCopyControlReqVO controlReqVO);

    List<CandleDO> genMinuteOne(TradePairRespDTO tradePairDO,
                                List<CandleDO> refCandleList,
                                long startTime,
                                int durationMinutes,
                                float oneMinuteTurnover,
                                BigDecimal currentPrice);

    void batchSaveCandleList(TradePairRespDTO tradePairDO, List<CandleDO> list);

}
