package com.rf.exchange.module.candle.dal.redis;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.rf.exchange.module.candle.dal.redis.RedisKeyConstants.CANDLE_CONTROL_PRICE_POINT;

/**
 * <AUTHOR>
 * @since 2024-11-03
 */
@Repository
public class CandleControlPricePointRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存控盘的价格节点列表
     *
     * @param pricePointList 价格节点列表
     */
    public void savePricePoints(long planId, List<ControlPricePointDO> pricePointList) {
        Map<String, String> pricePointListMap = new HashMap<>();
        for (ControlPricePointDO pricePointDO : pricePointList) {
            final String json = JsonUtils.toJsonString(pricePointDO);
            pricePointListMap.put(pricePointDO.getTimestamp().toString(), json);
        }
        redisTemplate.opsForHash().putAll(formatKey(planId), pricePointListMap);
        redisTemplate.expire(formatKey(planId), 3, TimeUnit.DAYS);
    }

    /**
     * 获取控盘的价格节点列表
     *
     * @return 价格节点列表
     */
    public Map<Long, ControlPricePointDO> getPricePoints(long planId) {
        final Map<Object, Object> entries = redisTemplate.opsForHash().entries(formatKey(planId));
        if (CollUtil.isEmpty(entries)) {
            return Map.of();
        }
        Map<Long, ControlPricePointDO> resultMap = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            resultMap.put(Long.parseLong((String) entry.getKey()), JsonUtils.parseObject(entry.getValue().toString(), ControlPricePointDO.class));
        }
        return resultMap;
    }

    /**
     * 获取指定时间的价格节点信息
     *
     * @param planId    计划id
     * @param timestamp 时间戳
     * @return 价格信息
     */
    public ControlPricePointDO getPricePoint(long planId, long timestamp) {
        final String json = (String) redisTemplate.opsForHash().get(formatKey(planId), String.valueOf(timestamp));
        return JsonUtils.parseObject(json, ControlPricePointDO.class);
    }

    /**
     * 删除控盘计划的价格节点
     *
     * @param planId 计划id
     */
    public void deletePricePoints(long planId) {
        redisTemplate.delete(formatKey(planId));
    }

    private String formatKey(long planId) {
        return CANDLE_CONTROL_PRICE_POINT + ":" + planId;
    }
}
