package com.rf.exchange.module.candle.controller.app;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.jackson.core.util.PrecisionUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.tenant.core.aop.TenantIgnore;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryReqVO;
import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryRespVO;
import com.rf.exchange.module.candle.service.candle.CandleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * APP K线简化数据控制器
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Tag(name = "APP - K线简化数据")
@RestController
@RequestMapping("/candle")
@Validated
public class AppKlineSummaryController {

    @Resource
    private CandleService candleService;

    @Operation(summary = "获取K线简化数据（按资产类型）")
    @GetMapping("/kline-summary")
    @PreAuthenticated
    @TenantIgnore
    public CommonResult<List<AppKlineSummaryRespVO>> getKlineSummaryByAssetType(@Valid AppKlineSummaryReqVO reqVO) {
        // 使用高精度（8位小数）执行K线数据查询
        return PrecisionUtils.withHighPrecision(() -> {
            List<AppKlineSummaryRespVO> result = candleService.appGetKlineSummaryByAssetType(reqVO, getTenantId(), getLoginUserId());
            return success(result);
        });
    }
}
