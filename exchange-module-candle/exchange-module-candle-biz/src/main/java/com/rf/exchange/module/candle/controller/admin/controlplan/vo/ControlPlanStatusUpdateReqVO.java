package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-10-26
 */
@Schema(description = "管理后台 - 复制币和自发币的控盘计划状态修改 Request VO")
@Data
public class ControlPlanStatusUpdateReqVO {

    @Schema(description = "计划id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划id不能为空")
    private Long id;

    @Schema(description = "控盘计划状态 0:等待中 1:关闭", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;
}
