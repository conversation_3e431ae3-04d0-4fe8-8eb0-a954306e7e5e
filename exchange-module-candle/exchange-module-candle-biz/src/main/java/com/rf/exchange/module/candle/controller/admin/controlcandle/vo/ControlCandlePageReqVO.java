package com.rf.exchange.module.candle.controller.admin.controlcandle.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 控盘k线(用于接口数据替换)分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ControlCandlePageReqVO extends PageParam {

    @Schema(description = "交易对编码")
    private String code;

    @Schema(description = "最高价格", example = "4326")
    private BigDecimal highPrice;

    @Schema(description = "最低价格", example = "10838")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价格", example = "24311")
    private BigDecimal openPrice;

    @Schema(description = "收盘价格", example = "19377")
    private BigDecimal closePrice;

    @Schema(description = "成交量")
    private BigDecimal volume;

    @Schema(description = "成交额")
    private BigDecimal turnover;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "是否是休市数据")
    private Boolean isMarketClose;

}