//package com.rf.exchange.module.candle.controller.admin.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.NotNull;
//import lombok.Data;
//
//import java.math.BigDecimal;
//
///**
// * 复制币控盘信息
// *
// * <AUTHOR>
// * @since 2024-10-23
// */
//@Data
//public class AdminCandleCopyControlReqVO {
//
//    @Schema(description = "控盘的交易对id")
//    @NotNull(message = "tradePairId不能为空")
//    private Long tradePairId;
//
//    @Schema(description = "控盘开始时间")
//    @NotNull(message = "startTime不能为空")
//    private Long startTime;
//
//    @Schema(description = "最高价变差值")
//    private BigDecimal highPriceChange;
//
//    @Schema(description = "最低价的差值")
//    private BigDecimal lowPriceChange;
//
//    @Schema(description = "收盘价的差值")
//    private BigDecimal closePriceChange;
//
//    @Schema(description = "租户id")
//    @NotNull(message = "租户id不能为空")
//    private Long tenantId;
//}
