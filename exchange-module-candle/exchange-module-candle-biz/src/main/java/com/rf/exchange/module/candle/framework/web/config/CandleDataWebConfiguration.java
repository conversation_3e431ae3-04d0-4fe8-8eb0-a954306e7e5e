package com.rf.exchange.module.candle.framework.web.config;

import com.rf.exchange.framework.swagger.config.ExchangeSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
@Configuration(proxyBeanMethods = false)
public class CandleDataWebConfiguration {

    /**
     * Candle 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi candleGroupedOpenApi() {
        return ExchangeSwaggerAutoConfiguration.buildGroupedOpenApi("candle", "k线行情");
    }

}
