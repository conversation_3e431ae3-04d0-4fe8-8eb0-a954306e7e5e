package com.rf.exchange.module.candle.dal.redis;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface RedisKeyConstants {

    String PRIVATE_KEY_PREFIX = "exch:candle:";

    /**
     * 交易对当前价格的缓存
     * <p>
     * KEY 格式: candle_current_price:{id}
     * VALUE 数据格式：String 模版信息 *
     */
    String CANDLE_CURRENT_PRICE = PRIVATE_KEY_PREFIX + "current_price";

    /**
     * 交易对的休市价格备份
     */
    String CANDLE_PRICE_MARKET_CLOSE_BACKUP = PRIVATE_KEY_PREFIX + "price_market_close_backup";

    /**
     * 交易对当前的订单薄缓存
     */
    String CANDLE_CURRENT_ORDER_BOOK = PRIVATE_KEY_PREFIX + "current_order_book";

    /**
     * 交易对的今日1日k线
     */
    String CANDLE_TODAY_KLINE = PRIVATE_KEY_PREFIX + "today_kline_1d";

    String CUSTOM_TRADE_PAIR_CANDLE = PRIVATE_KEY_PREFIX + "custom:";

    /**
     * 控盘计划的价格节点
     */
    String CANDLE_CONTROL_PRICE_POINT = PRIVATE_KEY_PREFIX + "control_price";

    /**
     * 控盘计划的临时k线
     */
    String CANDLE_CONTROL_KLINE = PRIVATE_KEY_PREFIX + "control_kline";
}
