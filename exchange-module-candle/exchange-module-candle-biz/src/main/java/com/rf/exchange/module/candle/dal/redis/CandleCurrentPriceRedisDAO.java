package com.rf.exchange.module.candle.dal.redis;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.monitor.TracerUtils;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 交易对当前价格的redis DAO
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@Slf4j
@Repository
public class CandleCurrentPriceRedisDAO {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    public void set(String tradePairCode, CurrentPriceRespDTO priceDto) {
        String jsonStr = JsonUtils.toJsonString(priceDto);
        redisTemplate.opsForHash().put(RedisKeyConstants.CANDLE_CURRENT_PRICE, tradePairCode, jsonStr);
    }

    public CurrentPriceRespDTO get(String tradePairCode) {
        Object val = redisTemplate.opsForHash().get(RedisKeyConstants.CANDLE_CURRENT_PRICE, tradePairCode);
        return JsonUtils.parseObject((String) val, CurrentPriceRespDTO.class);
    }

    /**
     * 获取所有交易对的当前价格
     *
     * @return 交易对价格的字典 格式：<交易对code : 价格信息>
     */
    public Map<String, CurrentPriceRespDTO> getAll() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(RedisKeyConstants.CANDLE_CURRENT_PRICE);
        return getPriceRespDTOMap(entries);
    }

    /**
     * 获取所有交易对的当前价格
     *
     * @return 交易对价格的字典 格式：<交易对code : 价格信息>
     */
    public Map<String, CurrentPriceRespDTO> getMultiKeys(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyMap();
        }
        Collection<Object> lKeys = new ArrayList<>(keys);
        List<Object> objects = redisTemplate.opsForHash().multiGet(RedisKeyConstants.CANDLE_CURRENT_PRICE, lKeys);
        Map<String, CurrentPriceRespDTO> result = new HashMap<>();
        for (Object object : objects) {
            if (object == null) {
                continue;
            }
            CurrentPriceRespDTO dto = parseJson((String) object);
            if (dto != null) {
                result.put(dto.getTradePairCode(), dto);
            }
        }
        return result;
    }

    /**
     * 更新交易对的休市价格备份
     *
     * @param tradePairCode 交易对代码
     * @param priceDto      价格信息
     */
    public void setCloseBackupPrice(String tradePairCode, CurrentPriceRespDTO priceDto) {
        String jsonStr = JsonUtils.toJsonString(priceDto);
        redisTemplate.opsForHash().put(RedisKeyConstants.CANDLE_PRICE_MARKET_CLOSE_BACKUP, tradePairCode, jsonStr);
    }

    /**
     * 获取交易对的休市价格备份
     *
     * @param tradePairCode 交易对代码
     * @return 价格信息
     */
    public CurrentPriceRespDTO getCloseBackupPrice(String tradePairCode) {
        Object val = redisTemplate.opsForHash().get(RedisKeyConstants.CANDLE_PRICE_MARKET_CLOSE_BACKUP, tradePairCode);
        return JsonUtils.parseObject((String) val, CurrentPriceRespDTO.class);
    }

    /**
     * 获取所有交易对的休市价格备份
     * @return 价格信息Map
     */
    public Map<String, CurrentPriceRespDTO> getAllCloseBackupPrice() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(RedisKeyConstants.CANDLE_PRICE_MARKET_CLOSE_BACKUP);
        return getPriceRespDTOMap(entries);
    }

    private Map<String, CurrentPriceRespDTO> getPriceRespDTOMap(Map<Object, Object> entries) {
        Map<String, CurrentPriceRespDTO> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            CurrentPriceRespDTO dto = parseJson((String) entry.getValue());
            if (dto != null) {
                result.put((String) entry.getKey(), dto);
            }
        }
        return result;
    }

    private CurrentPriceRespDTO parseJson(String jsonStr) {
        try {
            return JsonUtils.parseObject(jsonStr, CurrentPriceRespDTO.class);
        } catch (RuntimeException e) {
            log.error("解析redis中的交易对价格信息异常 {} traceId:{}", e.getMessage(), TracerUtils.getTraceId());
        }
        return null;
    }
}
