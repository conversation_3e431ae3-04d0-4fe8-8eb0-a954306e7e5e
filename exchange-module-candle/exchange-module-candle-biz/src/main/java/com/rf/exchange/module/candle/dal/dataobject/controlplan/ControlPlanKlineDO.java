package com.rf.exchange.module.candle.dal.dataobject.controlplan;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 控盘计划的k线 DO
 * 还未根据控盘计划生效时间改变实际的k线数据
 *
 * <AUTHOR>
 */
@TableName("candle_control_plan_kline")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlPlanKlineDO implements Serializable, TransPojo {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 控盘计划的id
     */
    private Long planId;
    /**
     * 交易对编码
     */
    private String code;
    /**
     * 引用价格
     */
    private BigDecimal referPrice;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型 1分钟 2小时 3日 4月
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 最高价格
     */
    private BigDecimal highPrice;
    /**
     * 最低价格
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 成交额
     */
    private BigDecimal turnover;
    /**
     * 是否是休市数据
     */
    private Boolean isMarketClose;

}