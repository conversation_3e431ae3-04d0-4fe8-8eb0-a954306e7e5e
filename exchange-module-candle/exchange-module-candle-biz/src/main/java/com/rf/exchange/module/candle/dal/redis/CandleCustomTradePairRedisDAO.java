package com.rf.exchange.module.candle.dal.redis;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.service.candle.CandleService;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@Repository
public class CandleCustomTradePairRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CandleService candleService;

    private final int cacheMinute = 20;

    public Map<Long, CandleDO> get(String code, long startTime) {
        String key = RedisKeyConstants.CUSTOM_TRADE_PAIR_CANDLE + code;
        String json = stringRedisTemplate.opsForValue().get(key);
        List<CandleDO> list = null;
        if (!StrUtil.isEmpty(json)) {
            list = JsonUtils.parseArray(json, CandleDO.class);
            if (list.stream().filter(c -> c.getTimestamp() >= startTime).count() == 0) {
                list = null;
            }
        }
        if (list == null) {
            list = candleService.getCandleOneMinuteList(code, startTime, cacheMinute);
            String jsonString = JsonUtils.toJsonString(list);
            stringRedisTemplate.opsForValue().set(key, jsonString);
            stringRedisTemplate.expire(key, Duration.ofMinutes(cacheMinute));
        }

        Map<Long, CandleDO> map = list.stream()
                .collect(Collectors.toMap(
                        obj -> obj.getTimestamp() / 60, // 转为分钟为单位
                        obj -> obj,
                        (existing, replacement) -> replacement // 如果出现相同的 key，保留最新的值
                ));
        return map;
    }

    public void clear(String code) {
        String key = RedisKeyConstants.CUSTOM_TRADE_PAIR_CANDLE + code;
        stringRedisTemplate.delete(key);
    }

    public void clearAll() {
        stringRedisTemplate.delete(RedisKeyConstants.CUSTOM_TRADE_PAIR_CANDLE);
    }
}
