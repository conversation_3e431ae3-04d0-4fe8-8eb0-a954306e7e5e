package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 复制币和自发币的控盘计划参数新增/修改 Request VO")
@Data
public class ControlPlanPreviewReqVO {

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30793")
    @NotNull(message = "交易对id不能为空")
    private Long tradePairId;

    @Schema(description = "控盘开始时间(秒)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "控盘开始时间不能为空")
    @Digits(integer = 10, fraction = 0, message = "开始时间单位需要为秒")
    private Long startTime;

    @Schema(description = "控盘时长(分) 15分钟 30分钟 45分钟 60分钟")
    @NotNull(message = "控盘时长不能为空")
    private Integer minutes;

    @Schema(description = "结束价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "507")
    @NotNull(message = "结束价格不能为空")
    private BigDecimal endPrice;

    @Schema(description = "波动率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "波动率不能为空")
    private BigDecimal fluctuation;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED, example = "19202")
    private BigDecimal maxPrice;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3509")
    private BigDecimal minPrice;
}