package com.rf.exchange.module.candle.controller.admin.controlplan;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.module.candle.controller.admin.controlplan.vo.*;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;
import com.rf.exchange.module.candle.service.controlplan.ControlPlanService;
import com.rf.exchange.module.candle.service.controlplan.ControlPricePointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

@Tag(name = "管理后台 - 控盘k线")
@RestController
@RequestMapping("/candle/control-plan")
@Validated
public class ControlPlanController extends TenantBaseController {

    @Resource
    private ControlPlanService controlPlanService;
    @Resource
    private ControlPricePointService controlPricePointService;

    @PostMapping("/preview")
    @Operation(summary = "获取控盘参数的k线预览")
    @PreAuthorize("@ss.hasPermission('candle:control-plan:query')")
    public CommonResult<ControlPlanPreviewRespVO> getPlanPreview(@Valid @RequestBody ControlPlanPreviewReqVO reqVO) {
        setupTenantId();
        return success(controlPlanService.getControlPlanPreviewCandles(reqVO, getTenantId()));
    }

    @PostMapping("/save")
    @Operation(summary = "保存控盘参数及预览k线")
    @PreAuthorize("@ss.hasAnyPermissions('candle:control-plan:update')")
    public CommonResult<Boolean> saveControlPlan(@Valid @RequestBody ControlPlanPlanSaveReqVO reqVO) {
        setupTenantId();
        controlPlanService.saveControlPlanPreview(reqVO);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新控盘计划参数")
    @PreAuthorize("@ss.hasPermission('candle:control-plan:update')")
    public CommonResult<Boolean> updatePlanStatus(@Valid @RequestBody ControlPlanStatusUpdateReqVO updateReqVO) {
        setupTenantId();
        controlPlanService.updateControlPlanStatus(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除控盘计划")
    @Parameter(name = "id", description = "计划id", required = true)
    @PreAuthorize("@ss.hasPermission('candle:control-plan:delete')")
    public CommonResult<Boolean> deleteControlPlan(@RequestParam("id") Long id) {
        setupTenantId();
        controlPlanService.deleteControlPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得控盘计划详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('candle:control-plan:query')")
    public CommonResult<ControlPlanRespVO> getControlPlan(@RequestParam("id") Long id) {
        setupTenantId();
        ControlPlanDO controlPlan = controlPlanService.getControlPlan(id);
        return success(BeanUtils.toBean(controlPlan, ControlPlanRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得控盘计划分页")
    @PreAuthorize("@ss.hasPermission('candle:control-plan:query')")
    public CommonResult<PageResult<ControlPlanRespVO>> getControlPlanPage(@Valid ControlPlanPageReqVO pageReqVO) {
        setupTenantId();
        PageResult<ControlPlanDO> pageResult = controlPlanService.getControlPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ControlPlanRespVO.class));
    }

    @GetMapping("/price-points")
    @Operation(summary = "价格节点列表")
    @Parameter(name = "planId", description = "计划id", required = true)
    @PreAuthorize("@ss.hasPermission('candle:control-plan:query')")
    public CommonResult<List<ControlPricePointRespVO>> getControlPricePoint(@RequestParam("planId") Long id) {
        Map<Long, ControlPricePointDO> pricePointDOMap = controlPricePointService.getControlPricePointListOfPlan(id);
        List<ControlPricePointDO> list = new ArrayList<>(pricePointDOMap.values());
        return success(BeanUtils.toBean(list, ControlPricePointRespVO.class));
    }

}