package com.rf.exchange.module.candle.dal.dataobject.controlplan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 控盘k线(用于接口数据替换) DO
 * <p>
 * 实体和CandleDO实际上是一样的,只是CandleDO是按照交易对区分的存储的，这个ControlCandle是不区分交易对的，
 * 所有交易对的控盘k线都在这个表中
 * 如果后续这个表中的数据过大的话，需要优化
 *
 * <AUTHOR>
 */
@TableName("data_control_candle")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlCandleDO implements Serializable, TransPojo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型 1分钟 2小时 3日 4月
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 交易对编码
     */
    private String code;
    /**
     * 最高价格
     */
    private BigDecimal highPrice;
    /**
     * 最低价格
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 成交额
     */
    private BigDecimal turnover;
    /**
     * 是否是休市数据
     */
    private Boolean isMarketClose;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}