package com.rf.exchange.module.candle.controller.admin.controlcandle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 控盘k线(用于接口数据替换) Response VO")
@Data
@ExcelIgnoreUnannotated
public class ControlCandleRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28716")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "时间范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时间范围")
    private Integer timeRange;

    @Schema(description = "时间范围类型 1分钟 2小时 3日 4月", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("时间范围类型 1分钟 2小时 3日 4月")
    private Integer timeType;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时间戳")
    private Long timestamp;

    @Schema(description = "交易对编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对编码")
    private String code;

    @Schema(description = "最高价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "4326")
    @ExcelProperty("最高价格")
    private BigDecimal highPrice;

    @Schema(description = "最低价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "10838")
    @ExcelProperty("最低价格")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "24311")
    @ExcelProperty("开盘价格")
    private BigDecimal openPrice;

    @Schema(description = "收盘价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "19377")
    @ExcelProperty("收盘价格")
    private BigDecimal closePrice;

    @Schema(description = "成交量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("成交量")
    private BigDecimal volume;

    @Schema(description = "成交额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("成交额")
    private BigDecimal turnover;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否是休市数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否是休市数据")
    private Boolean isMarketClose;

}