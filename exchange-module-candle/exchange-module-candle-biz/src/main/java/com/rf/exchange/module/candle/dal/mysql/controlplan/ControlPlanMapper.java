package com.rf.exchange.module.candle.dal.mysql.controlplan;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.controller.admin.controlplan.vo.ControlPlanPageReqVO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 复制币和自发币的控盘计划参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ControlPlanMapper extends BaseMapperX<ControlPlanDO> {

    default PageResult<ControlPlanDO> selectPage(ControlPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ControlPlanDO>()
                .eqIfPresent(ControlPlanDO::getTradePairId, reqVO.getTradePairId())
                .eqIfPresent(ControlPlanDO::getTradePairCode, reqVO.getTradePairCode())
                .betweenIfPresent(ControlPlanDO::getStartTime, reqVO.getStartTime())
                .eqIfPresent(ControlPlanDO::getEndPrice, reqVO.getEndPrice())
                .eqIfPresent(ControlPlanDO::getMinPrice, reqVO.getMinPrice())
                .eqIfPresent(ControlPlanDO::getMaxPrice, reqVO.getMaxPrice())
                .eqIfPresent(ControlPlanDO::getFluctuation, reqVO.getFluctuation())
                .eqIfPresent(ControlPlanDO::getDurationSecs, reqVO.getDurationSecs())
                .eqIfPresent(ControlPlanDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ControlPlanDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ControlPlanDO::getId));
    }

}