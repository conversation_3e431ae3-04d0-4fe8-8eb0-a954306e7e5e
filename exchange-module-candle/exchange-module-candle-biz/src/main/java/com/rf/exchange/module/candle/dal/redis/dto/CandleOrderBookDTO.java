package com.rf.exchange.module.candle.dal.redis.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 挂单订单薄
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
public class CandleOrderBookDTO implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    /**
     * 价格
     */
    private String price;
    /**
     * 成交量
     */
    private String volume;
}
