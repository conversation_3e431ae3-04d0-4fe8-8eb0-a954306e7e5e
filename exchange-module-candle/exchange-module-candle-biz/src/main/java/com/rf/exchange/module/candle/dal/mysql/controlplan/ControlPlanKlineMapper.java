package com.rf.exchange.module.candle.dal.mysql.controlplan;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 控盘计划的k线 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ControlPlanKlineMapper extends BaseMapperX<ControlPlanKlineDO> {

    void batchInsertPlanKLines(@Param("planKLines") List<ControlPlanKlineDO> kLines);
}