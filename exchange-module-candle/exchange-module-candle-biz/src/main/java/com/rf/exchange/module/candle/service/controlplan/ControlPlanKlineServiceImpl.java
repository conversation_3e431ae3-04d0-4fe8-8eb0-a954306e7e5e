package com.rf.exchange.module.candle.service.controlplan;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;
import com.rf.exchange.module.candle.dal.mysql.controlplan.ControlPlanKlineMapper;
import com.rf.exchange.module.candle.dal.redis.CandleControlPlanKlineRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 控盘计划的k线 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ControlPlanKlineServiceImpl implements ControlPlanKlineService {

    @Resource
    private ControlPlanKlineMapper controlPlanKlineMapper;
    @Resource
    private CandleControlPlanKlineRedisDAO controlPlanKlineRedisDAO;

    @Override
    @Master
    @DSTransactional
    public void savePlanKlineList(ControlPlanDO planDO, List<ControlPlanKlineDO> planKlineDOList) {
        if (CollUtil.isEmpty(planKlineDOList)) {
            return;
        }
        controlPlanKlineMapper.batchInsertPlanKLines(planKlineDOList);
    }

    @Override
    public List<ControlPlanKlineDO> getControlPlanKlineListByPlanId(long planId) {
        final List<ControlPlanKlineDO> pricePoints = controlPlanKlineRedisDAO.getPlanKLines(planId);
        if (CollUtil.isNotEmpty(pricePoints)) {
            return pricePoints;
        }
        LambdaQueryWrapperX<ControlPlanKlineDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ControlPlanKlineDO::getPlanId, planId);
        final List<ControlPlanKlineDO> kLines = controlPlanKlineMapper.selectList(wrapper);

        controlPlanKlineRedisDAO.savePlanKLines(planId, kLines);
        return kLines;
    }
}