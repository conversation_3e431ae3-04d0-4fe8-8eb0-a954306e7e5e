package com.rf.exchange.module.candle.service.controlplan;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.controller.admin.controlplan.vo.*;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;
import com.rf.exchange.module.candle.dal.mysql.controlplan.ControlPlanMapper;
import com.rf.exchange.module.candle.dal.redis.CandleControlPlanRedisDAO;
import com.rf.exchange.module.candle.enums.CandleControlPlanStatusEnum;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.controlcandle.ControlCandleService;
import com.rf.exchange.module.candle.util.CandleControlCurveGenerator;
import com.rf.exchange.module.candle.util.CandleKLineAggregate;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.*;

/**
 * 复制币和自发币的控盘计划参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ControlPlanServiceImpl implements ControlPlanService {

    @Resource
    private ControlPlanMapper controlPlanMapper;
    @Resource
    private CandleControlPlanRedisDAO controlPlanRedisDAO;
    @Resource
    private ControlPricePointService pricePointService;
    @Resource
    private ControlPlanKlineService planKlineService;
    @Resource
    private ControlCandleService controlCandleService;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;
    @Resource
    @Lazy
    private CandleDataApi candleDataApi;

    @Override
    public void updateControlPlanStatus(ControlPlanStatusUpdateReqVO reqVO) {
        final ControlPlanDO existsPlan = validateControlPlanExists(reqVO.getId());

        CandleControlPlanStatusEnum fromStatus = CandleControlPlanStatusEnum.statusOf(existsPlan.getStatus());
        CandleControlPlanStatusEnum targetStatus = CandleControlPlanStatusEnum.statusOf(reqVO.getStatus());

        if (fromStatus == CandleControlPlanStatusEnum.RUNNING && targetStatus == CandleControlPlanStatusEnum.CLOSED) {
            throw exception(CONTROL_PLAN_CANNOT_STOP);
        } else if (fromStatus == CandleControlPlanStatusEnum.CLOSED && targetStatus == CandleControlPlanStatusEnum.WAITING) {
            // 如果从关闭状态再次打开，需要判断startTime是否过期了
            long now = System.currentTimeMillis() / 1000;
            if (now > existsPlan.getStartTime()) {
                throw exception(CONTROL_PLAN_EXPIRE);
            }
        }

        ControlPlanDO update = new ControlPlanDO();
        update.setId(reqVO.getId());
        update.setStatus(reqVO.getStatus());
        controlPlanMapper.updateById(update);

        // 计划的状态转变则需要处理redis缓存
        controlPlanRedisDAO.transferPlanStatus(existsPlan, fromStatus, targetStatus);
    }

    @Override
    public void deleteControlPlan(Long id) {
        // 校验存在
        final ControlPlanDO existsPlan = validateControlPlanExists(id);
        if (!existsPlan.getStatus().equals(CandleControlPlanStatusEnum.WAITING.getStatus())) {
            throw exception(CONTROL_PLAN_CANNOT_DELETE);
        }
        // 删除
        controlPlanMapper.deleteById(id);
        controlPlanRedisDAO.removePlanFromWait(existsPlan.getId());
    }

    private ControlPlanDO getPlanByCodeAndStartEndTime(String code, long startTime, long endTime) {
        LambdaQueryWrapperX<ControlPlanDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(ControlPlanDO::getTradePairCode, code);
        wrapper.geIfPresent(ControlPlanDO::getStartTime, startTime);
        wrapper.leIfPresent(ControlPlanDO::getEndTime, endTime);
        wrapper.last("limit 1");
        return controlPlanMapper.selectOne(wrapper);
    }

    private ControlPlanDO validateControlPlanExistsByCodeAndTS(String code, long startTime, long endTime) {
        final ControlPlanDO planDO = getPlanByCodeAndStartEndTime(code, startTime, endTime);
        if (planDO == null) {
            throw exception(CONTROL_PLAN_NOT_EXISTS);
        }
        return planDO;
    }

    private ControlPlanDO validateControlPlanExists(Long id) {
        final ControlPlanDO planDO = controlPlanMapper.selectById(id);
        if (planDO == null) {
            throw exception(CONTROL_PLAN_NOT_EXISTS);
        }
        return planDO;
    }

    @Override
    @Slave
    public ControlPlanDO getControlPlan(Long id) {
        return controlPlanMapper.selectById(id);
    }

    @Override
    @Slave
    public PageResult<ControlPlanDO> getControlPlanPage(ControlPlanPageReqVO pageReqVO) {
        return controlPlanMapper.selectPage(pageReqVO);
    }

    @Override
    public ControlPlanPreviewRespVO getControlPlanPreviewCandles(ControlPlanPreviewReqVO previewReqVO, Long tenantId) {
        // 处理控盘计划的参数

        // 处理起始时间和结束时间必须是可以被5分钟整除的时间戳，因为k线需要生成5分钟，15分钟k线，所以不能从不能被5整除的分钟开始生成
        long startTime = nextTsMultipleOfRange(previewReqVO.getStartTime(), 15);
        long endTime = startTime + previewReqVO.getMinutes() * 60;

        // 控盘起始时间当天最后的时间戳
        final LocalDateTime dateTime = Instant.ofEpochSecond(startTime).atOffset(ZoneOffset.ofHours(8)).toLocalDateTime();
        LocalDateTime startOfDay = dateTime.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusSeconds(1);
        long endOfDayTS = endOfDay.toEpochSecond(ZoneOffset.UTC);
        // 控盘时间不能超过当天最后一秒
        if (endTime > endOfDayTS) {
            throw exception(CONTROL_PLAN_CROSS_DAY);
        }

        // 校验交易对是否存在
        final TradePairRespDTO tradePair = tradePairApi.validateTradePairExists(previewReqVO.getTradePairId());

        // 获取当前已经存在的控盘计划，判断控盘计划的时间范围是否有重叠
        LambdaQueryWrapperX<ControlPlanDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(ControlPlanDO::getTradePairCode, tradePair.getCode());
        wrapper.eqIfPresent(ControlPlanDO::getStatus, CandleControlPlanStatusEnum.WAITING.getStatus());
        wrapper.or().eq(ControlPlanDO::getStatus, CandleControlPlanStatusEnum.RUNNING.getStatus());
        wrapper.inIfPresent(ControlPlanDO::getTradePairId, tradePair.getId());

        final List<ControlPlanDO> existsPlanList = controlPlanMapper.selectList(wrapper);
        for (ControlPlanDO existsPlan : existsPlanList) {
            if ((existsPlan.getStartTime() <= startTime && existsPlan.getEndTime() >= startTime) || (
                    existsPlan.getStartTime() <= endTime && existsPlan.getEndTime() <= endTime)) {
                throw exception(CONTROL_PLAN_TIME_CONFLICT);
            }
        }

        long now = System.currentTimeMillis() / 1000;

        // TODO: 放开注释
        // 起始时间必须要在当前时间之后
        //if (previewReqVO.getStartTime() <= now) {
        //    throw exception(CONTROL_PLAN_START_TIME_AFTER_NOW);
        //}

        // 交易对当前的价格
        final CurrentPriceRespDTO currentPrice = candleDataApi.getCurrentPrice(tradePair.getCode());

        // 这里startPrice从0开始因为此刻生成的价格曲线基础价格并不是计划生效时刻的价格
        // 所以这里从0开始，只是用0至价格的涨跌幅作为生成曲线的价格起点和终点，然后再生成k线的时候需要考虑此曲线的引用价格
        double startPrice = 0;
        double endPrice = previewReqVO.getEndPrice().subtract(currentPrice.getCurrentPrice()).doubleValue();

        // 计算出曲线的最大最小值
        double maxPrice;
        double minPrice;
        if (previewReqVO.getMaxPrice() != null) {
            maxPrice = previewReqVO.getMaxPrice().doubleValue();
        } else {
            maxPrice = Math.max(startPrice, endPrice) * 1.02;
        }
        if (previewReqVO.getMinPrice() != null) {
            minPrice = previewReqVO.getMinPrice().doubleValue();
        } else {
            minPrice = Math.min(startPrice, endPrice) * 0.98;
        }

        // 控盘计划
        final ControlPlanDO planDO = BeanUtils.toBean(previewReqVO, ControlPlanDO.class);
        planDO.setTradePairCode(tradePair.getCode());
        planDO.setStartTime(startTime);
        planDO.setEndTime(endTime);
        planDO.setDurationSecs(previewReqVO.getMinutes() * 60);
        planDO.setReferPrice(currentPrice.getCurrentPrice());
        planDO.setEndPrice(previewReqVO.getEndPrice());
        planDO.setMinPrice(BigDecimal.valueOf(minPrice));
        planDO.setMaxPrice(BigDecimal.valueOf(maxPrice));
        planDO.setFluctuation(previewReqVO.getFluctuation());
        planDO.setStatus(CandleControlPlanStatusEnum.WAITING.getStatus());
        planDO.setTenantId(tenantId);

        // 生成每秒价格曲线
        final List<ControlPricePointDTO> points = generatePricePoints(previewReqVO, tradePair, currentPrice.getCurrentPrice(), minPrice, maxPrice, endPrice, startTime, endTime);
        // 组装价格曲线数据为k线
        final CandleKLineAggregate candleAssembler = new CandleKLineAggregate();
        final Map<Integer, List<CandleDO>> candleListMap = candleAssembler.aggregatePricePoint(tradePair.getCode(), points);

        // 临时将k线和价格曲线数据保存到redis中
        String tmpSaveKey = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd_HHmmss") + RandomUtil.randomString(8);
        controlPlanRedisDAO.saveControlPlanData(tmpSaveKey, points, candleListMap, planDO);

        ControlPlanPreviewRespVO respVO = new ControlPlanPreviewRespVO();
        respVO.setCode(tradePair.getCode());
        respVO.setTmpKey(tmpSaveKey);

        List<ControlPlanPreviewRespVO.CandleList> candleLists = new ArrayList<>();
        for (Map.Entry<Integer, List<CandleDO>> entry : candleListMap.entrySet()) {
            String unit = null;
            Integer time = null;
            if (entry.getKey().equals(CandleTimeRangeEnum.MIN_ONE.getKLineTypeAllTick())) {
                time = CandleTimeRangeEnum.MIN_ONE.getRange();
                unit = CandleTimeRangeEnum.MIN_ONE.getTypeUnit();
            } else if (entry.getKey().equals(CandleTimeRangeEnum.MIN_FIVE.getKLineTypeAllTick())) {
                time = CandleTimeRangeEnum.MIN_FIVE.getRange();
                unit = CandleTimeRangeEnum.MIN_FIVE.getTypeUnit();
            } else if (entry.getKey().equals(CandleTimeRangeEnum.MIN_FIFTEEN.getKLineTypeAllTick())) {
                time = CandleTimeRangeEnum.MIN_FIFTEEN.getRange();
                unit = CandleTimeRangeEnum.MIN_FIFTEEN.getTypeUnit();
            } else if (entry.getKey().equals(CandleTimeRangeEnum.MIN_THIRTY.getKLineTypeAllTick())) {
                time = CandleTimeRangeEnum.MIN_THIRTY.getRange();
                unit = CandleTimeRangeEnum.MIN_THIRTY.getTypeUnit();
            } else if (entry.getKey().equals(CandleTimeRangeEnum.HOUR_ONE.getKLineTypeAllTick())) {
                time = CandleTimeRangeEnum.HOUR_ONE.getRange();
                unit = CandleTimeRangeEnum.HOUR_ONE.getTypeUnit();
            }
            if (unit != null) {
                ControlPlanPreviewRespVO.CandleList candleList = new ControlPlanPreviewRespVO.CandleList();
                candleList.setUnit(unit);
                candleList.setTime(time);
                candleList.setCandles(entry.getValue());
                candleList.setCount(entry.getValue().size());
                candleLists.add(candleList);
            }
        }
        respVO.setCandleList(candleLists);
        return respVO;
    }

    /**
     * 获取到晚于inputTS之后最大可以被range整除的时间戳
     *
     * @param inputTs  输入时间戳
     * @param duration 时间段长度(单位分钟)
     * @return 符合要求的时间戳
     */
    private long nextTsMultipleOfRange(long inputTs, int duration) {
        long tsInMin = inputTs / 60;
        long nextTs = ((tsInMin / duration) + 1) * duration;
        return nextTs * 60;
    }

    @Override
    @Master
    @DSTransactional
    @SuppressWarnings("unchecked")
    public void saveControlPlanPreview(ControlPlanPlanSaveReqVO reqVO) {
        // 先通过预览生成的key获取生成预览k线的参数和价格节点列表
        String previewKey = reqVO.getPreviewKey();

        final Map<String, Object> planData = controlPlanRedisDAO.getControlPlanData(previewKey);
        if (CollUtil.isEmpty(planData)) {
            throw exception(CONTROL_PLAN_NOT_EXISTS);
        }

        final ControlPlanDO planDO = (ControlPlanDO) planData.get("plan");
        if (planDO == null) {
            throw exception(CONTROL_PLAN_NOT_EXISTS);
        }

        // 判断控盘计划的时间段是否重叠
        final ControlPlanDO existsPlan = getPlanByCodeAndStartEndTime(planDO.getTradePairCode(), planDO.getStartTime(), planDO.getEndTime());
        if (existsPlan != null) {
            throw exception(CONTROL_PLAN_EXISTS);
        }
        controlPlanMapper.insert(planDO);

        final List<ControlPricePointDTO> priceLists = (List<ControlPricePointDTO>) planData.get("pricePoints");
        pricePointService.savePricePoints(planDO, priceLists);

        final Map<Integer, List<CandleDO>> candleMap = (Map<Integer, List<CandleDO>>) planData.get("candleMap");
        List<ControlPlanKlineDO> candleDOList = candleMap.values().stream().flatMap(List::stream).map(candleDO -> {
            final ControlPlanKlineDO kline = BeanUtils.toBean(candleDO, ControlPlanKlineDO.class);
            kline.setReferPrice(planDO.getReferPrice());
            kline.setPlanId(planDO.getId());
            kline.setTurnover(BigDecimal.ZERO);
            return kline;
        }).toList();
        planKlineService.savePlanKlineList(planDO, candleDOList);

        // 存储等待生效的控盘计划到redis中
        controlPlanRedisDAO.addPlanToWait(planDO);
    }


    @Override
    public ControlPlanDO getRunningPlanOfCode(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        final List<ControlPlanDO> waitPlans = controlPlanRedisDAO.getRunningPlans();
        for (ControlPlanDO planDO : waitPlans) {
            if (code.equalsIgnoreCase(planDO.getTradePairCode())) {
                return planDO;
            }
        }
        return null;
    }

    @Override
    public List<ControlPlanDO> getRunningPlans() {
        final List<ControlPlanDO> runningPlans = controlPlanRedisDAO.getRunningPlans();
        if (CollUtil.isNotEmpty(runningPlans)) {
            return runningPlans;
        }

        LambdaQueryWrapperX<ControlPlanDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ControlPlanDO::getStatus, CandleControlPlanStatusEnum.RUNNING.getStatus());
        wrapper.orderByAsc(ControlPlanDO::getStartTime);
        final List<ControlPlanDO> planDOList = controlPlanMapper.selectList(wrapper);

        controlPlanRedisDAO.saveRunningPlans(planDOList);

        return planDOList;
    }

    @Override
    public List<ControlPlanDO> getWaitingPlans() {
        final List<ControlPlanDO> waitingPlans = controlPlanRedisDAO.getWaitingPlans();
        if (CollUtil.isNotEmpty(waitingPlans)) {
            return waitingPlans;
        }

        LambdaQueryWrapperX<ControlPlanDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ControlPlanDO::getStatus, CandleControlPlanStatusEnum.WAITING.getStatus());
        wrapper.orderByAsc(ControlPlanDO::getStartTime);
        final List<ControlPlanDO> planDOList = controlPlanMapper.selectList(wrapper);
        controlPlanRedisDAO.saveWaitingPlans(planDOList);

        return controlPlanRedisDAO.getWaitingPlans();
    }

    @Override
    public void startPlan(long planId, BigDecimal execPrice) {
        final ControlPlanDO existsPlan = validateControlPlanExists(planId);
        Integer oldStatus = existsPlan.getStatus();
        ControlPlanDO update = new ControlPlanDO();
        update.setId(planId);
        update.setExecPrice(execPrice);
        update.setUpdater("system");
        update.setUpdateTime(DateUtil.currentSeconds());
        update.setStatus(CandleControlPlanStatusEnum.RUNNING.getStatus());
        controlPlanMapper.updateById(update);
        // 从等待的控盘计划中移除计划并添加到运行的计划中
        controlPlanRedisDAO.transferPlanStatus(existsPlan,
                CandleControlPlanStatusEnum.statusOf(oldStatus),
                CandleControlPlanStatusEnum.RUNNING);
    }

    @Override
    public void finishPlan(long planId) {
        final ControlPlanDO existsPlan = validateControlPlanExists(planId);
        Integer oldStatus = existsPlan.getStatus();

        ControlPlanDO update = new ControlPlanDO();
        update.setId(planId);
        update.setUpdater("system");
        update.setUpdateTime(DateUtil.currentSeconds());
        update.setStatus(CandleControlPlanStatusEnum.FINISHED.getStatus());
        controlPlanMapper.updateById(update);
        // 从redis中移除已经结束了的运行计划
        controlPlanRedisDAO.transferPlanStatus(existsPlan,
                CandleControlPlanStatusEnum.statusOf(oldStatus),
                CandleControlPlanStatusEnum.FINISHED);
    }

    /**
     * 生成每秒价格曲线
     *
     * @param previewReqVO 预览参数
     * @param tradePair    交易对
     * @param currentPrice 交易对当前的真实价格
     * @param startMin     控盘时长(秒)
     * @param endMin       起始时间(秒时间戳)
     * @return 价格曲线
     */
    private List<ControlPricePointDTO> generatePricePoints(ControlPlanPreviewReqVO previewReqVO,
                                                           TradePairRespDTO tradePair,
                                                           BigDecimal currentPrice,
                                                           double minPrice,
                                                           double maxPrice,
                                                           double endPrice,
                                                           long startMin,
                                                           long endMin) {
        double startPrice = 0;
        long seconds = (endMin - startMin);

        // 创建价格波动的随机曲线
        final CandleControlCurveGenerator curveGenerator = new CandleControlCurveGenerator(
                currentPrice,
                startPrice,
                endPrice,
                minPrice,
                maxPrice,
                previewReqVO.getFluctuation().doubleValue(),
                (int) seconds, startMin,
                tradePair.getScale(),
                4
        );
        return curveGenerator.generatePriceCurve();
    }

}