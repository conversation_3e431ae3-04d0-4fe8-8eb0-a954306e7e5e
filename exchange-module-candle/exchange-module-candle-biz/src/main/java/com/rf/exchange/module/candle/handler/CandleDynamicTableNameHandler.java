package com.rf.exchange.module.candle.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;

import java.time.LocalDateTime;
import java.util.List;

import static cn.hutool.core.date.DatePattern.*;

/**
 * k线分钟级别的数据库表名handler
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public class CandleDynamicTableNameHandler implements TableNameHandler {

    //每个请求线程维护一个month/year数据，避免多线程数据冲突。所以使用ThreadLocal
    private static final ThreadLocal<String> MONTH_DATA = new ThreadLocal<>();
    private static final ThreadLocal<String> YEAR_DATA = new ThreadLocal<>();

    //用于记录哪些表可以使用该月份/年份动态表名处理器（即哪些表按月分表哪些按照年份处理）
    private List<String> monthTableNames;
    private List<String> yearTableNames;

    //构造函数，构造动态表名处理器的时候，传递tableNames参数
    public CandleDynamicTableNameHandler(List<String> monthTableNames, List<String> yearTableNames) {
        this.monthTableNames = monthTableNames;
        this.yearTableNames = yearTableNames;
    }

    //设置请求线程的month数据
    public static void setMonth(String month) {
        MONTH_DATA.set(month);
    }

    //设置请求线程的month数据
    public static void setYear(String year) {
        YEAR_DATA.set(year);
    }

    public static void clear() {
        MONTH_DATA.remove();
        YEAR_DATA.remove();
    }

    @Override
    public String dynamicTableName(String sql, String tableName) {
        if (CollectionUtil.isNotEmpty(monthTableNames) && monthTableNames.contains(tableName)) {
            String month = MONTH_DATA.get();
            if (StrUtil.isEmpty(month)) {
                month = LocalDateTimeUtil.format(LocalDateTime.now(), SIMPLE_MONTH_PATTERN);
            }
            // 表名增加月份后缀
            return tableName + "_" + month;
        } else if (CollectionUtil.isNotEmpty(yearTableNames) && yearTableNames.contains(tableName)) {
            String year = YEAR_DATA.get();
            if (StrUtil.isEmpty(year)) {
                year = LocalDateTimeUtil.format(LocalDateTime.now(), NORM_DATE_PATTERN);
            }
            // 表名增加年份后缀
            return tableName + "_" + year;
        } else {
            return tableName;   //表名原样返回
        }
    }
}
