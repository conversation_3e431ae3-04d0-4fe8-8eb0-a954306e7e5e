package com.rf.exchange.module.candle.service.candlecontrol;

import cn.hutool.core.util.RandomUtil;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.candle.controller.admin.vo.AdminCandlePreviewReqVo;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.redis.CandleCustomTradePairRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.enums.CandleTimeTypeEnum;
import com.rf.exchange.module.candle.service.candle.CandleService;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.TradePairControlApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
@Validated
public class CandleControlServiceImpl implements CandleControlService {

    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private CandleService candleService;
    @Resource
    private TradePairControlApi tradePairControlApi;
    @Resource
    private CandleTodayKlinePriceRedisDAO candleTodayKlinePriceRedisDAO;
    @Resource
    private CandleCustomTradePairRedisDAO customTradePairCandleRedisDAO;

//    @Override
//    public void genCandleControl(TradePairControlRespDTO tradePairControlDO) {
//        TradePairRespDTO tradePairDo = tradePairApi.getTradePair(tradePairControlDO.getTradePairId());
//        //控盘开始时间
//        long startTime = tradePairControlDO.getStartTime() / 1000;
//        long endTime = tradePairControlDO.getEndTime() / 1000;
//        //获取开始时间的k线
//        CandleDO candleDO = candleService.getFirstCandle(tradePairControlDO.getTradePairCode(), startTime);
//        //有可能控盘的开始时间那根k线还没生成，那就找最后一根k线的时间
//        if (candleDO == null) {
//            candleDO = candleService.getLastCandle(tradePairDo.getCode(), CandleTimeRangeEnum.MIN_ONE, null, null);
//        }
//        if (candleDO != null) {
//            //生成交易对控盘一分钟中k线
//            List<CandleDO> minute1TkLines = genControlCandleOneMinute(tradePairDo,tradePairControlDO.getOneMinuteTurnover(), candleDO.getTimestamp() + 60, candleDO.getClosePrice(), endTime, tradePairControlDO.getEndPrice());
//            //在控盘的结束时间提前生成未来一天的k线，因为不知道控盘结束时间有可能跨天，所以提前一天生成各种时间范围的k线，这样才能提前聚合各个时间范围的k线
//            long future1DayEndTime = DateUtils.getAddDayStartTimeSecondsUtc(tradePairControlDO.getEndTime(), 1);
//            int durationMinutes = (int) (future1DayEndTime / 60 - endTime / 60);
//            //获取参考交易对的k线数据
//            List<CandleDO> refCandleList = candleService.getRefCandleMinuteOneList(tradePairDo.getReferenceCode(), durationMinutes);
//            List<CandleDO> future1DayMinute1TkLine = genMinuteOne(tradePairDo, refCandleList, endTime, durationMinutes, tradePairDo.getOneMinuteTurnover().floatValue(), tradePairControlDO.getEndPrice());
//            minute1TkLines.addAll(future1DayMinute1TkLine);
//            batchSaveCandleList(tradePairDo, minute1TkLines);
//            candleService.deleteExclude1MinuteCandle(tradePairControlDO.getTradePairCode(), startTime);
//            tradePairControlApi.setTradePairControlFinish(tradePairControlDO.getId());
//            customTradePairCandleRedisDAO.clear(tradePairDo.getCode());
//        } else {
//            //throw exception0();
//        }
//    }


    @Override
    public List<CandleDO> genControlCandleOneMinute(TradePairRespDTO tradePairDO,BigDecimal controlOneMinuteTurnover, long startTime, BigDecimal startPrice, long endTime, BigDecimal endPrice) {
        startTime = startTime / 60 * 60;  // 去除秒数
        endTime = endTime / 60 * 60;  // 去除秒数
        int durationMinutes = (int) ((endTime - startTime) / 60) + 1;  // 相差分钟数
        List<CandleDO> tkLines = new ArrayList<>();

        // 生成每分钟的随机波动
        List<BigDecimal> fluctuations = generateClosingPrices(startPrice, endPrice, durationMinutes);
        BigDecimal currentPrice = startPrice;

        for (int i = 0; i < durationMinutes; i++) {
            long timestamp = startTime + (i * 60);  // 时间戳
            CandleDO line = new CandleDO();
            line.setCode(tradePairDO.getCode());

            BigDecimal openPrice = currentPrice;  // 开盘价
//            BigDecimal fluctuation = fluctuations.get(i);  // 当前分钟波动值
//            currentPrice = currentPrice.add(fluctuation);  // 新的收盘价

//            // 防止价格跌至0以下
//            if (currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
//                currentPrice = startPrice;
//            }
            currentPrice = fluctuations.get(i);  // 当前分钟波动值


            // 随机生成最高价和最低价（高低影线）
            boolean hasUpperShadow = RandomUtil.randomBoolean();
            boolean hasLowerShadow = RandomUtil.randomBoolean();
            double high = openPrice.doubleValue();
            double low = openPrice.doubleValue();

            if (hasUpperShadow) {
                high = Math.max(openPrice.doubleValue(), currentPrice.doubleValue()) * (1 + RandomUtil.randomDouble(0, 0.03));  // 最高价
            }

            if (hasLowerShadow) {
                low = Math.min(openPrice.doubleValue(), currentPrice.doubleValue()) * (1 - RandomUtil.randomDouble(0, 0.03));  // 最低价
            }

            // 设置蜡烛线的开盘、收盘、最高、最低
            line.setTimeRange(CandleTimeRangeEnum.MIN_ONE.getRange());
            line.setTimeType(CandleTimeTypeEnum.MINUTES.getType());
            line.setTimestamp(timestamp);
            line.setOpenPrice(openPrice);
            line.setClosePrice(currentPrice);
            line.setHighPrice(new BigDecimal(high));
            line.setLowPrice(new BigDecimal(low));
            if (line.getOpenPrice().compareTo(line.getHighPrice()) > 0) {
                line.setHighPrice(line.getOpenPrice());
            }
            if (line.getClosePrice().compareTo(line.getHighPrice()) > 0) {
                line.setHighPrice(line.getClosePrice());
            }
            if (line.getOpenPrice().compareTo(line.getLowPrice()) < 0) {
                line.setLowPrice(line.getOpenPrice());
            }
            if (line.getClosePrice().compareTo(line.getLowPrice()) < 0) {
                line.setLowPrice(line.getClosePrice());
            }

            // 计算成交量和成交额
            BigDecimal turnover = controlOneMinuteTurnover
                    .add(controlOneMinuteTurnover.multiply(new BigDecimal(RandomUtil.randomFloat(-0.5f, 0.5f))));
            double volume = turnover.doubleValue() / low;
            if (low != high) {
                volume = turnover.doubleValue() / RandomUtil.randomDouble(low, high);
            }
            line.setVolume(new BigDecimal(volume));
            line.setTurnover(turnover);

            // 添加到结果集
            tkLines.add(line);
        }

        return tkLines;
    }

    @Override
    public void saveControlCandleList(AdminCandlePreviewReqVo adminCandlePreviewReqVo, List<CandleDO> list) {
        TradePairRespDTO tradePairDo = tradePairApi.getTradePair(adminCandlePreviewReqVo.getTradePairId());
        //控盘开始时间
        long startTime = adminCandlePreviewReqVo.getStartTime() / 1000;
        long endTime = adminCandlePreviewReqVo.getEndTime() / 1000;

        //生成控盘结束后未来1天的k线
        long future1DayEndTime = DateUtils.getAddDayStartTimeSecondsUtc(adminCandlePreviewReqVo.getEndTime(), 1);
        int durationMinutes = (int) (future1DayEndTime / 60 - endTime / 60);
        //获取参考交易对的k线数据
        List<CandleDO> refCandleList = candleService.getRefCandleMinuteOneList(tradePairDo.getReferenceCode(), durationMinutes);
        List<CandleDO> future1DayMinute1TkLine = genMinuteOne(tradePairDo, refCandleList, endTime, durationMinutes, tradePairDo.getOneMinuteTurnover().floatValue(), adminCandlePreviewReqVo.getEndPrice());
        list.addAll(future1DayMinute1TkLine);

        batchSaveCandleList(tradePairDo, list);
        //删除不是1分钟级别k线的其它k线
        candleService.deleteExclude1MinuteCandle(tradePairDo.getCode(), startTime);

        //tradePairControlApi.setTradePairControlFinish(tradePairControlDO.getId());
        customTradePairCandleRedisDAO.clear(tradePairDo.getCode());
        candleTodayKlinePriceRedisDAO.delete(tradePairDo.getCode());
    }

    public static List<BigDecimal> generateClosingPrices(BigDecimal startPrice, BigDecimal endPrice, int totalMinutes) {
        List<BigDecimal> closingPrices = new ArrayList<>();

        // Step 1: 计算平均涨幅
        BigDecimal totalChange = endPrice.subtract(startPrice);
        BigDecimal averageChange = totalChange.divide(BigDecimal.valueOf(totalMinutes), 8, RoundingMode.HALF_UP);

        // Step 2: 生成基础的平均上涨趋势
        BigDecimal currentPrice = startPrice;
        closingPrices.add(currentPrice); // 第一个收盘价是 startPrice
        for (int i = 1; i < totalMinutes; i++) {
            currentPrice = currentPrice.add(averageChange); // 每根K线加上平均涨幅
            closingPrices.add(currentPrice);
        }

        // Step 3: 随机选择几根K线进行大幅度涨跌（超过5%）
        BigDecimal fluctuationLimit = startPrice.multiply(BigDecimal.valueOf(0.02)); // 涨跌幅不能超过5%

        for (int i = 0; i < totalMinutes; i++) {
            // 随机选择K线进行波动
            int index = RandomUtil.randomInt(totalMinutes - 1) + 1;  // 避免修改第一个K线
            BigDecimal currentFluctuation = BigDecimal.valueOf((RandomUtil.randomFloat(-0.5f,0.5f)) * 2).multiply(fluctuationLimit);  // -5% 到 5%之间波动
            closingPrices.set(index, closingPrices.get(index).add(currentFluctuation));
        }

        // Step 4: 确保最终收盘价等于endPrice，通过抵消调整波动
        BigDecimal finalPrice = closingPrices.get(closingPrices.size() - 1);
        BigDecimal adjustment = endPrice.subtract(finalPrice);  // 调整最后的偏差
        closingPrices.set(closingPrices.size() - 1, endPrice);  // 最后一根直接设置为目标价格

        // 需要调整的偏差分配给前几根K线
        for (int i = 1; i < totalMinutes - 1 && adjustment.compareTo(BigDecimal.ZERO) != 0; i++) {
            BigDecimal currentAdjustment = adjustment.divide(BigDecimal.valueOf(totalMinutes - 1 - i), 8, RoundingMode.HALF_UP);
            closingPrices.set(i, closingPrices.get(i).add(currentAdjustment));
            adjustment = adjustment.subtract(currentAdjustment);
        }

        return closingPrices;
    }

    /**
     * 根据参考交易对生成k线
     *
     * @param tradePairDO       要生效k线的交易对
     * @param refCandleList     参考的k线列表
     * @param startTime         生成开始时间
     * @param durationMinutes   生成几条k线
     * @param oneMinuteTurnover 每分钟交易量
     * @return 返回生成的k线
     */
    @Override
    public List<CandleDO> genMinuteOne(TradePairRespDTO tradePairDO, List<CandleDO> refCandleList, long startTime, int durationMinutes, float oneMinuteTurnover, BigDecimal currentPrice) {
        List<CandleDO> tkLines = new ArrayList<>();
        //交易对小数位
        int tradePairScale = tradePairDO.getScale();
        // 交易对保留小数位
        String pattern = "#." + "0".repeat(tradePairScale);
        DecimalFormat df = new DecimalFormat(pattern);
        float previousClosePrice = currentPrice.floatValue();
        //取整分钟数，去掉秒数
        startTime = (startTime / 60) * 60;
        for (long i = 0; i < durationMinutes; i++) {
//            if (i == refCandleList.size()) {
//                Collections.shuffle(refCandleList);
//            }

            int candleIndex = (int) (i % refCandleList.size());
            CandleDO refCandle = refCandleList.get(candleIndex);

            long timestamp = startTime + (i * 60);
            CandleDO line = new CandleDO();
            line.setCode(tradePairDO.getCode());
            line.setTimeRange(CandleTimeRangeEnum.MIN_ONE.getRange());
            line.setTimeType(CandleTimeTypeEnum.MINUTES.getType());
            line.setTimestamp(timestamp);

            //region--------------开盘收盘计算----------------
            //算出参考k线收盘的涨跌及上下影线
            float closeUpDownRatio = refCandle.getClosePrice().subtract(refCandle.getOpenPrice()).divide(refCandle.getOpenPrice(), 4, RoundingMode.HALF_UP).floatValue();
            // 当前 K 线的开盘价是上一 K 线的收盘价
            float openPrice = previousClosePrice;
            // 收盘价
            float closePrice = openPrice + (openPrice * closeUpDownRatio);
            previousClosePrice = closePrice;

            // 设置开盘价和收盘价
            line.setOpenPrice(new BigDecimal(openPrice));
            line.setClosePrice(new BigDecimal(closePrice));
            //endregion--------------开盘收盘计算----------------

            //region--------------上影线下影下计算----------------
            float upShadowRatio = refCandle.getHighPrice().subtract(refCandle.getOpenPrice()).divide(refCandle.getOpenPrice(), 4, RoundingMode.HALF_UP).floatValue();
            float downShadowRatio = refCandle.getClosePrice().subtract(refCandle.getLowPrice()).divide(refCandle.getLowPrice(), 4, RoundingMode.HALF_UP).floatValue();

            float maxPrice = Math.max(openPrice, closePrice);
            float minPrice = Math.min(openPrice, closePrice);
            line.setHighPrice(new BigDecimal(maxPrice + (maxPrice * upShadowRatio)));
            line.setLowPrice(new BigDecimal(minPrice + (minPrice * -downShadowRatio)));
            //endregion--------------上影线下影下计算----------------

            //region--------------成交额优化计算----------------

            // 计算成交额
            float turnover = oneMinuteTurnover + (oneMinuteTurnover * RandomUtil.randomFloat(-0.5f, 0.5f)); // 使用开盘价和收盘价的平均值计算成交额
            // 每分钟成交量
            float volume = turnover / (RandomUtil.randomFloat(minPrice, maxPrice));
            // 设置成交量和成交额
            line.setVolume(new BigDecimal(volume));
            line.setTurnover(new BigDecimal(turnover));
            //endregion--------------成交额优化计算----------------
            tkLines.add(line);
        }
        return tkLines;
    }

    @Override
    public void batchSaveCandleList(TradePairRespDTO tradePairDO, List<CandleDO> list) {
        int batchSize = 1024 * 4;
        // 插入 K 线到数据库中
        for (int i = 0; i < list.size(); i += batchSize) {
            // 获取子列表（批次）
            List<CandleDO> batchList = list.subList(i, Math.min(i + batchSize, list.size()));
            //dataSyncService.insertCandleRecordsAndUpdateTradePairCurrentCandleTime(tradePairDO.getId(), endTime * 1000, tradePairDO.getCode(), null, batchList);
            candleService.batchInsertCandles(tradePairDO.getCode(), batchList);
        }
    }
}
