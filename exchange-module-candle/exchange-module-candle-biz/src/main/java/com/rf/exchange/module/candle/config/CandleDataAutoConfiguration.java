package com.rf.exchange.module.candle.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.rf.exchange.framework.mybatis.core.util.MyBatisUtils;
import com.rf.exchange.module.candle.handler.CandleDynamicTableNamePatternHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;


/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Slf4j
@EnableConfigurationProperties(CandleDataSourceProperties.class)
public class CandleDataAutoConfiguration {

    @Resource
    public CandleDataSourceProperties candleDataSourceProperties;

    @Bean
    public DynamicTableNameInnerInterceptor candleDynamicTableInterceptor(MybatisPlusInterceptor interceptor) {
        DynamicTableNameInnerInterceptor tableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        CandleDynamicTableNamePatternHandler patternHandler = new CandleDynamicTableNamePatternHandler("data_candle_tradepair");
        tableNameInnerInterceptor.setTableNameHandler(patternHandler);
        // 表名拦截器加载在第0个
        MyBatisUtils.addInterceptor(interceptor, tableNameInnerInterceptor, 0);
        return tableNameInnerInterceptor;
    }
}
