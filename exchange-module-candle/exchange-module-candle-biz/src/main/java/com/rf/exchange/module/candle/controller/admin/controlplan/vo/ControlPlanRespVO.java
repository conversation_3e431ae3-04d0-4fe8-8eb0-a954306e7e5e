package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 复制币和自发币的控盘计划参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ControlPlanRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "26678")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30793")
    @ExcelProperty("交易对id")
    private Long tradePairId;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String tradePairCode;

    @Schema(description = "控盘开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("控盘开始时间")
    private Long startTime;

    @Schema(description = "结束价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "507")
    @ExcelProperty("结束价格")
    private BigDecimal endPrice;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3509")
    @ExcelProperty("最低价")
    private BigDecimal minPrice;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED, example = "19202")
    @ExcelProperty("最低价")
    private BigDecimal maxPrice;

    @Schema(description = "波动率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("波动率")
    private BigDecimal fluctuation;

    @Schema(description = "波动时长(秒)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("波动时长(秒)")
    private Long durationSecs;

    @Schema(description = "控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}