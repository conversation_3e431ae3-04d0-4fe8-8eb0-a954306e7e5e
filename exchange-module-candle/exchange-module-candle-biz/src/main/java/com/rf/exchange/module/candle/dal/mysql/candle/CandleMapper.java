package com.rf.exchange.module.candle.dal.mysql.candle;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-07
 */
@Mapper
public interface CandleMapper extends BaseMapperX<CandleDO> {

    default Long selectRecordCount(@Param("timeRange") Integer timeRange, @Param("timeType") Integer timeType, Long startTime, Long endTime) {
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRange);
        wrapper.eqIfPresent(CandleDO::getTimeType, timeType);
        wrapper.geIfPresent(CandleDO::getTimestamp, startTime);
        wrapper.leIfPresent(CandleDO::getTimestamp, endTime);
        return selectCount(wrapper);
    }

    default CandleDO selectLastOne(@Param("timeRange") Integer timeRange, @Param("timeType") Integer timeType, Long startTime, Long endTime) {
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRange);
        wrapper.eqIfPresent(CandleDO::getTimeType, timeType);
        wrapper.geIfPresent(CandleDO::getTimestamp, startTime);
        wrapper.leIfPresent(CandleDO::getTimestamp, endTime);
        wrapper.orderByDesc(CandleDO::getTimestamp);
        wrapper.last("limit 1");
        return selectOne(wrapper);
    }

    void batchInsertCandle(@Param("candleList") List<CandleDO> candleDOS, @Param("tableName") String tableName);

    void batchInsertCandleNoUpdate(@Param("candleList") List<CandleDO> candleDOS, @Param("tableName") String tableName);
}
