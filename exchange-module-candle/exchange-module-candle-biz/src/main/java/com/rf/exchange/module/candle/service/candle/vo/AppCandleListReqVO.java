package com.rf.exchange.module.candle.service.candle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@Schema(description = "用户 App - K线")
@Data
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class AppCandleListReqVO {

    @Schema(description = "交易对代码")
    @NotEmpty(message = "code must not be null")
    private String code;

    @Schema(description = "k线类型 1m 5m 15m 30m 1H 2H 4H 1D 1W 1M")
    @NotNull(message = "bar must not be null")
    private String bar;

    @Schema(description = "结束时间戳")
    @NotNull(message = "endTs must not be null")
    private Long endTs;

    @Schema(description = "k线条数")
    @NotNull(message = "limit must not be null")
    private Long limit;

}
