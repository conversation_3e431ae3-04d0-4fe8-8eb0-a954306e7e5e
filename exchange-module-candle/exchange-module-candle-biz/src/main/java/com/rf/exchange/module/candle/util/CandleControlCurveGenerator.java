package com.rf.exchange.module.candle.util;

import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 控盘k线生成器
 *
 * <AUTHOR>
 * @since 2024-10-27
 */
public class CandleControlCurveGenerator {
    // 初始价格
    private final double startPrice;
    // 最终目标价格
    private final double endPrice;
    // 价格下限
    private final double minPrice;
    // 价格上限
    private final double maxPrice;
    // 波动率
    private final double fluctuationFactor;
    // 趋势时长(秒)
    private final int trendDuration;
    // 随机数
    private final Random random;
    // 开始时间戳
    private final long startTime;
    // 价格精度
    private final int priceScale;
    // 成交量精度
    private final int volumeScale;
    // 引用价格
    private final BigDecimal referPrice;

    public CandleControlCurveGenerator(BigDecimal referPrice,
                                       double startPrice,
                                       double endPrice,
                                       double minPrice,
                                       double maxPrice,
                                       double fluctuationFactor,
                                       int trendDuration,
                                       long startTime,
                                       int priceScale,
                                       int volumeScale) {
        this.startPrice = startPrice;
        this.endPrice = endPrice;
        this.minPrice = minPrice;
        this.maxPrice = maxPrice;
        this.fluctuationFactor = fluctuationFactor;
        this.trendDuration = trendDuration;
        this.random = new Random();
        this.priceScale = priceScale;
        this.volumeScale = volumeScale;
        this.referPrice = referPrice;

        // 是否是毫秒的时间戳
        boolean isMillTS = String.valueOf(startTime).length() == 13;
        if (isMillTS) {
            this.startTime = startTime / 1000;
        } else {
            this.startTime = startTime;
        }
    }

    // 生成价格波动曲线
    public List<ControlPricePointDTO> generatePriceCurve() {

        List<ControlPricePointDTO> priceCurve = new ArrayList<>(); // 存储每秒的价格和成交量
        double currentPrice = startPrice; // 当前价格初始化为起始价格

        // 根据 priceScale 确定波动的缩放因子和放大系数
        double scaleFactor = Math.pow(10, -priceScale);
        double fluctuationAmplifier = Math.pow(10, priceScale);

        // 计算每秒趋势的平均增量，使得价格从 startPrice 到 endPrice
        double trendIncrement = (endPrice - startPrice) / trendDuration;
        long ts = startTime;
        // 遍历 trendDuration 秒，生成每秒的价格和成交量
        for (int i = 0; i < trendDuration; i++) {

            // 生成一个随机波动值，使价格在趋势增量基础上产生波动
            double fluctuation = ((random.nextDouble() - 0.5) * fluctuationFactor);
            //double fluctuation = ((random.nextDouble() - 0.5) * fluctuationFactor * scaleFactor * fluctuationAmplifier);

            // 更新当前价格，加入趋势增量和随机波动
            currentPrice += trendIncrement + fluctuation;

            // 限制价格在 minPrice 和 maxPrice 范围内
            currentPrice = Math.max(minPrice, Math.min(maxPrice, currentPrice));

            // 模拟成交量，基础值为10，根据价格波动幅度随机生成
            double volume = 10 + random.nextDouble() * Math.abs(fluctuation) * 500;

            BigDecimal priceDiff = new BigDecimal(String.valueOf(currentPrice));
            priceDiff = priceDiff.setScale(priceScale, RoundingMode.HALF_DOWN);

            BigDecimal volumeDec = new BigDecimal(String.valueOf(volume));
            volumeDec = volumeDec.setScale(volumeScale, RoundingMode.HALF_DOWN);

            // 价格节点
            ControlPricePointDTO pricePoint = ControlPricePointDTO.builder()
                    .timestamp(ts)
                    .priceDiff(priceDiff)
                    .referPrice(referPrice)
                    .volume(volumeDec)
                    .build();

            // 将生成的价格和成交量添加到曲线列表中
            priceCurve.add(pricePoint);
            // 累加时间戳
            ts += 1;
        }

        return priceCurve; // 返回生成的价格曲线
    }
}
