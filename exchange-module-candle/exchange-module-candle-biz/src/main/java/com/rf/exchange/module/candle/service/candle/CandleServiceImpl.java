package com.rf.exchange.module.candle.service.candle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.jackson.core.databind.BigDecimalScaleContextHolder;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.candle.context.CandleTableContextHolder;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import com.rf.exchange.module.candle.dal.mysql.candle.CandleMapper;
import com.rf.exchange.module.candle.dal.mysql.candle.CandleTableMapper;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryReqVO;
import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryRespVO;
import com.rf.exchange.module.candle.service.candle.vo.AdminCandleListReqVO;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleListReqVO;
import com.rf.exchange.module.candle.service.controlcandle.ControlCandleService;
import com.rf.exchange.module.candle.service.controlplan.ControlPlanService;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairTenantApi;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.member.api.favoritetradepair.MemberFavoriteTradePairApi;
import java.util.stream.Collectors;
import java.util.Set;
import java.util.Collections;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.CANDLE_BAR_VALUE_INVALID;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
@Slf4j
@Service
@Validated
public class CandleServiceImpl implements CandleService {

    @Resource
    private ControlPlanService controlPlanService;
    @Resource
    private ControlCandleService controlCandleService;
    @Resource
    private CandleTableMapper candleTableMapper;
    @Resource
    private CandleMapper candleMapper;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;
    @Resource
    @Lazy
    private CandleDataApi candleDataApi;
    @Resource
    @Lazy
    private TradePairTenantApi tradePairTenantApi;

    @Resource
    @Lazy
    private MemberFavoriteTradePairApi memberFavoriteTradePairApi;

    @Override
    public List<CandleDO> appGetCandleList(AppCandleListReqVO reqVO) {
        final TradePairRespDTO tradePairDTO = tradePairApi.validateTradePairExists(reqVO.getCode());
        if (tradePairDTO == null) {
            return Collections.emptyList();
        }

        // bar参数转成CandleTimeRangeEnum
        CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.enumOf(reqVO.getBar());
        if (timeRangeEnum == null) {
            throw exception(CANDLE_BAR_VALUE_INVALID);
        }
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(reqVO.getCode());
        // 设置交易对的小数点保留位数
        BigDecimalScaleContextHolder.setPriceScale(tradePairDTO.getScale());

        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(
                CandleDO::getClosePrice,
                CandleDO::getHighPrice,
                CandleDO::getOpenPrice,
                CandleDO::getLowPrice,
                CandleDO::getTimestamp,
                CandleDO::getVolume,
                CandleDO::getTurnover);
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRangeEnum.getRange());
        wrapper.eqIfPresent(CandleDO::getTimeType, timeRangeEnum.getTypeValue());
        wrapper.leIfPresent(CandleDO::getTimestamp, reqVO.getEndTs());
        wrapper.orderByDesc(CandleDO::getTimestamp);

        // 最多一次查询1000条数据
        int limit = (int) Math.min(reqVO.getLimit(), 1000);
        wrapper.last("limit " + limit);

        final List<CandleDO> resultList = candleMapper.selectList(wrapper);

        // 处理复制币的k线,看是否需要替换k线数据为复制币的k线数据
        if (tradePairDTO.getIsCopy()) {
            // 获取控盘计划生成的k线
            final List<ControlCandleDO> controlCandles = controlCandleService.appGetControlCandleList(reqVO, timeRangeEnum);
            if (CollUtil.isNotEmpty(controlCandles)) {
                final Map<Long, ControlCandleDO> controlCandleMap = controlCandles.stream().collect(Collectors.toMap(ControlCandleDO::getTimestamp, candleDO -> candleDO));

                // 替换时间戳相同的原始k线为控盘k线
                for (CandleDO candleDO : resultList) {
                    if (controlCandleMap.containsKey(candleDO.getTimestamp())) {
                        final ControlCandleDO controlCandle = controlCandleMap.get(candleDO.getTimestamp());
                        candleDO.setHighPrice(controlCandle.getHighPrice());
                        candleDO.setLowPrice(controlCandle.getLowPrice());
                        candleDO.setOpenPrice(controlCandle.getOpenPrice());
                        candleDO.setClosePrice(controlCandle.getClosePrice());
                    }
                }
            }
        }

        TenantContextHolder.clear();
        return resultList;
    }

    @Override
    @Slave
    public CandleDO getLastCandle(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        CandleDO result = candleMapper.selectLastOne(timeRangeEnum.getRange(), timeRangeEnum.getTypeValue(), startTime, endTime);
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return result;
    }

    @Override
    @Slave
    public Long getCandleRecordCount(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime) {
        if (StrUtil.isEmpty(code) || timeRangeEnum == null) {
            return null;
        }
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        Long count = candleMapper.selectRecordCount(timeRangeEnum.getRange(), timeRangeEnum.getTypeValue(), startTime, endTime);
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return count;
    }

    @Override
    @Master
    @DSTransactional
    public void createCandleTable(String code) {
        candleTableMapper.createTable(candleTableNameOf(code), "交易对%s".formatted(code));
    }

    @Override
    @Master
    @DSTransactional
    public void createAllCandleTableIfNeed() {
        TenantContextHolder.setIgnore(true);
        List<TradePairRespDTO> tradePairList = tradePairApi.getTradePairListCached();
        Set<String> tableNameSet = tradePairList.stream().map(tradePairRespDTO -> candleTableNameOf(tradePairRespDTO.getCode())).collect(Collectors.toSet());
        Set<String> existsTableNameSet = new HashSet<>(candleTableMapper.selectAllCandleTableNames());
        tableNameSet.removeAll(existsTableNameSet);
        if (CollUtil.isEmpty(tableNameSet)) {
            return;
        }
        for (String table : tableNameSet) {
            candleTableMapper.createTable(table, "交易对%s".formatted(getCodeFromTableName(table)));
        }
        // 创建完成之后再检查一次是否全部创建成功
        existsTableNameSet = new HashSet<>(candleTableMapper.selectAllCandleTableNames());
        if (!existsTableNameSet.containsAll(tableNameSet)) {
            tableNameSet.removeAll(existsTableNameSet);
            log.error("创建历史k线表失败 没有创建表:{}", tableNameSet);
        } else {
            log.info("创建历史k线表成功");
        }
        TenantContextHolder.clear();
    }

    private static String candleTableNameOf(String code) {
        return "data_candle_tradepair_" + code.toLowerCase();
    }

    @Override
    @Master
    @DSTransactional
    public void batchInsertCandles(String code, List<CandleDO> candleDOList) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        String tableName = getTableNameFromCode(code);
        try {
            candleMapper.batchInsertCandle(candleDOList, tableName);
            log.debug("插入数据库 交易对:[{}] 数据条数:[{}]", code, candleDOList.size());
        } catch (Exception e) {
            log.error("记录已经存在 交易对:{} msg:{}", code, e.getCause().getMessage());
        }
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
    }

    @Override
    public void batchInsertCandlesNoUpdate(String code, List<CandleDO> candleDOList, boolean isCopyTrade) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        String tableName = getTableNameFromCode(code);
        try {
            candleMapper.batchInsertCandleNoUpdate(candleDOList, tableName);
            log.debug("插入数据库 是否复制币:[{}] code:[{}] 数据条数:{} ", isCopyTrade, code, candleDOList.size());
        } catch (Exception e) {
            log.info("记录已经存在 是否复制币:[{}] code:{} msg:{}", isCopyTrade, code, e.getCause().getMessage());
        }
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
    }

    @Override
    public Long getLastKlineTimestamp(String code) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange()).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).select(
                CandleDO::getTimestamp);
        wrapper.orderByDesc(CandleDO::getTimestamp);
        wrapper.last("limit 1");
        CandleDO candleDO = candleMapper.selectOne(wrapper);
        if (candleDO == null) return null;
        //Long timestamp = candleMapper.getLastTimestamp(getCodeFromTableName(code));
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return candleDO.getTimestamp();
    }

    /**
     * 从table name中获取系统交易对代码
     *
     * @param tableName 表名
     * @return 交易对代码
     */
    private String getCodeFromTableName(String tableName) {
        int lastIndex = tableName.lastIndexOf('_');
        if (lastIndex != -1) {
            return tableName.substring(lastIndex + 1);
        }
        return null;
    }

    /**
     * 通过系统交易对代码获取表名
     *
     * @param code 交易对代码
     * @return 表名
     */
    private String getTableNameFromCode(String code) {
        return "data_candle_tradepair_" + code.toLowerCase();
    }


    @Override
    public List<CandleDO> getCandleOneMinuteList(String code, Long startTime, int limit) {
        final TradePairRespDTO tradePairDTO = tradePairApi.validateTradePairExists(code);
        if (tradePairDTO == null) {
            return Collections.emptyList();
        }

        // bar参数转成CandleTimeRangeEnum
        CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.MIN_ONE;
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        // 设置交易对的小数点保留位数
        BigDecimalScaleContextHolder.setPriceScale(tradePairDTO.getScale());

        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(
                CandleDO::getClosePrice,
                CandleDO::getHighPrice,
                CandleDO::getOpenPrice,
                CandleDO::getLowPrice,
                CandleDO::getTimestamp,
                CandleDO::getVolume,
                CandleDO::getTurnover);
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRangeEnum.getRange());
        wrapper.eqIfPresent(CandleDO::getTimeType, timeRangeEnum.getTypeValue());
        wrapper.geIfPresent(CandleDO::getTimestamp, startTime);
        wrapper.orderByAsc(CandleDO::getTimestamp);
        wrapper.last("limit " + limit);
        final List<CandleDO> list = candleMapper.selectList(wrapper);
        //List<CandleDO> list=candleMapper.getOneMinuteCandleList(getCodeFromTableName(code),timeRangeEnum.getRange(),timeRangeEnum.getType().getType(),startTime,limit);

        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return list;
    }

    @Override
    public List<CandleDO> getRefCandleMinuteOneList(String code, int limit) {
        final TradePairRespDTO tradePairDTO = tradePairApi.validateTradePairExists(code);
        if (tradePairDTO == null) {
            return Collections.emptyList();
        }

        // bar参数转成CandleTimeRangeEnum
        CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.MIN_ONE;
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        // 设置交易对的小数点保留位数
        BigDecimalScaleContextHolder.setPriceScale(tradePairDTO.getScale());

        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(
                CandleDO::getClosePrice,
                CandleDO::getHighPrice,
                CandleDO::getOpenPrice,
                CandleDO::getLowPrice,
                CandleDO::getTimestamp,
                CandleDO::getVolume,
                CandleDO::getTurnover);
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRangeEnum.getRange());
        wrapper.eqIfPresent(CandleDO::getTimeType, timeRangeEnum.getTypeValue());
        wrapper.orderByDesc(CandleDO::getTimestamp);
        wrapper.last("limit " + limit);
        final List<CandleDO> list = candleMapper.selectList(wrapper);
        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return list;
    }

    //@Override
    //public void copyRefCandleList(String srcCode, String destCode, long startTime) {
    //    TenantContextHolder.setIgnore(true);
    //    PageParam pageParam = new PageParam();
    //    pageParam.setPageNo(1);
    //    pageParam.setPageSize(1000);
    //
    //    CandleTableContextHolder.setTradePairCode(srcCode);
    //    boolean run = true;
    //    while (run) {
    //        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
    //        wrapper.eq(CandleDO::getCode, srcCode);
    //        wrapper.orderByAsc(CandleDO::getId);
    //
    //        final PageResult<CandleDO> pageResult = candleMapper.selectPage(pageParam, wrapper);
    //
    //        pageResult.getList().forEach(candleDO -> {
    //            candleDO.setCode(destCode);
    //        });
    //        candleMapper.batchInsertCandle(pageResult.getList(), getTableNameFromCode(destCode));
    //
    //        // 如果小于1000则表示已经全部拷贝完成
    //        if (pageResult.getList().size() < 1000) {
    //            run = false;
    //        } else {
    //            pageParam.setPageNo(pageParam.getPageNo() + 1);
    //        }
    //    }
    //    TenantContextHolder.clear();
    //}

    @Override
    public CandleDO getFirstCandle(String code, long startTime) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.ge(CandleDO::getTimestamp, startTime).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange());
        wrapper.orderByAsc(CandleDO::getTimestamp);
        wrapper.last("limit 1");
        CandleDO candleDO = candleMapper.selectOne(wrapper);
        if (candleDO == null) return null;
        //Long timestamp = candleMapper.getLastTimestamp(getCodeFromTableName(code));
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return candleDO;
    }

    @Override
    public void deleteExclude1MinuteCandle(String code, long startTime) {
        TenantContextHolder.setIgnore(true);
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.ge(CandleDO::getTimestamp, startTime).and(c -> c.ne(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange())
                .or().ne(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()));
        candleMapper.delete(queryWrapperX);

        //删除今天的天k线
        long todayStartTime = DateUtils.getTodayStartTimeSecondsUtc();
        queryWrapperX.clear();
        queryWrapperX.ge(CandleDO::getTimestamp, todayStartTime).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.DAY_ONE.getRange()).eq(CandleDO::getTimeType, CandleTimeRangeEnum.DAY_ONE.getTypeValue());
        candleMapper.delete(queryWrapperX);

        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
    }

    @Override
    public List<CandleDO> getAdminCandle(AdminCandleListReqVO reqVO) {
        final TradePairRespDTO tradePairDTO = tradePairApi.validateTradePairExists(reqVO.getCode());
        if (tradePairDTO == null) {
            return Collections.emptyList();
        }

        // bar参数转成CandleTimeRangeEnum
        CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.enumOf(reqVO.getBar());
        if (timeRangeEnum == null) {
            throw exception(CANDLE_BAR_VALUE_INVALID);
        }
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(reqVO.getCode());
        // 设置交易对的小数点保留位数
        BigDecimalScaleContextHolder.setPriceScale(tradePairDTO.getScale());

        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(
                CandleDO::getClosePrice,
                CandleDO::getHighPrice,
                CandleDO::getOpenPrice,
                CandleDO::getLowPrice,
                CandleDO::getTimestamp,
                CandleDO::getVolume,
                CandleDO::getTurnover);
        wrapper.eqIfPresent(CandleDO::getTimeRange, timeRangeEnum.getRange());
        wrapper.eqIfPresent(CandleDO::getTimeType, timeRangeEnum.getTypeValue());
        wrapper.geIfPresent(CandleDO::getTimestamp, reqVO.getStartTs());
        wrapper.leIfPresent(CandleDO::getTimestamp, reqVO.getEndTs());
        wrapper.orderByDesc(CandleDO::getTimestamp);

        final List<CandleDO> resultList = candleMapper.selectList(wrapper);

        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return resultList;
    }

    @Override
    public BigDecimal getHighPrice(String code, long startTime, long endTime) {
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.ge(CandleDO::getTimestamp, startTime).le(CandleDO::getTimestamp, endTime).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange());
        wrapper.select(CandleDO::getHighPrice).orderByDesc(CandleDO::getHighPrice).last("limit 1");//找出最大的highPrice
        List<CandleDO> list = candleMapper.selectList(wrapper);
        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        if (list.size() > 0) {
            return list.get(0).getHighPrice();
        }
        return null;
    }

    @Override
    public BigDecimal getLowPrice(String code, long startTime, long endTime) {
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.ge(CandleDO::getTimestamp, startTime).le(CandleDO::getTimestamp, endTime).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange());
        wrapper.select(CandleDO::getLowPrice).orderByAsc(CandleDO::getLowPrice).last("limit 1");//找出最大的highPrice
        List<CandleDO> list = candleMapper.selectList(wrapper);
        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        if (list.size() > 0) {
            return list.get(0).getLowPrice();
        }
        return null;
    }

    @Override
    public BigDecimal getSumVolume(String code, long startTime, long endTime) {
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.ge(CandleDO::getTimestamp, startTime).le(CandleDO::getTimestamp, endTime).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange());
        wrapper.select(CandleDO::getVolume);//找出最大的highPrice
        List<CandleDO> list = candleMapper.selectList(wrapper);
        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return list.stream().map(CandleDO::getVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getSumTurnover(String code, long startTime, long endTime) {
        // 忽略租户
        TenantContextHolder.setIgnore(true);
        // 设置动态表名的code
        CandleTableContextHolder.setTradePairCode(code);
        LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.ge(CandleDO::getTimestamp, startTime).le(CandleDO::getTimestamp, endTime).eq(CandleDO::getTimeType, CandleTimeRangeEnum.MIN_ONE.getType().getType()).eq(CandleDO::getTimeRange, CandleTimeRangeEnum.MIN_ONE.getRange());
        wrapper.select(CandleDO::getTurnover);//找出最大的highPrice
        List<CandleDO> list = candleMapper.selectList(wrapper);
        // 清除ThreadLocal
        CandleTableContextHolder.clear();
        TenantContextHolder.clear();
        return list.stream().map(CandleDO::getTurnover)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    @Slave
    public List<AppKlineSummaryRespVO> appGetKlineSummaryByAssetType(AppKlineSummaryReqVO reqVO, Long tenantId, Long userId) {
        try {
            // 1. 获取租户的交易对列表
            List<TradePairRespDTO> filteredTradePairList;

            // 特殊处理自选类型（assetType=4）
            if (TradeAssetTypeEnum.FAVORITES.getType().equals(reqVO.getAssetType())) {
                // 自选类型：获取用户收藏的交易对
                Set<String> favoriteCodes = memberFavoriteTradePairApi.getFavoriteTradePairCodes(userId, tenantId);
                if (favoriteCodes.isEmpty()) {
                    // 如果用户没有收藏任何交易对，返回空列表
                    return Collections.emptyList();
                }
                // 根据用户收藏的交易对代码获取交易对信息
                filteredTradePairList = tradePairTenantApi.getTradePairListByCodes(tenantId, favoriteCodes);
            } else {
                // 其他类型：按资产类型过滤
                filteredTradePairList = tradePairTenantApi.getListCachedByType(tenantId, TradeTypeEnum.PERIOD.getType(), reqVO.getAssetType());
            }

            if (CollectionUtil.isEmpty(filteredTradePairList)) {
                return Collections.emptyList();
            }

            // 2. 计算时间范围（最近24小时）
            long endTime = System.currentTimeMillis() / 1000; // 当前时间戳（秒）
            long startTime = endTime - 24 * 60 * 60; // 24小时前

            List<AppKlineSummaryRespVO> resultList = new ArrayList<>();

            // 3. 为每个交易对查询K线数据并计算统计信息
            for (TradePairRespDTO tradePair : filteredTradePairList) {
                try {
                    AppKlineSummaryRespVO summaryData = buildKlineSummaryData(tradePair, startTime, endTime, reqVO.getDataPoints());
                    if (summaryData != null) {
                        resultList.add(summaryData);
                    }
                } catch (Exception e) {
                    log.warn("获取交易对 {} 的K线简化数据失败: {}", tradePair.getCode(), e.getMessage());
                    // 继续处理其他交易对，不因为单个交易对失败而影响整体结果
                }
            }

            return resultList;
        } catch (Exception e) {
            log.error("获取K线简化数据失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建K线简化数据
     *
     * @param tradePair  交易对信息
     * @param startTime  开始时间戳（秒）
     * @param endTime    结束时间戳（秒）
     * @param dataPoints 数据点数量
     * @return K线简化数据
     */
    private AppKlineSummaryRespVO buildKlineSummaryData(TradePairRespDTO tradePair, long startTime, long endTime, Integer dataPoints) {
        try {
            // 设置动态表名和租户忽略
            TenantContextHolder.setIgnore(true);
            CandleTableContextHolder.setTradePairCode(tradePair.getCode());
            BigDecimalScaleContextHolder.setPriceScale(tradePair.getScale());

            // 查询1小时K线数据（最近24小时）
            CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.HOUR_ONE;
            LambdaQueryWrapperX<CandleDO> wrapper = new LambdaQueryWrapperX<>();
            wrapper.select(
                    CandleDO::getClosePrice,
                    CandleDO::getHighPrice,
                    CandleDO::getLowPrice,
                    CandleDO::getOpenPrice,
                    CandleDO::getTimestamp,
                    CandleDO::getVolume);
            wrapper.eq(CandleDO::getTimeRange, timeRangeEnum.getRange());
            wrapper.eq(CandleDO::getTimeType, timeRangeEnum.getTypeValue());
            wrapper.ge(CandleDO::getTimestamp, startTime);
            wrapper.le(CandleDO::getTimestamp, endTime);
            wrapper.orderByAsc(CandleDO::getTimestamp);

            List<CandleDO> candleList = candleMapper.selectList(wrapper);

            // 清除ThreadLocal
            CandleTableContextHolder.clear();
            TenantContextHolder.clear();
            // 注意：不清除 BigDecimalScaleContextHolder，让拦截器统一处理，避免序列化时精度丢失

            if (CollectionUtil.isEmpty(candleList)) {
                return null;
            }

            // 构建响应数据
            AppKlineSummaryRespVO respVO = new AppKlineSummaryRespVO();
            respVO.setCode(tradePair.getCode());
            respVO.setName(tradePair.getName());

            // 计算统计数据
            calculateSummaryStatistics(respVO, candleList);

            // 构建简化K线数据点
            buildKlinePoints(respVO, candleList, dataPoints);

            return respVO;
        } catch (Exception e) {
            // 确保清除ThreadLocal
            CandleTableContextHolder.clear();
            TenantContextHolder.clear();
            // 注意：不清除 BigDecimalScaleContextHolder，让拦截器统一处理，避免序列化时精度丢失
            throw e;
        }
    }

    /**
     * 计算统计数据
     *
     * @param respVO     响应对象
     * @param candleList K线数据列表
     */
    private void calculateSummaryStatistics(AppKlineSummaryRespVO respVO, List<CandleDO> candleList) {
        if (CollectionUtil.isEmpty(candleList)) {
            return;
        }

        // 获取最新价格（当前价格）
        CandleDO latestCandle = candleList.get(candleList.size() - 1);
        respVO.setCurrentPrice(latestCandle.getClosePrice());

        // 获取24小时前的价格（第一个数据点的开盘价）
        CandleDO firstCandle = candleList.get(0);
        BigDecimal price24hAgo = firstCandle.getOpenPrice();

        // 计算24小时涨跌幅
        BigDecimal change24h = latestCandle.getClosePrice().subtract(price24hAgo);
        respVO.setChange24h(change24h);

        // 计算24小时涨跌幅百分比
        if (price24hAgo.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal changePercent24h = change24h.divide(price24hAgo, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            respVO.setChangePercent24h(changePercent24h);
        } else {
            respVO.setChangePercent24h(BigDecimal.ZERO);
        }

        // 计算24小时最高价和最低价
        BigDecimal high24h = candleList.stream()
                .map(CandleDO::getHighPrice)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
        respVO.setHigh24h(high24h);

        BigDecimal low24h = candleList.stream()
                .map(CandleDO::getLowPrice)
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
        respVO.setLow24h(low24h);

        // 计算24小时成交量
        BigDecimal volume24h = candleList.stream()
                .map(CandleDO::getVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        respVO.setVolume24h(volume24h);
    }

    /**
     * 构建K线数据点
     *
     * @param respVO     响应对象
     * @param candleList K线数据列表
     * @param dataPoints 数据点数量
     */
    private void buildKlinePoints(AppKlineSummaryRespVO respVO, List<CandleDO> candleList, Integer dataPoints) {
        if (CollectionUtil.isEmpty(candleList)) {
            respVO.setKlinePoints(Collections.emptyList());
            return;
        }

        List<AppKlineSummaryRespVO.KlinePoint> klinePoints = new ArrayList<>();

        // 如果数据点数量大于等于K线数据数量，直接使用所有数据
        if (dataPoints >= candleList.size()) {
            for (CandleDO candle : candleList) {
                AppKlineSummaryRespVO.KlinePoint point = new AppKlineSummaryRespVO.KlinePoint();
                point.setTimestamp(candle.getTimestamp());
                point.setClosePrice(candle.getClosePrice());
                point.setTrend(calculateTrend(candle));
                klinePoints.add(point);
            }
        } else {
            // 按比例采样数据点
            int step = candleList.size() / dataPoints;
            for (int i = 0; i < dataPoints; i++) {
                int index = Math.min(i * step, candleList.size() - 1);
                CandleDO candle = candleList.get(index);

                AppKlineSummaryRespVO.KlinePoint point = new AppKlineSummaryRespVO.KlinePoint();
                point.setTimestamp(candle.getTimestamp());
                point.setClosePrice(candle.getClosePrice());
                point.setTrend(calculateTrend(candle));
                klinePoints.add(point);
            }
        }

        respVO.setKlinePoints(klinePoints);
    }

    /**
     * 计算涨跌趋势
     *
     * @param candle K线数据
     * @return 1:上涨 0:下跌
     */
    private Integer calculateTrend(CandleDO candle) {
        if (candle.getClosePrice().compareTo(candle.getOpenPrice()) >= 0) {
            return 1; // 上涨
        } else {
            return 0; // 下跌
        }
    }

}
