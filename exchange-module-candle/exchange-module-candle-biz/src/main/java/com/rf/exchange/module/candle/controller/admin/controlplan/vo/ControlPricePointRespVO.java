package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 控盘计划的时间节点 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ControlPricePointRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21860")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "控盘计划参数id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18998")
    @ExcelProperty("控盘计划参数id")
    private Long planId;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String tradPairCode;

    @Schema(description = "基础价格", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("基础价格")
    private BigDecimal priceBase;

    @Schema(description = "成交量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("成交量")
    private Long volume;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时间戳")
    private Long timestamp;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}