package com.rf.exchange.module.candle.dal.redis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.monitor.TracerUtils;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.TradePairControlApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairControlRespDTO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Slf4j
@Repository
public class CandleTodayKlinePriceRedisDAO {

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TradePairControlApi tradePairControlApi;

    public void update(String tradePairCode, TodayKlinePriceDTO priceDto) {
        final TodayKlinePriceDTO existsKline = get(tradePairCode);
        if (existsKline != null) {
            BeanUtil.copyProperties(priceDto, existsKline, CopyOptions.create().ignoreNullValue());
            set(tradePairCode, existsKline);
        } else {
            set(tradePairCode, priceDto);
        }
        // 这里设置复制币
        List<TradePairRespDTO> customTradePair = tradePairApi.getCustomTradePair();
        customTradePair = customTradePair.stream().filter(c -> c.getIsCopy() && c.getReferenceCode().equalsIgnoreCase(tradePairCode)).toList();
        for (TradePairRespDTO copyTradePair : customTradePair) {
            String copyCode = copyTradePair.getCode();
            //查看是否这一分钟有控盘
            TradePairControlRespDTO tradePairControlDO = tradePairControlApi.getCacheByCode(copyCode);
            if (tradePairControlDO != null) {
                //控的那分钟
                long controlMinuteTime = tradePairControlDO.getStartTime() / 1000 / 60 * 60;
                long nowMinuteTime = DateUtils.getUnixTimestampNow() / 1000 / 60 * 60;
                if (controlMinuteTime == nowMinuteTime) {
                    priceDto.setHighPrice(tradePairControlDO.getHighPrice() + "");
                    priceDto.setLowPrice(tradePairControlDO.getLowPrice() + "");
                    priceDto.setClosePrice(tradePairControlDO.getEndPrice() + "");
                }
            }
            priceDto.setTradePairCode(copyCode);
            // 今日收盘k线
            TodayKlinePriceDTO existsKlineCustom = get(tradePairCode);
            if (existsKlineCustom != null) {
                BeanUtil.copyProperties(priceDto, existsKlineCustom, CopyOptions.create().ignoreNullValue());
                set(copyCode, existsKlineCustom);
            } else {
                set(copyCode, priceDto);
            }
        }
    }

    public TodayKlinePriceDTO get(String tradePairCode) {
        String redisKey = formatKey();
        Object val = redisTemplate.opsForHash().get(redisKey, tradePairCode);
        return JsonUtils.parseObject((String) val, TodayKlinePriceDTO.class);
    }

    /**
     * 获取所有交易对的当前价格
     *
     * @return 交易对价格的字典 格式：<交易对code : 价格信息>
     */
    public Map<String, TodayKlinePriceDTO> getAll() {
        String redisKey = formatKey();
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(redisKey);
        Map<String, TodayKlinePriceDTO> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            TodayKlinePriceDTO dto = parseJson((String) entry.getValue());
            if (dto != null) {
                result.put((String) entry.getKey(), dto);
            }
        }
        return result;
    }

    /**
     * 获取所有交易对的当前价格
     *
     * @return 交易对价格的字典 格式：<交易对code : 价格信息>
     */
    public Map<String, TodayKlinePriceDTO> getMultiKeys(Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyMap();
        }
        Collection<Object> lKeys = new ArrayList<>(keys);
        List<Object> objects = redisTemplate.opsForHash().multiGet(formatKey(), lKeys);
        Map<String, TodayKlinePriceDTO> result = new HashMap<>();
        for (Object object : objects) {
            if (object == null) {
                continue;
            }
            TodayKlinePriceDTO dto = parseJson((String) object);
            if (dto != null) {
                result.put(dto.getTradePairCode(), dto);
            }
        }
        return result;
    }

    private void set(String tradePairCode, TodayKlinePriceDTO priceDto) {
        String redisKey = formatKey();
        String jsonStr = JsonUtils.toJsonString(priceDto);
        redisTemplate.opsForHash().put(redisKey, tradePairCode, jsonStr);
    }

    /**
     * 控盘后当天的数据要删除
     *
     * @param tradePairCode
     */
    public void delete(String tradePairCode) {
        String redisKey = formatKey();
        redisTemplate.opsForHash().delete(redisKey, tradePairCode);
    }

    private TodayKlinePriceDTO parseJson(String jsonStr) {
        try {
            return JsonUtils.parseObject(jsonStr, TodayKlinePriceDTO.class);
        } catch (RuntimeException e) {
            log.error("解析redis中的交易对昨日收盘信息异常 {} traceId:{}", e.getMessage(), TracerUtils.getTraceId());
        }
        return null;
    }

    private String formatKey() {
        return RedisKeyConstants.CANDLE_TODAY_KLINE;
    }
}
