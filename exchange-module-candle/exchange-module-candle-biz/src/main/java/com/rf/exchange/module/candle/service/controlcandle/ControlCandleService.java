package com.rf.exchange.module.candle.service.controlcandle;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandlePageReqVO;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandleSaveReqVO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleListReqVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 控盘k线(用于接口数据替换) Service 接口
 *
 * <AUTHOR>
 */
public interface ControlCandleService {

    /**
     * 创建控盘k线(用于接口数据替换)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createControlCandle(@Valid ControlCandleSaveReqVO createReqVO);

    /**
     * 更新控盘k线(用于接口数据替换)
     *
     * @param updateReqVO 更新信息
     */
    void updateControlCandle(@Valid ControlCandleSaveReqVO updateReqVO);

    /**
     * 删除控盘k线(用于接口数据替换)
     *
     * @param id 编号
     */
    void deleteControlCandle(Long id);

    /**
     * 获得控盘k线(用于接口数据替换)
     *
     * @param id 编号
     * @return 控盘k线(用于接口数据替换)
     */
    ControlCandleDO getControlCandle(Integer id);

    /**
     * 获得控盘k线(用于接口数据替换)分页
     *
     * @param pageReqVO 分页查询
     * @return 控盘k线(用于接口数据替换)分页
     */
    PageResult<ControlCandleDO> getControlCandlePage(ControlCandlePageReqVO pageReqVO);

    /**
     * 保存控盘的k线列表
     * @param candleDOList 控盘k线
     */
    void saveControlCandles(List<ControlCandleDO> candleDOList);

    /**
     * 获取控盘计划的展示k线数据列表
     *
     * @param reqVO         k线请求
     * @param timeRangeEnum
     * @return 用于前端展示的控盘k线
     */
    List<ControlCandleDO> appGetControlCandleList(AppCandleListReqVO reqVO, CandleTimeRangeEnum timeRangeEnum);
}