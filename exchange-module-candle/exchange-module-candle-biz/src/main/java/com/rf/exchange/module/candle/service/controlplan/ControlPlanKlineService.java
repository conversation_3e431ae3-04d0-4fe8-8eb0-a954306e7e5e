package com.rf.exchange.module.candle.service.controlplan;

import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;

import java.util.List;

/**
 * 控盘计划的k线 Service 接口
 *
 * <AUTHOR>
 */
public interface ControlPlanKlineService {

    /**
     * 保存计划控盘k线列表
     *
     * @param planDO          控盘计划信息
     * @param planKlineDOList k线列表
     */
    void savePlanKlineList(ControlPlanDO planDO, List<ControlPlanKlineDO> planKlineDOList);


    /**
     * 获取控盘计划的k线列表
     *
     * @param planId 计划id
     * @return k线列表
     */
    List<ControlPlanKlineDO> getControlPlanKlineListByPlanId(long planId);
}