package com.rf.exchange.module.candle.api;

import com.rf.exchange.module.candle.CandleConvert;
import com.rf.exchange.module.candle.api.dto.CandleDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.CandleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
@Service
@Validated
public class CandleDataApiImpl implements CandleDataApi {

    @Resource
    private CandleCurrentPriceRedisDAO currentPriceRedisDAO;
    @Resource
    private CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;
    @Resource
    private CandleService candleService;

    @Override
    public CandleDTO getLastCandle(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime) {
        CandleDO lastCandle = candleService.getLastCandle(code, timeRangeEnum, startTime, endTime);
        return CandleConvert.INSTANCE.convert(lastCandle);
    }

    @Override
    public long getCandleRecordCount(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime) {
        return candleService.getCandleRecordCount(code, timeRangeEnum, startTime, endTime);
    }

    @Override
    public Map<String, CurrentPriceRespDTO> getAllCurrentPriceList() {
        return currentPriceRedisDAO.getAll();
    }

    @Override
    public Map<String, CurrentPriceRespDTO> getCurrentPriceListByIdSet(Set<String> codeSet) {
        List<String> list = new ArrayList<>(codeSet);
        return currentPriceRedisDAO.getMultiKeys(list);
    }

    @Override
    public Map<String, TodayKlinePriceDTO> getTodayKlinePriceListByIdSet(Set<String> codeSet) {
        List<String> list = new ArrayList<>(codeSet);
        return todayKlinePriceRedisDAO.getMultiKeys(list);
    }

    @Override
    public CurrentPriceRespDTO getCurrentPrice(String code) {
        return currentPriceRedisDAO.get(code);
    }

    @Override
    public void createCandleTable(String code) {
        candleService.createCandleTable(code);
    }

    @Override
    public void createAllCandleTableIfNeed() {
        candleService.createAllCandleTableIfNeed();
    }

    @Override
    public void batchInsertCandles(String code, List<CandleDTO> candles) {
        final List<CandleDO> candleDOList = CandleConvert.INSTANCE.convertList2(candles);
        candleService.batchInsertCandles(code, candleDOList);
    }

    @Override
    public void batchInsertCandlesNoUpdate(String code, List<CandleDTO> candles, boolean isCopyTrade) {
        final List<CandleDO> candleDOList = CandleConvert.INSTANCE.convertList2(candles);
        candleService.batchInsertCandlesNoUpdate(code, candleDOList, isCopyTrade);
    }
}
