package com.rf.exchange.module.candle.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * APP K线简化数据响应VO
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Schema(description = "APP K线简化数据响应VO")
@Data
public class AppKlineSummaryRespVO {

    @Schema(description = "交易对代码", example = "XAUUSD")
    private String code;

    @Schema(description = "交易对名称", example = "黄金/美元")
    private String name;

    @Schema(description = "当前价格", example = "2350.55")
    private BigDecimal currentPrice;

    @Schema(description = "24小时涨跌幅", example = "0.0123")
    private BigDecimal change24h;

    @Schema(description = "24小时涨跌幅百分比", example = "1.23")
    private BigDecimal changePercent24h;

    @Schema(description = "24小时最高价", example = "2360.00")
    private BigDecimal high24h;

    @Schema(description = "24小时最低价", example = "2340.00")
    private BigDecimal low24h;

    @Schema(description = "24小时成交量", example = "1234567.89")
    private BigDecimal volume24h;

    @Schema(description = "简化K线数据点(最近24小时，每小时一个点)")
    private List<KlinePoint> klinePoints;

    @Schema(description = "K线数据点")
    @Data
    public static class KlinePoint {
        @Schema(description = "时间戳", example = "1705478400")
        private Long timestamp;

        @Schema(description = "收盘价", example = "2350.55")
        private BigDecimal closePrice;

        @Schema(description = "涨跌状态 1:上涨 0:下跌", example = "1")
        private Integer trend;
    }
}
