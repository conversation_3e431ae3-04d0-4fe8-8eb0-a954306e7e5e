package com.rf.exchange.module.candle.service.candle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppCandleRespVO {
    /**
     * 时间戳
     */
    @Schema(description = "k线时间戳(单位秒)")
    private Long timestamp;
    /**
     * 最高价
     */
    @Schema(description = "最高价")
    private BigDecimal highPrice;
    /**
     * 最低价
     */
    @Schema(description = "最低价")
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    @Schema(description = "开盘价")
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    @Schema(description = "收盘价")
    private BigDecimal closePrice;
    /**
     * 交易量
     */
    @Schema(description = "交易量")
    private BigDecimal volume;
    /**
     * 成交金额
     */
    @Schema(description = "成交额")
    private BigDecimal turnover;
}
