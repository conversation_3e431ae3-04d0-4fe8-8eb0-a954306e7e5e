package com.rf.exchange.module.candle;

import com.rf.exchange.module.candle.api.dto.CandleDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-06
 */
@Mapper
public interface CandleConvert {

    CandleConvert INSTANCE = Mappers.getMapper(CandleConvert.class);

    CandleDTO convert(CandleDO candleDO);

    CandleDO convert2(CandleDTO candleDTO);

    AppCandleRespVO convert3(CandleDO candleDO);

    List<CandleDO> convertList2(List<CandleDTO> candleDTOList);

    List<AppCandleRespVO> convertList3(List<CandleDO> candleDOList);

    List<CandleDTO> convertList4(List<CandleDO> candleList);
}
