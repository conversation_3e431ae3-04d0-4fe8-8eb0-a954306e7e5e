package com.rf.exchange.module.candle.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdminCandlePreviewReqVo {

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28")
    @NotNull(message = "交易对不能为空")
    private Long tradePairId;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    @Schema(description = "结束时间收盘价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束金额不能为空")
    private BigDecimal endPrice;

    @Schema(description = "每分钟成交额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "每分钟成交额不能为空")
    private BigDecimal oneMinuteTurnover;

}
