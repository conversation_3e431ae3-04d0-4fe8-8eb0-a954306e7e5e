package com.rf.exchange.module.candle.service.candle;

import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryReqVO;
import com.rf.exchange.module.candle.controller.app.vo.AppKlineSummaryRespVO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.vo.AdminCandleListReqVO;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleListReqVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface CandleService {

    /**
     * App获取k线列表的接口
     *
     * @param reqVO 请求信息
     * @return k线列表
     */
    List<CandleDO> appGetCandleList(AppCandleListReqVO reqVO);

    /**
     * 获取最后一条k线数据
     *
     * @param code          交易对代码
     * @param timeRangeEnum 时间范围
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return k线数据
     */
    CandleDO getLastCandle(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime);


    /**
     * 获取交易对指的
     *
     * @param code          交易对代码
     * @param timeRangeEnum 时间范围
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 记录条数
     */
    Long getCandleRecordCount(String code, CandleTimeRangeEnum timeRangeEnum, Long startTime, Long endTime);

    /**
     * 创建交易对的k线数据表
     *
     * @param code 交易对代码
     */
    void createCandleTable(String code);

    /**
     * 批量创建交易对的k线表
     * 如果表已经存在则不会创建表
     */
    void createAllCandleTableIfNeed();

    /**
     * 批量插入k线数据
     *
     * @param code         交易对代码
     * @param candleDOList k线了列表
     */
    void batchInsertCandles(String code, List<CandleDO> candleDOList);

    /**
     * 批量插入k线存在就不更新
     *
     * @param code 交易对代码
     * @param candleDOList k线信息
     * @param isCopyTrade 是否是复制币 true:是 false:不是
     */
    void batchInsertCandlesNoUpdate(String code, List<CandleDO> candleDOList, boolean isCopyTrade);

    /**
     * 获取币种的1分钟k线数据
     * @param code
     * @param startTime
     * @return
     */
    List<CandleDO> getCandleOneMinuteList(String code, Long startTime, int limit);

    /**
     * 获取时间内的第一根k线
     * @param code
     * @param startTime
     * @return
     */
    CandleDO getFirstCandle(String code, long startTime);

    /**
     * 获取参考交易对的最后几条1分钟k线图
     * @param code
     * @param limit
     * @return
     */
    List<CandleDO> getRefCandleMinuteOneList(String code,int limit);

    /**
     * 获取最后一根k线时间
     * @param code
     * @return
     */
    Long getLastKlineTimestamp(String code);

    /**
     * 删除1分钟以外的k线，因为控盘的一分钟线重新 生成后，需要重新生成其它时间范围级别的k线
     * @param code
     * @param startTime
     */
    void deleteExclude1MinuteCandle(String code, long startTime);

    /**
     * 管理后台查看k线
     * @param reqVO
     * @return
     */
    @Deprecated
    List<CandleDO> getAdminCandle(AdminCandleListReqVO reqVO);

    BigDecimal getHighPrice(String code, long startTime, long endTime);

    BigDecimal getLowPrice(String code, long startTime, long endTime);

    BigDecimal getSumVolume(String code,long startTime,long endTime);

    BigDecimal getSumTurnover(String code,long startTime,long endTime);

    /**
     * 获取K线简化数据（按资产类型）
     *
     * @param reqVO    查询请求
     * @param tenantId 租户ID
     * @param userId   用户ID（用于自选类型）
     * @return K线简化数据列表
     */
    List<AppKlineSummaryRespVO> appGetKlineSummaryByAssetType(AppKlineSummaryReqVO reqVO, Long tenantId, Long userId);
}
