package com.rf.exchange.module.candle.dal.mysql.controlplan;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 控盘计划的时间节点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ControlPricePointMapper extends BaseMapperX<ControlPricePointDO> {

    //default PageResult<ControlPricePointDO> selectPage(ControlPricePointPageReqVO reqVO) {
    //    return selectPage(reqVO, new LambdaQueryWrapperX<ControlPricePointDO>()
    //            .eqIfPresent(ControlPricePointDO::getPlanId, reqVO.getPlanId())
    //            .eqIfPresent(ControlPricePointDO::getTradPairCode, reqVO.getTradPairCode())
    //            .eqIfPresent(ControlPricePointDO::getPriceBase, reqVO.getPriceBase())
    //            .eqIfPresent(ControlPricePointDO::getVolume, reqVO.getVolume())
    //            .eqIfPresent(ControlPricePointDO::getTimestamp, reqVO.getTimestamp())
    //            .betweenIfPresent(ControlPricePointDO::getCreateTime, reqVO.getCreateTime())
    //            .orderByDesc(ControlPricePointDO::getId));
    //}

    void batchInsertPricePoints(@Param("priceList") List<ControlPricePointDO> controlPricePoints);
}