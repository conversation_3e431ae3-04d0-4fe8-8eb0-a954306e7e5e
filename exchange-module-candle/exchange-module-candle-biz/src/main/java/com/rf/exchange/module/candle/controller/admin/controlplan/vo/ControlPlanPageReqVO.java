package com.rf.exchange.module.candle.controller.admin.controlplan.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 复制币的控盘计划参数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ControlPlanPageReqVO extends PageParam {

    @Schema(description = "交易对id", example = "30793")
    private Long tradePairId;

    @Schema(description = "交易对代码")
    private String tradePairCode;

    @Schema(description = "控盘开始时间")
    private Long[] startTime;

    @Schema(description = "结束价格")
    private BigDecimal endPrice;

    @Schema(description = "最低价")
    private BigDecimal minPrice;

    @Schema(description = "最低价")
    private BigDecimal maxPrice;

    @Schema(description = "波动率")
    private BigDecimal fluctuation;

    @Schema(description = "波动时长(秒)")
    private Long durationSecs;

    @Schema(description = "算法")
    private String algorithm;

    @Schema(description = "控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    private Long[] createTime;

}