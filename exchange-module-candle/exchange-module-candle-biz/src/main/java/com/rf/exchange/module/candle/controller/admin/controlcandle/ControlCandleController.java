package com.rf.exchange.module.candle.controller.admin.controlcandle;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandlePageReqVO;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandleRespVO;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandleSaveReqVO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import com.rf.exchange.module.candle.service.controlcandle.ControlCandleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 控盘k线(用于接口数据替换)")
@RestController
@RequestMapping("/candle/control-candle")
@Validated
public class ControlCandleController {

    @Resource
    private ControlCandleService controlCandleService;

    @PostMapping("/create")
    @Operation(summary = "创建控盘k线(用于接口数据替换)")
    @PreAuthorize("@ss.hasPermission('candle:control-candle:create')")
    public CommonResult<Long> createControlCandle(@Valid @RequestBody ControlCandleSaveReqVO createReqVO) {
        return success(controlCandleService.createControlCandle(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新控盘k线(用于接口数据替换)")
    @PreAuthorize("@ss.hasPermission('candle:control-candle:update')")
    public CommonResult<Boolean> updateControlCandle(@Valid @RequestBody ControlCandleSaveReqVO updateReqVO) {
        controlCandleService.updateControlCandle(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除控盘k线(用于接口数据替换)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('candle:control-candle:delete')")
    public CommonResult<Boolean> deleteControlCandle(@RequestParam("id") Long id) {
        controlCandleService.deleteControlCandle(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得控盘k线(用于接口数据替换)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('candle:control-candle:query')")
    public CommonResult<ControlCandleRespVO> getControlCandle(@RequestParam("id") Integer id) {
        ControlCandleDO controlCandle = controlCandleService.getControlCandle(id);
        return success(BeanUtils.toBean(controlCandle, ControlCandleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得控盘k线(用于接口数据替换)分页")
    @PreAuthorize("@ss.hasPermission('candle:control-candle:query')")
    public CommonResult<PageResult<ControlCandleRespVO>> getControlCandlePage(@Valid ControlCandlePageReqVO pageReqVO) {
        PageResult<ControlCandleDO> pageResult = controlCandleService.getControlCandlePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ControlCandleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出控盘k线(用于接口数据替换) Excel")
    @PreAuthorize("@ss.hasPermission('candle:control-candle:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportControlCandleExcel(@Valid ControlCandlePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ControlCandleDO> list = controlCandleService.getControlCandlePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "控盘k线(用于接口数据替换).xls", "数据", ControlCandleRespVO.class,
                        BeanUtils.toBean(list, ControlCandleRespVO.class));
    }

}