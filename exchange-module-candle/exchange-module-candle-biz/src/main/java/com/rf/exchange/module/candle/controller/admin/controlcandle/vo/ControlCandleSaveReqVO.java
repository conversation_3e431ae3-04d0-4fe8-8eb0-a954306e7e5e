package com.rf.exchange.module.candle.controller.admin.controlcandle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 控盘k线(用于接口数据替换)新增/修改 Request VO")
@Data
public class ControlCandleSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28716")
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "时间范围", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer timeRange;

    @Schema(description = "时间范围类型 1分钟 2小时 3日 4月", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer timeType;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long timestamp;

    @Schema(description = "交易对编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易对编码不能为空")
    private String code;

    @Schema(description = "最高价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "4326")
    @NotNull(message = "最高价格不能为空")
    private BigDecimal highPrice;

    @Schema(description = "最低价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "10838")
    @NotNull(message = "最低价格不能为空")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "24311")
    @NotNull(message = "开盘价格不能为空")
    private BigDecimal openPrice;

    @Schema(description = "收盘价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "19377")
    @NotNull(message = "收盘价格不能为空")
    private BigDecimal closePrice;

    @Schema(description = "成交量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "成交量不能为空")
    private BigDecimal volume;

    @Schema(description = "成交额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "成交额不能为空")
    private BigDecimal turnover;

    @Schema(description = "是否是休市数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否是休市数据不能为空")
    private Boolean isMarketClose;

}