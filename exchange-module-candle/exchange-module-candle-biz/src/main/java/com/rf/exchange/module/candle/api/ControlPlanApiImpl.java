package com.rf.exchange.module.candle.api;

import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.candle.api.dto.ControlCandleDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanKLineDTO;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;
import com.rf.exchange.module.candle.dal.redis.CandleControlPricePointRedisDAO;
import com.rf.exchange.module.candle.service.controlcandle.ControlCandleService;
import com.rf.exchange.module.candle.service.controlplan.ControlPlanKlineService;
import com.rf.exchange.module.candle.service.controlplan.ControlPlanService;
import com.rf.exchange.module.candle.service.controlplan.ControlPricePointService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-03
 */
@Service
@Validated
public class ControlPlanApiImpl implements ControlPlanApi {

    @Resource
    private ControlPlanService controlPlanService;
    @Resource
    private ControlPricePointService planPricePointService;
    @Resource
    private ControlPlanKlineService planKlineService;
    @Resource
    private CandleControlPricePointRedisDAO pricePointRedisDAO;
    @Resource
    private ControlCandleService controlCandleService;

    @Override
    public List<ControlPlanDTO> getRunningPlanList() {
        final List<ControlPlanDO> runningPlans = controlPlanService.getRunningPlans();
        return BeanUtils.toBean(runningPlans, ControlPlanDTO.class);
    }

    @Override
    public List<ControlPlanDTO> getWaitPlanList() {
        final List<ControlPlanDO> waitingPlans = controlPlanService.getWaitingPlans();
        return BeanUtils.toBean(waitingPlans, ControlPlanDTO.class);
    }

    @Override
    public Map<Long, ControlPricePointDTO> getPricePointMap(long planId) {
        final Map<Long, ControlPricePointDO> pricePointList = planPricePointService.getControlPricePointListOfPlan(planId);
        Map<Long, ControlPricePointDTO> resultMap = new HashMap<>();
        for (Map.Entry<Long, ControlPricePointDO> entry : pricePointList.entrySet()) {
            resultMap.put(entry.getKey(), BeanUtils.toBean(entry.getValue(), ControlPricePointDTO.class));
        }
        return resultMap;
    }

    @Override
    public ControlPricePointDTO getPricePoint(long planId, long timestamp) {
        final ControlPricePointDO pricePoint = pricePointRedisDAO.getPricePoint(planId, timestamp);
        return BeanUtils.toBean(pricePoint, ControlPricePointDTO.class);
    }

    @Override
    public List<ControlPlanKLineDTO> getPlanKlineList(long planId) {
        final List<ControlPlanKlineDO> kLines = planKlineService.getControlPlanKlineListByPlanId(planId);
        return BeanUtils.toBean(kLines, ControlPlanKLineDTO.class);
    }

    @Override
    public void startPlan(long planId, BigDecimal execPrice) {
        controlPlanService.startPlan(planId, execPrice);
    }

    @Override
    public void finishPlan(long planId) {
        controlPlanService.finishPlan(planId);
    }

    @Override
    public void saveControlCandles(List<ControlCandleDTO> controlCandleDOS) {
        final List<ControlCandleDO> candles = BeanUtils.toBean(controlCandleDOS, ControlCandleDO.class);
        controlCandleService.saveControlCandles(candles);
    }
}
