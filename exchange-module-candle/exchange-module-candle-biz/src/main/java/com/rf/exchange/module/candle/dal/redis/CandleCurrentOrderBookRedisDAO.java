package com.rf.exchange.module.candle.dal.redis;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookListDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import static com.rf.exchange.module.candle.dal.redis.RedisKeyConstants.CANDLE_CURRENT_ORDER_BOOK;

/**
 * 交易对的订单薄的redis DAO
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Slf4j
@Repository
public class CandleCurrentOrderBookRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 缓存交易对的订单薄
     *
     * @param code          交易对代码
     * @param orderBookList 订单薄列表
     */
    public void set(String code, CandleOrderBookListDTO orderBookList) {
        if (StrUtil.isEmpty(code)) {
            return;
        }
        String json = JsonUtils.toJsonString(orderBookList);
        stringRedisTemplate.opsForValue().set(formatKey(code), json);
    }

    /**
     * 获取指定交易对的订单薄
     *
     * @param code 交易对代码
     * @return 订单薄
     */
    public CandleOrderBookListDTO get(String code) {
        if (StrUtil.isEmpty(code)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(formatKey(code));
        try {
            return JsonUtils.parseObject(json, CandleOrderBookListDTO.class);
        } catch (Exception e) {
            log.error("");
        }
        return new CandleOrderBookListDTO();
    }

    private String formatKey(String code) {
        return CANDLE_CURRENT_ORDER_BOOK + ":" + code;
    }
}
