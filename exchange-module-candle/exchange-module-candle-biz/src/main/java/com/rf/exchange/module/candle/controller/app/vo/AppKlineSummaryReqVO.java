package com.rf.exchange.module.candle.controller.app.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * APP K线简化数据查询请求VO
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Schema(description = "APP K线简化数据查询请求VO")
@Data
public class AppKlineSummaryReqVO {

    /**
     * {@link TradeAssetTypeEnum}
     */
    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇 4:自选", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "资产类型不能为空")
    @InEnum(value = TradeAssetTypeEnum.class, message = "资产类型值错误")
    private Integer assetType;

    @Schema(description = "数据点数量，默认24个点(24小时)", example = "24")
    private Integer dataPoints = 24;
}
