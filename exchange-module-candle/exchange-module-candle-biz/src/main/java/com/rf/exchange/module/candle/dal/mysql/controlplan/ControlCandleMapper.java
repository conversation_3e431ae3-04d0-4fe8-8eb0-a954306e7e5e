package com.rf.exchange.module.candle.dal.mysql.controlplan;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandlePageReqVO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 控盘k线(用于接口数据替换) Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ControlCandleMapper extends BaseMapperX<ControlCandleDO> {

    default PageResult<ControlCandleDO> selectPage(ControlCandlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ControlCandleDO>()
                .eqIfPresent(ControlCandleDO::getCode, reqVO.getCode())
                .eqIfPresent(ControlCandleDO::getHighPrice, reqVO.getHighPrice())
                .eqIfPresent(ControlCandleDO::getLowPrice, reqVO.getLowPrice())
                .eqIfPresent(ControlCandleDO::getOpenPrice, reqVO.getOpenPrice())
                .eqIfPresent(ControlCandleDO::getClosePrice, reqVO.getClosePrice())
                .eqIfPresent(ControlCandleDO::getVolume, reqVO.getVolume())
                .eqIfPresent(ControlCandleDO::getTurnover, reqVO.getTurnover())
                .betweenIfPresent(ControlCandleDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ControlCandleDO::getIsMarketClose, reqVO.getIsMarketClose())
                .orderByDesc(ControlCandleDO::getId));
    }


    void batchInsertControlCandles(@Param("controlCandles") List<ControlCandleDO> controlCandles);

}