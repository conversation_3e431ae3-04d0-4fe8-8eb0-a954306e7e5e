package com.rf.exchange.module.candle.service.controlplan;

import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;

import java.util.List;
import java.util.Map;

/**
 * 控盘计划的时间节点 Service 接口
 *
 * <AUTHOR>
 */
public interface ControlPricePointService {

    /**
     * 获取控盘计划的时间节点信息
     *
     * @param planId 计划id
     * @return 时间节点信息列表 key:时间戳
     */
    Map<Long, ControlPricePointDO> getControlPricePointListOfPlan(long planId);

    /**
     * 保存价格节点列表
     *
     * @param planDO
     * @param pricePointList 价格节点
     */
    void savePricePoints(ControlPlanDO planDO, List<ControlPricePointDTO> pricePointList);
}