package com.rf.exchange.module.candle.dal.redis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 订单薄
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CandleOrderBookListDTO implements Serializable {
    @Serial
    private final static long serialVersionUID = 1L;

    /**
     * 交易对代码
     */
    private String tradePairCode;

    /**
     * 买单列表
     */
    private List<CandleOrderBookDTO> bids = Collections.emptyList();
    /**
     * 卖单列表
     */
    private List<CandleOrderBookDTO> asks = Collections.emptyList();
}
