package com.rf.exchange.module.candle.dal.dataobject.candle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * K线数据对象基础类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
@TableName("data_candle_tradepair")
public class CandleDO implements Serializable, TransPojo {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 时间范围
     */
    private Integer timeRange;
    /**
     * 时间范围类型
     */
    private Integer timeType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    /**
     * 开盘价格
     */
    private BigDecimal openPrice;
    /**
     * 收盘价格
     */
    private BigDecimal closePrice;
    /**
     * 交易量
     */
    private BigDecimal volume;
    /**
     * 成交金额
     */
    private BigDecimal turnover;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 是否是休市k线
     */
    private Boolean isMarketClose;
}
