package com.rf.exchange.module.candle.dal.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanKlineDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.rf.exchange.module.candle.dal.redis.RedisKeyConstants.CANDLE_CONTROL_KLINE;

/**
 * <AUTHOR>
 * @since 2024-11-03
 */
@Repository
public class CandleControlPlanKlineRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存控盘的价格节点列表
     *
     * @param pricePointList 价格节点列表
     */
    public void savePlanKLines(long planId, List<ControlPlanKlineDO> pricePointList) {
        if (CollUtil.isEmpty(pricePointList)) {
            return;
        }
        final String json = JsonUtils.toJsonString(pricePointList);
        redisTemplate.opsForValue().set(formatKey(planId), json);
    }

    /**
     * 获取控盘的价格节点列表
     * @return 价格节点列表
     */
    public List<ControlPlanKlineDO> getPlanKLines(long planId) {
        final String json = (String) redisTemplate.opsForValue().get(formatKey(planId));
        if (StrUtil.isEmpty(json)) {
            return List.of();
        }
        return JsonUtils.parseArray(json, ControlPlanKlineDO.class);
    }

    /**
     * 删除控盘计划的价格节点
     * @param planId 计划id
     */
    public void deletePricePoints(long planId) {
        redisTemplate.delete(formatKey(planId));
    }

    private String formatKey(long planId) {
        return CANDLE_CONTROL_KLINE + ":" + planId;
    }
}
