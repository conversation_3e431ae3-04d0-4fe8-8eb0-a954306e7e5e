package com.rf.exchange.module.candle.dal.mysql.candle;

import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
@Mapper
public interface CandleTableMapper {

    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_NAME LIKE 'data_candle_tradepair_%'")
    List<String> selectAllCandleTableNames();

    void createTable(@Param("tableName") String tableName, @Param("comment") String comment);

}
