package com.rf.exchange.module.candle.util;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 * k线聚合
 *
 * <AUTHOR>
 * @since 2024-10-27
 */
@Slf4j
public class CandleKLineAggregate {

    /**
     * 组装价格节点为k线
     *
     * @param code        交易对代码
     * @param pricePoints 价格节点
     * @return k线列表Map, key:CandleTimeRangeEnum的kLineTypeAllTick值 value:k线列表
     */
    public Map<Integer, List<CandleDO>> aggregatePricePoint(String code, List<ControlPricePointDTO> pricePoints) {
        if (CollUtil.isEmpty(pricePoints)) {
            return Map.of();
        }

        pricePoints.sort(Comparator.comparing(ControlPricePointDTO::getTimestamp));

        Map<Integer, List<CandleDO>> map = new HashMap<>();
        int pointSize = pricePoints.size();
        if (pointSize >= 60) {
            final List<CandleDO> candlesMin1 = aggregateKLines(code, pricePoints, CandleTimeRangeEnum.MIN_ONE);
            map.put(CandleTimeRangeEnum.MIN_ONE.getKLineTypeAllTick(), candlesMin1);
        }
        if (pointSize >= 300) {
            final List<CandleDO> candlesMin5 = aggregateKLines(code, pricePoints, CandleTimeRangeEnum.MIN_FIVE);
            map.put(CandleTimeRangeEnum.MIN_FIVE.getKLineTypeAllTick(), candlesMin5);
        }
        if (pointSize >= 900) {
            final List<CandleDO> candlesMin15 = aggregateKLines(code, pricePoints, CandleTimeRangeEnum.MIN_FIFTEEN);
            map.put(CandleTimeRangeEnum.MIN_FIFTEEN.getKLineTypeAllTick(), candlesMin15);
        }
        if (pointSize >= 1800) {
            final List<CandleDO> candlesMin30 = aggregateKLines(code, pricePoints, CandleTimeRangeEnum.MIN_THIRTY);
            map.put(CandleTimeRangeEnum.MIN_THIRTY.getKLineTypeAllTick(), candlesMin30);
        }
        if (pointSize >= 3600) {
            final List<CandleDO> candlesHour = aggregateKLines(code, pricePoints, CandleTimeRangeEnum.HOUR_ONE);
            map.put(CandleTimeRangeEnum.HOUR_ONE.getKLineTypeAllTick(), candlesHour);
        }
        return map;
    }

    /**
     * @param code          交易对代码
     * @param points        价格节点列表
     * @param timeRangeEnum 时间范围枚举
     * @return k线数据列表
     */
    public static List<CandleDO> aggregateKLines(String code, List<ControlPricePointDTO> points, CandleTimeRangeEnum timeRangeEnum) {
        List<CandleDO> kLines = new ArrayList<>();
        if (points == null || points.isEmpty()) return kLines;

        long intervalInMin = timeRangeEnum.getSecs();
        long currentIntervalStart = (points.getFirst().getTimestamp() / intervalInMin) * intervalInMin;

        final ControlPricePointDTO firstPoint = points.getFirst();
        BigDecimal open = firstPoint.getReferPrice().add(firstPoint.getPriceDiff());
        BigDecimal close = open;
        BigDecimal high = open;
        BigDecimal low = open;
        BigDecimal volume = BigDecimal.ZERO;

        for (int i = 0; i < points.size(); i++) {
            ControlPricePointDTO point = points.get(i);
            BigDecimal price = point.getReferPrice().subtract(point.getPriceDiff());

            // 如果当前数据点的时间戳超出了当前时间段
            if (point.getTimestamp() >= currentIntervalStart + intervalInMin) {

                // 将当前聚合的K线数据添加到结果列表
                CandleDO candle = createCandle(code, timeRangeEnum, currentIntervalStart, open, close, high, low, volume);
                kLines.add(candle);

                // 重新计算新的时间段起始时间，确保没有时间跳跃
                currentIntervalStart = (point.getTimestamp() / intervalInMin) * intervalInMin;

                // 重置新时间段的开盘、最高、最低价及交易量
                open = price;
                high = price;
                low = price;
                volume = point.getVolume();
            }

            // 聚合当前时间段的数据
            close = price;
            high = high.max(price);
            low = low.min(price);
            volume = volume.add(point.getVolume());

            log.debug("节点的时间戳:[{}] 当前时间段开始:[{}] 时间范围:[{}] idx:[{}] k线数量:[{}]", point.getTimestamp(), currentIntervalStart, intervalInMin, i, kLines.size());
        }

        // 添加最后一个时间段的K线数据
        CandleDO candle = createCandle(code, timeRangeEnum, currentIntervalStart, open, close, high, low, volume);
        kLines.add(candle);

        return kLines;
    }

    private static CandleDO createCandle(String code,
                                         CandleTimeRangeEnum timeRangeEnum,
                                         long startTS,
                                         BigDecimal openPrice,
                                         BigDecimal closePrice,
                                         BigDecimal highPrice,
                                         BigDecimal lowPrice,
                                         BigDecimal volume) {
        CandleDO candle = new CandleDO();
        candle.setCode(code);
        candle.setClosePrice(closePrice);
        candle.setOpenPrice(openPrice);
        candle.setHighPrice(highPrice);
        candle.setLowPrice(lowPrice);
        candle.setVolume(volume);
        candle.setTimestamp(startTS);
        candle.setTimeRange(timeRangeEnum.getRange());
        candle.setTimeType(timeRangeEnum.getTypeValue());
        candle.setCreateTime(LocalDateTime.ofEpochSecond(startTS, 0, ZoneOffset.UTC));
        candle.setIsMarketClose(false);
        return candle;
    }
}
