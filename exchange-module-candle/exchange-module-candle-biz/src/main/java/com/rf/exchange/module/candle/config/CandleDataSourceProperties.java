package com.rf.exchange.module.candle.config;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 三方数据配置类
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@Validated
@ConfigurationProperties(prefix = "exchange.candle-datasource")
public class CandleDataSourceProperties {
    /**
     * 是否启用candle的数据源
     */
    private boolean enabled = false;
    /**
     * polygon数据源
     */
    private PolygonDataSource polygon;
    /**
     * AllTick数据源
     */
    private AllTickDataSource alltick;

    @Getter
    @Setter
    public static class PolygonDataSource {
        /**
         * 是否启用
         */
        private Boolean enable;
        /**
         * http接口请求地址
         * 外汇、加密货币、大宗商品
         */
        private String host;
        /**
         * http接口请求密钥
         */
        private String secret;
        /**
         * 外汇ws连接地址
         */
        private String wsHostForex;
        /**
         * 加密货币的ws地址
         */
        private String wsHostCrypto;
        /**
         * 心跳间隔，单位秒
         */
        private Integer heartSec;
    }

    @Setter
    @Getter
    public static class AllTickDataSource {
        /**
         * 是否启用
         */
        @NotNull(message = "enable不能为空")
        private Boolean enable;
        /**
         * http接口请求地址
         * 外汇、加密货币、大宗商品
         */
        @NotEmpty(message = "host不能为空")
        private String host;
        /**
         * 单个查询k线的接口
         */
        @NotEmpty(message = "singleKLine不能为空")
        private String singleKLine;
        /**
         * 批量查询k线的接口
         */
        @NotEmpty(message = "batchKLine不能为空")
        private String batchKLine;
        /**
         * http接口请求密钥
         */
        @NotEmpty(message = "secret不能为空")
        private String secret;
        /**
         * ws连接地址
         * 外汇、加密货币、大宗商品
         */
        @NotEmpty(message = "wsHost不能为空")
        private String wsHost;
        /**
         * 心跳间隔，单位秒
         */
        @NotNull(message = "heartSec不能为空")
        private Integer heartSec;
        /**
         * 买卖单深度，默认5个订单薄
         */
        private Integer deep = 5;
    }
}
