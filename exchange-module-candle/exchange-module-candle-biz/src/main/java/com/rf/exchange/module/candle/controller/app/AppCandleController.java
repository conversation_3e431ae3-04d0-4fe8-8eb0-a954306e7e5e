package com.rf.exchange.module.candle.controller.app;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.candle.CandleConvert;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.CandleService;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleListReqVO;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@Tag(name = "用户 APP - K线接口")
@RestController
@RequestMapping("/candle/")
public class AppCandleController {

    @Resource
    private CandleService candleService;
    @Resource
    private CandleTodayKlinePriceRedisDAO candleTodayKlinePriceRedisDAO;

    @Operation(summary = "获取K线列表")
    @GetMapping("/list")
    @PreAuthenticated
    public CommonResult<List<AppCandleRespVO>> candleList(@Valid AppCandleListReqVO reqVO) {
        final List<CandleDO> candleDOList = candleService.appGetCandleList(reqVO);
        CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.enumOf(reqVO.getBar());
        if (timeRangeEnum == CandleTimeRangeEnum.DAY_ONE) {
            TodayKlinePriceDTO todayKlinePriceDTO = candleTodayKlinePriceRedisDAO.get(reqVO.getCode());
            if (todayKlinePriceDTO != null && candleDOList.size() > 0) {
                candleDOList.remove(0);
                CandleDO todayCandle = new CandleDO();
                todayCandle.setCode(todayKlinePriceDTO.getTradePairCode());
                todayCandle.setTimeRange(timeRangeEnum.getRange());
                todayCandle.setTimeType(timeRangeEnum.getTypeValue());
                todayCandle.setTimestamp(Long.valueOf(todayKlinePriceDTO.getTimestamp()));
                todayCandle.setHighPrice(new BigDecimal(todayKlinePriceDTO.getHighPrice()));
                todayCandle.setLowPrice(new BigDecimal(todayKlinePriceDTO.getLowPrice()));
                todayCandle.setOpenPrice(new BigDecimal(todayKlinePriceDTO.getOpenPrice()));
                todayCandle.setClosePrice(new BigDecimal(todayKlinePriceDTO.getClosePrice()));
                todayCandle.setVolume(new BigDecimal(todayKlinePriceDTO.getVolume24H()));
                todayCandle.setTurnover(new BigDecimal(todayKlinePriceDTO.getTurnover()));
                candleDOList.add(0, todayCandle);
            }
        }
        if (timeRangeEnum == CandleTimeRangeEnum.WEEK_ONE) {
            TodayKlinePriceDTO todayKlinePriceDTO = candleTodayKlinePriceRedisDAO.get(reqVO.getCode());
            long weekStartTime = DateUtils.getWeekStartTime();
            long endTime=DateUtils.getUnixTimestampNow() / 1000;
            CandleDO todayCandle = new CandleDO();
            todayCandle.setCode(reqVO.getCode());
            todayCandle.setTimeRange(timeRangeEnum.getRange());
            todayCandle.setTimeType(timeRangeEnum.getTypeValue());
            todayCandle.setTimestamp(weekStartTime);
            BigDecimal weekHighPrice = candleService.getHighPrice(reqVO.getCode(), weekStartTime, endTime);
            BigDecimal weekLowPrice = candleService.getLowPrice(reqVO.getCode(), weekStartTime, endTime);
            CandleDO startCandle = candleService.getFirstCandle(reqVO.getCode(), weekStartTime);
            BigDecimal volume=candleService.getSumVolume(reqVO.getCode(),weekStartTime,endTime);
            BigDecimal turnover=candleService.getSumTurnover(reqVO.getCode(),weekStartTime,endTime);
            todayCandle.setHighPrice(weekHighPrice);
            todayCandle.setLowPrice(weekLowPrice);
            todayCandle.setOpenPrice(startCandle.getOpenPrice());
            todayCandle.setClosePrice(new BigDecimal(todayKlinePriceDTO.getClosePrice()));
            todayCandle.setVolume(volume);
            todayCandle.setTurnover(turnover);
            candleDOList.add(0, todayCandle);
        }
        return success(CandleConvert.INSTANCE.convertList3(candleDOList));
    }
}
