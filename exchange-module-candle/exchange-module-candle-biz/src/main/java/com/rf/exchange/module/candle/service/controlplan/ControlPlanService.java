package com.rf.exchange.module.candle.service.controlplan;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.candle.controller.admin.controlplan.vo.*;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import jakarta.validation.Valid;

import java.math.BigDecimal;
import java.util.List;

/**
 * 复制币和自发币的控盘计划参数 Service 接口
 *
 * <AUTHOR>
 */
public interface ControlPlanService {

    /**
     * 更新控盘计划状态
     *
     * @param reqVO 请求信息
     */
    void updateControlPlanStatus(@Valid ControlPlanStatusUpdateReqVO reqVO);

    /**
     * 删除复制币和自发币的控盘计划参数
     *
     * @param id 编号
     */
    void deleteControlPlan(Long id);

    /**
     * 获得复制币和自发币的控盘计划参数
     *
     * @param id 编号
     * @return 复制币和自发币的控盘计划参数
     */
    ControlPlanDO getControlPlan(Long id);

    /**
     * 获得复制币和自发币的控盘计划参数分页
     *
     * @param pageReqVO 分页查询
     * @return 复制币和自发币的控盘计划参数分页
     */
    PageResult<ControlPlanDO> getControlPlanPage(ControlPlanPageReqVO pageReqVO);

    /**
     * 获取控盘计划的预览k线
     *
     * @param previewReqVO 请求参数
     * @param tenantId
     * @return 预览k线数据列表
     */
    ControlPlanPreviewRespVO getControlPlanPreviewCandles(ControlPlanPreviewReqVO previewReqVO, Long tenantId);

    /**
     * 保存控盘计划的预览k线
     *
     * @param reqVO 请求参数
     */
    void saveControlPlanPreview(ControlPlanPlanSaveReqVO reqVO);

    /**
     * 获取交易对的运行中的控盘计划
     *
     * @param code 交易对代码
     * @return 运行中的控盘计划
     */
    ControlPlanDO getRunningPlanOfCode(String code);

    /**
     * 获取所有运行中的控盘计划
     *
     * @return 控盘计划列表
     */
    List<ControlPlanDO> getRunningPlans();

    /**
     * 获取所有等待中的控盘计划
     *
     * @return 控盘计划列表
     */
    List<ControlPlanDO> getWaitingPlans();


    /**
     * 开始执行控盘计划
     *
     * @param planId    计划id
     * @param execPrice
     */
    void startPlan(long planId, BigDecimal execPrice);

    /**
     * 结束运行的控盘计划
     *
     * @param planId 计划id
     */
    void finishPlan(long planId);
}