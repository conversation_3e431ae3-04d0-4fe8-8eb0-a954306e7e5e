package com.rf.exchange.module.candle.service.controlplan;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPricePointDO;
import com.rf.exchange.module.candle.dal.mysql.controlplan.ControlPricePointMapper;
import com.rf.exchange.module.candle.dal.redis.CandleControlPricePointRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 控盘计划的时间节点 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ControlPricePointServiceImpl implements ControlPricePointService {

    @Resource
    private ControlPricePointMapper controlPricePointMapper;
    @Resource
    private CandleControlPricePointRedisDAO controlPricePointRedisDAO;

    @Override
    public Map<Long, ControlPricePointDO> getControlPricePointListOfPlan(long planId) {
        final Map<Long, ControlPricePointDO> pricePointMap = controlPricePointRedisDAO.getPricePoints(planId);
        if (CollUtil.isNotEmpty(pricePointMap)) {
            return pricePointMap;
        }

        LambdaQueryWrapperX<ControlPricePointDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ControlPricePointDO::getPlanId, planId);
        wrapper.orderByDesc(ControlPricePointDO::getTimestamp);
        final List<ControlPricePointDO> pointDOS = controlPricePointMapper.selectList(wrapper);

        controlPricePointRedisDAO.savePricePoints(planId, pointDOS);

        return pointDOS.stream().collect(Collectors.toMap(ControlPricePointDO::getTimestamp, x -> x));
    }

    @Override
    @Master
    @DSTransactional
    public void savePricePoints(ControlPlanDO planDO, List<ControlPricePointDTO> pricePointList) {
        if (CollUtil.isEmpty(pricePointList)) {
            return;
        }
        long now = DateUtil.currentSeconds();
        final List<ControlPricePointDO> prices = pricePointList.stream().map(dto -> {
            final ControlPricePointDO priceDO = BeanUtils.toBean(dto, ControlPricePointDO.class);
            priceDO.setTradePairCode(planDO.getTradePairCode());
            priceDO.setPlanId(planDO.getId());
            return priceDO;
        }).toList();
        controlPricePointMapper.batchInsertPricePoints(prices);
        controlPricePointRedisDAO.savePricePoints(planDO.getId(), prices);
    }
}