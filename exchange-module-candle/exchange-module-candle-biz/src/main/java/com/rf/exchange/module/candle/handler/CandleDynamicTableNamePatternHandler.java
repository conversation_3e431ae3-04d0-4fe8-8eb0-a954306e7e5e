package com.rf.exchange.module.candle.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.rf.exchange.module.candle.context.CandleTableContextHolder;

import java.util.regex.Pattern;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.CANDLE_TABLE_NAME_NOT_AVAILABLE;

/**
 * <AUTHOR>
 * @since 2024-07-20
 */
public class CandleDynamicTableNamePatternHandler implements TableNameHandler {

    private final String pattern;
    private final Pattern codePattern;

    public CandleDynamicTableNamePatternHandler(String pattern) {
        this.pattern = pattern;
        codePattern = Pattern.compile(this.pattern + "_([a-zA-Z_]*)");
    }

    @Override
    public String dynamicTableName(String sql, String tableName) {
        // 判断表名是否复合交易对的k线表名
        if (StrUtil.isNotEmpty(tableName) && tableName.startsWith(pattern)) {
            // 如果tableName已经是完整的表名则不处理直接返回
            if (codePattern.matcher(tableName).find()) {
                return tableName;
            }
            // 如果运行上下文中没有指定系统交易对代码则抛出异常
            String tradePairCode = CandleTableContextHolder.getTradePairCode();
            if (StrUtil.isEmpty(tradePairCode)) {
                throw exception(CANDLE_TABLE_NAME_NOT_AVAILABLE);
            }
            return tableName + "_" + tradePairCode.toLowerCase();
        }
        return tableName;
    }
}
