package com.rf.exchange.module.candle.controller.admin;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.candle.CandleConvert;
import com.rf.exchange.module.candle.api.dto.CandleDTO;
import com.rf.exchange.module.candle.controller.admin.vo.AdminCandlePreviewReqVo;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.redis.CandleCustomTradePairRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.CandleService;
import com.rf.exchange.module.candle.service.candle.vo.AdminCandleListReqVO;
import com.rf.exchange.module.candle.service.candlecontrol.CandleControlService;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.TradePairControlApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Hashtable;
import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.*;

@Deprecated
@Tag(name = "后台管理 - K线预览接口")
@RestController
@RequestMapping("/candle/")
public class AdminCandleController {
    @Resource
    private CandleService candleService;
    @Resource
    private CandleControlService candleControlService;
    @Resource
    private TradePairControlApi tradePairControlApi;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private CandleTodayKlinePriceRedisDAO candleTodayKlinePriceRedisDAO;
    @Resource
    private CandleCustomTradePairRedisDAO candleCustomTradePairRedisDAO;

    //    @Operation(summary = "生成k线列表")
//    @GetMapping("/gen")
//    @PreAuthorize("@ss.hasPermission('candle:gen:create')")
//    public CommonResult<Boolean> gen(Long tradePairControlId) {
//        TradePairControlRespDTO tradePairControlRespDTO = tradePairControlApi.get(tradePairControlId);
//        candleControlService.genCandleControl(tradePairControlRespDTO);
//        return success(Boolean.TRUE);
//    }
    static Hashtable<Long, List<CandleDO>> ControlCandleMap = new Hashtable<>();

    @Operation(summary = "生成预览k线列表")
    @GetMapping("/gen")
    @PreAuthorize("@ss.hasPermission('candle:gen:create')")
    public CommonResult<List<CandleDO>> gen(@Valid AdminCandlePreviewReqVo reqVo) {
        long minStartTime = DateUtils.getUnixTimestampNow() + 120000;
        if (reqVo.getStartTime() < minStartTime) {
            throw exception(CANDLE_MIN_START_ERROR);
        }
        BigDecimal startPrice = BigDecimal.ZERO;
        //先取交易对
        TradePairRespDTO tradePairRespDTO = tradePairApi.getTradePair(reqVo.getTradePairId());
        //如果是复制币
        if (tradePairRespDTO.getIsCopy()) {
            CandleDO candleDO = candleService.getLastCandle(tradePairRespDTO.getCode(), CandleTimeRangeEnum.MIN_ONE, null, null);
            reqVo.setStartTime(candleDO.getTimestamp()*1000);
            startPrice = candleDO.getClosePrice();
        } else {
            //取开始时间那根k线
            AdminCandleListReqVO adminCandleListReqVO = new AdminCandleListReqVO();
            adminCandleListReqVO.setCode(tradePairRespDTO.getCode());
            adminCandleListReqVO.setStartTs(reqVo.getStartTime() / 1000 / 60 * 60);
            adminCandleListReqVO.setEndTs(reqVo.getStartTime() / 1000 / 60 * 60);
            adminCandleListReqVO.setBar("1m");
            final List<CandleDO> candleDOList = candleService.getAdminCandle(adminCandleListReqVO);
            if (candleDOList.size() == 0) {
                throw exception(CANDLE_NOT_GENERATE);
            }
            CandleDO startCandle = candleDOList.get(0);//开始时间的k线
            startPrice = startCandle.getOpenPrice();
        }

        //生成控盘k线
        List<CandleDO> controlCandleList = candleControlService.genControlCandleOneMinute(tradePairRespDTO, reqVo.getOneMinuteTurnover(), reqVo.getStartTime() / 1000, startPrice, reqVo.getEndTime() / 1000, reqVo.getEndPrice());
        //把预览的k线保存在缓存中
        ControlCandleMap.put(reqVo.getTradePairId(), controlCandleList);
        return success(controlCandleList);
    }

    @Operation(summary = "保存预览k线列表")
    @PostMapping("/save")
    @PreAuthorize("@ss.hasPermission('candle:gen:create')")
    public CommonResult<Boolean> save(@Valid @RequestBody AdminCandlePreviewReqVo reqVo) {
        List<CandleDO> list = ControlCandleMap.get(reqVo.getTradePairId());
        if (list == null || list.isEmpty()) {
            throw exception(CANDLE_NOT_PREVIEW);
        }
        candleControlService.saveControlCandleList(reqVo, list);
        return success(true);
    }

//    @Operation(summary = "获取K线列表")
//    @GetMapping("/preview")
//    @PreAuthorize("@ss.hasPermission('candle:gen:query')")
//    public CommonResult<List<AppCandleRespVO>> preview(long tradePairControlId, String bar) {
//        TradePairControlRespDTO tradePairControlRespDTO = tradePairControlApi.get(tradePairControlId);
//        AdminCandleListReqVO adminCandleListReqVO = new AdminCandleListReqVO();
//        adminCandleListReqVO.setCode(tradePairControlRespDTO.getTradePairCode());
//        adminCandleListReqVO.setStartTs(tradePairControlRespDTO.getStartTime() / 1000);
//        adminCandleListReqVO.setEndTs(tradePairControlRespDTO.getEndTime() / 1000);
//        adminCandleListReqVO.setBar(bar);
//        final List<CandleDO> candleDOList = candleService.getAdminCandle(adminCandleListReqVO);
//        return success(CandleConvert.INSTANCE.convertList3(candleDOList));
//    }

    @Operation(summary = "获取一根一分钟k线")
    @GetMapping("/getcandle")
//    @PreAuthorize("@ss.hasPermission('candle:gen:query')")
    public CommonResult<CandleDTO> getCandle(long tradePairId, long time) {
        if (tradePairId <= 0) {
            return CommonResult.error(NOT_CUSTOM_TRADE_PAIR);
        }
        TradePairRespDTO tradePairRespDTO = tradePairApi.getTradePair(tradePairId);
        if (tradePairRespDTO == null) {
            return CommonResult.error(NOT_CUSTOM_TRADE_PAIR);
        }
        CandleDO startCandle = null;//开始时间的k线
        //如果是复制币
        if (tradePairRespDTO.getIsCopy()) {
            startCandle = candleService.getLastCandle(tradePairRespDTO.getCode(), CandleTimeRangeEnum.MIN_ONE, null, null);
        } else {
            AdminCandleListReqVO adminCandleListReqVO = new AdminCandleListReqVO();
            adminCandleListReqVO.setCode(tradePairRespDTO.getCode());
            adminCandleListReqVO.setStartTs(time / 1000 / 60 * 60);
            adminCandleListReqVO.setEndTs(time / 1000 / 60 * 60);
            adminCandleListReqVO.setBar("1m");
            final List<CandleDO> candleDOList = candleService.getAdminCandle(adminCandleListReqVO);
            if (candleDOList.isEmpty()) {
                throw exception(CANDLE_NOT_GENERATE);
            }
            startCandle=candleDOList.get(0);
        }

        return success(CandleConvert.INSTANCE.convert(startCandle));
    }
}
