package com.rf.exchange.module.candle.context;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * k线的上下文
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
public class CandleTableContextHolder {
    /**
     * 当前的交易对代码
     */
    public static final ThreadLocal<String> TRADE_PAIR_CODE = new TransmittableThreadLocal<>();

    /**
     * 获取交易对代码
     *
     * @return 交易对代码
     */
    public static String getTradePairCode() {
        return TRADE_PAIR_CODE.get();
    }

    public static void setTradePairCode(String tradePairCode) {
        TRADE_PAIR_CODE.set(tradePairCode);
    }

    public static void clear() {
        TRADE_PAIR_CODE.remove();
    }
}
