package com.rf.exchange.module.candle.dal.redis;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlPlanDO;
import com.rf.exchange.module.candle.enums.CandleControlPlanStatusEnum;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-10-27
 */
@Repository
public class CandleControlPlanRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存控盘数据
     */
    public void saveControlPlanData(String key, List<ControlPricePointDTO> pricePointList, Map<Integer, List<CandleDO>> candleMap, ControlPlanDO planDO) {
        String redisKey = formatDataKey(key);
        final String pricePointJson = JsonUtils.toJsonString(pricePointList);
        redisTemplate.opsForHash().put(redisKey, "pricePoints", pricePointJson);

        final String candleMapJson = JsonUtils.toJsonString(candleMap);
        redisTemplate.opsForHash().put(redisKey, "candleMap", candleMapJson);

        final String planJson = JsonUtils.toJsonString(planDO);
        redisTemplate.opsForHash().put(redisKey, "plan", planJson);

        // 过期时间为3天
        redisTemplate.expire(redisKey, 3, TimeUnit.DAYS);
    }

    /**
     * 获取控盘数据
     */
    public Map<String, Object> getControlPlanData(String key) {
        final String redisKey = formatDataKey(key);
        final Map<Object, Object> entries = redisTemplate.opsForHash().entries(redisKey);
        final String pricePointsJson = (String) entries.get("pricePoints");
        final String candleJson = (String) entries.get("candleMap");
        final String planJson = (String) entries.get("plan");

        Map<String, Object> resultMap = new HashMap<>();

        // 价格节点
        final List<ControlPricePointDTO> pointDTOListObj = JsonUtils.parseArray(pricePointsJson, ControlPricePointDTO.class);
        resultMap.put("pricePoints", pointDTOListObj);

        // 临时k线
        final Map<String, Object> candleMapObj = JsonUtils.parseObject(candleJson, Map.class);
        if (candleMapObj != null) {
            Map<Integer, List<CandleDO>> resultCandleMap = new HashMap<>();
            for (Map.Entry<String, ?> entry : candleMapObj.entrySet()) {
                if (entry.getValue() instanceof List) {
                    List<Object> list = (List<Object>) entry.getValue();
                    final List<CandleDO> candles = BeanUtils.toBean(list, CandleDO.class);
                    resultCandleMap.put(Integer.parseInt(entry.getKey()), candles);
                }
            }
            resultMap.put("candleMap", resultCandleMap);
        }

        // 控盘计划
        ControlPlanDO planDO = JsonUtils.parseObject(planJson, ControlPlanDO.class);
        resultMap.put("plan", planDO);

        return resultMap;
    }


    /**
     * 添加等待生效的控盘计划
     *
     * @param planDO 控盘
     */
    public void addPlanToWait(ControlPlanDO planDO) {
        final String json = JsonUtils.toJsonString(planDO);
        redisTemplate.opsForHash().put(waitPlanKey(), String.valueOf(planDO.getId()), json);
    }

    /**
     * 移除等待状态的控盘计划
     *
     * @param planId 计划id
     */
    public void removePlanFromWait(long planId) {
        redisTemplate.opsForHash().delete(waitPlanKey(), String.valueOf(planId));
    }

    /**
     * 获取等待中的控盘计划
     *
     * @return 计划列表
     */
    public List<ControlPlanDO> getWaitingPlans() {
        return getPlanListWithKey(waitPlanKey());
    }

    public void saveWaitingPlans(List<ControlPlanDO> planDOList) {
        Map<String, String> planMap = new HashMap<>();
        for (ControlPlanDO planDO : planDOList) {
            String json = JsonUtils.toJsonString(planDO);
            planMap.put(String.valueOf(planDO.getId()), json);
        }
        redisTemplate.opsForHash().putAll(waitPlanKey(), planMap);
    }

    /**
     * 获取运行中的计划
     *
     * @return 控盘计划列表
     */
    public List<ControlPlanDO> getRunningPlans() {
        return getPlanListWithKey(runningPlanKey());
    }

    public void saveRunningPlans(List<ControlPlanDO> planDOList) {
        Map<String, String> planMap = new HashMap<>();
        for (ControlPlanDO planDO : planDOList) {
            String json = JsonUtils.toJsonString(planDO);
            planMap.put(String.valueOf(planDO.getId()), json);
        }
        redisTemplate.opsForHash().putAll(runningPlanKey(), planMap);
    }

    /**
     * 获取等待中的控盘计划
     *
     * @param planId 计划id
     * @return 控盘计划
     */
    public ControlPlanDO getWaitPlanById(long planId) {
        final Object obj = redisTemplate.opsForHash().get(waitPlanKey(), String.valueOf(planId));
        if (obj != null) {
            return JsonUtils.parseObject((String) obj, ControlPlanDO.class);
        }
        return null;
    }

    /**
     * 获取运行中的控盘计划
     *
     * @param planId 计划id
     * @return 控盘计划
     */
    public ControlPlanDO getRunningPlanById(long planId) {
        final Object obj = redisTemplate.opsForHash().get(runningPlanKey(), String.valueOf(planId));
        if (obj != null) {
            return JsonUtils.parseObject((String) obj, ControlPlanDO.class);
        }
        return null;
    }

    private List<ControlPlanDO> getPlanListWithKey(String key) {
        List<ControlPlanDO> planDOList = new ArrayList<>();
        final Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            final ControlPlanDO planDO = JsonUtils.parseObject(entry.getValue().toString(), ControlPlanDO.class);
            planDOList.add(planDO);
        }
        return planDOList;
    }

    /**
     * 转换控盘计划的状态
     *
     * @param planDO       计划id
     * @param fromStatus   起始状态
     * @param targetStatus 目标状态
     */
    public void transferPlanStatus(ControlPlanDO planDO, CandleControlPlanStatusEnum fromStatus, CandleControlPlanStatusEnum targetStatus) {
        if (fromStatus == CandleControlPlanStatusEnum.WAITING && targetStatus == CandleControlPlanStatusEnum.CLOSED) {
            removePlanFromWait(planDO.getId());
        } else if (fromStatus == CandleControlPlanStatusEnum.WAITING && targetStatus == CandleControlPlanStatusEnum.RUNNING) {
            final Object obj = redisTemplate.opsForHash().get(waitPlanKey(), String.valueOf(planDO.getId()));
            if (obj != null) {
                removePlanFromWait(planDO.getId());
                redisTemplate.opsForHash().put(runningPlanKey(), String.valueOf(planDO.getId()), obj);
            }
        } else if (fromStatus == CandleControlPlanStatusEnum.CLOSED && targetStatus == CandleControlPlanStatusEnum.WAITING) {
            addPlanToWait(planDO);
        } else if ((fromStatus == CandleControlPlanStatusEnum.RUNNING && targetStatus == CandleControlPlanStatusEnum.FINISHED)
                || (fromStatus == CandleControlPlanStatusEnum.FINISHED && targetStatus == CandleControlPlanStatusEnum.FINISHED)
                || (fromStatus == CandleControlPlanStatusEnum.CLOSED && targetStatus == CandleControlPlanStatusEnum.CLOSED)) {
            removeRunningPlan(planDO.getId());
            removePlanFromWait(planDO.getId());
        }
    }

    /**
     * 移除结束的控盘计划
     *
     * @param planId 计划id
     */
    public void removeRunningPlan(long planId) {
        redisTemplate.opsForHash().delete(runningPlanKey(), String.valueOf(planId));
    }

    private String formatDataKey(String key) {
        return RedisKeyConstants.PRIVATE_KEY_PREFIX + "plan_tmp_data:" + key;
    }

    private String waitPlanKey() {
        return RedisKeyConstants.PRIVATE_KEY_PREFIX + "plan_wait";
    }

    private String runningPlanKey() {
        return RedisKeyConstants.PRIVATE_KEY_PREFIX + "plan_running";
    }
}
