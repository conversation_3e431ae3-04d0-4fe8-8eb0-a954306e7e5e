package com.rf.exchange.module.candle.dal.dataobject.controlplan;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 控盘计划的时间节点 DO
 *
 * <AUTHOR>
 */
@TableName("candle_control_price_point")
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlPricePointDO implements Serializable, TransPojo {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 控盘计划参数id
     */
    private Long planId;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 参考价格
     * 实际价格根据这个priceBase作为计算具体价格的基础
     */
    private BigDecimal referPrice;
    /**
     * 价格差值(有正负)
     */
    private BigDecimal priceDiff;
    /**
     * 成交量
     */
    private BigDecimal volume;
    /**
     * 时间戳
     */
    private Long timestamp;
}