package com.rf.exchange.module.candle.dal.dataobject.controlplan;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import com.rf.exchange.module.candle.enums.CandleControlPlanStatusEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * 复制币和自发币的控盘计划参数 DO
 *
 * <AUTHOR>
 */
@TableName("candle_control_plan")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlPlanDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 交易对id
     */
    private Long tradePairId;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 控盘开始时间
     */
    private Long startTime;
    /**
     * 控盘结束时间
     */
    private Long endTime;
    /**
     * 生成计划时的交易对价格
     */
    private BigDecimal referPrice;
    /**
     * 执行计划时候的价格
     */
    private BigDecimal execPrice;
    /**
     * 结束价格
     */
    private BigDecimal endPrice;
    /**
     * 最低价
     */
    private BigDecimal minPrice;
    /**
     * 最低价
     */
    private BigDecimal maxPrice;
    /**
     * 波动率
     */
    private BigDecimal fluctuation;
    /**
     * 波动时长(秒)
     */
    private Integer durationSecs;
    /**
     * 控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成
     * {@link CandleControlPlanStatusEnum}
     */
    private Integer status;
    /**
     * 租户id
     */
    private Long tenantId;
}