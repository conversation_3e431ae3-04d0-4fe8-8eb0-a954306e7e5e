package com.rf.exchange.module.candle.service.controlcandle;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandlePageReqVO;
import com.rf.exchange.module.candle.controller.admin.controlcandle.vo.ControlCandleSaveReqVO;
import com.rf.exchange.module.candle.dal.dataobject.controlplan.ControlCandleDO;
import com.rf.exchange.module.candle.dal.mysql.controlplan.ControlCandleMapper;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.vo.AppCandleListReqVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.candle.enums.ErrorCodeConstants.CONTROL_CANDLE_NOT_EXISTS;

/**
 * 控盘k线(用于接口数据替换) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ControlCandleServiceImpl implements ControlCandleService {

    @Resource
    private ControlCandleMapper controlCandleMapper;

    @Override
    public Long createControlCandle(ControlCandleSaveReqVO createReqVO) {
        // 插入
        ControlCandleDO controlCandle = BeanUtils.toBean(createReqVO, ControlCandleDO.class);
        controlCandleMapper.insert(controlCandle);
        // 返回
        return controlCandle.getId();
    }

    @Override
    public void updateControlCandle(ControlCandleSaveReqVO updateReqVO) {
        // 校验存在
        validateControlCandleExists(updateReqVO.getId());
        // 更新
        ControlCandleDO updateObj = BeanUtils.toBean(updateReqVO, ControlCandleDO.class);
        controlCandleMapper.updateById(updateObj);
    }

    @Override
    public void deleteControlCandle(Long id) {
        // 校验存在
        validateControlCandleExists(id);
        // 删除
        controlCandleMapper.deleteById(id);
    }

    private void validateControlCandleExists(Long id) {
        if (controlCandleMapper.selectById(id) == null) {
            throw exception(CONTROL_CANDLE_NOT_EXISTS);
        }
    }

    @Override
    @Slave
    public ControlCandleDO getControlCandle(Integer id) {
        return controlCandleMapper.selectById(id);
    }

    @Override
    @Slave
    public PageResult<ControlCandleDO> getControlCandlePage(ControlCandlePageReqVO pageReqVO) {
        return controlCandleMapper.selectPage(pageReqVO);
    }

    @Override
    public void saveControlCandles(List<ControlCandleDO> candleDOList) {
        controlCandleMapper.batchInsertControlCandles(candleDOList);
    }

    @Override
    public List<ControlCandleDO> appGetControlCandleList(AppCandleListReqVO reqVO, CandleTimeRangeEnum timeRangeEnum) {
        LambdaQueryWrapperX<ControlCandleDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(
                ControlCandleDO::getClosePrice,
                ControlCandleDO::getHighPrice,
                ControlCandleDO::getOpenPrice,
                ControlCandleDO::getLowPrice,
                ControlCandleDO::getTimestamp,
                ControlCandleDO::getVolume,
                ControlCandleDO::getTurnover);
        wrapper.eqIfPresent(ControlCandleDO::getTimeRange, timeRangeEnum.getRange());
        wrapper.eqIfPresent(ControlCandleDO::getTimeType, timeRangeEnum.getTypeValue());
        wrapper.leIfPresent(ControlCandleDO::getTimestamp, reqVO.getEndTs());
        wrapper.orderByDesc(ControlCandleDO::getTimestamp);
        // 最多一次查询1000条数据
        int limit = (int) Math.min(reqVO.getLimit(), 1000);
        wrapper.last("limit " + limit);

        return controlCandleMapper.selectList(wrapper);
    }
}