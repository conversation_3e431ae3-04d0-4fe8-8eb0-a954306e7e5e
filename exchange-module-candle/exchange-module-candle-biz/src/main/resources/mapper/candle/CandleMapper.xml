<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.candle.CandleMapper">

    <sql id="tableNameSql">
        ${tableName}
    </sql>

    <insert id="batchInsertCandle">
        INSERT INTO
        <include refid="tableNameSql" />
        (id,
        time_range,
        time_type,
        timestamp,
        code,
        high_price,
        low_price,
        open_price,
        close_price,
        volume,
        turnover)
        VALUES
        <foreach collection="candleList" item="item" index="index" separator=",">
            (#{item.id},
            #{item.timeRange},
            #{item.timeType},
            #{item.timestamp},
            #{item.code},
            #{item.highPrice},
            #{item.lowPrice},
            #{item.openPrice},
            #{item.closePrice},
            #{item.volume},
            #{item.turnover})
        </foreach>
        ON DUPLICATE KEY UPDATE
        high_price=VALUES(high_price),
        low_price=VALUES(low_price),
        open_price=VALUES(open_price),
        close_price=VALUES(close_price),
        volume=VALUES(volume),
        turnover=VALUES(turnover)
    </insert>

    <sql id="srcTable">
        ${srcTable}
    </sql>
    <sql id="destTable">
        ${destTable}
    </sql>

    <insert id="batchInsertCandleNoUpdate">
        INSERT INTO
        <include refid="tableNameSql" />
        (id,
        time_range,
        time_type,
        timestamp,
        code,
        high_price,
        low_price,
        open_price,
        close_price,
        volume,
        turnover)
        VALUES
        <foreach collection="candleList" item="item" index="index" separator=",">
            (#{item.id},
            #{item.timeRange},
            #{item.timeType},
            #{item.timestamp},
            #{item.code},
            #{item.highPrice},
            #{item.lowPrice},
            #{item.openPrice},
            #{item.closePrice},
            #{item.volume},
            #{item.turnover})
        </foreach>
        ON DUPLICATE KEY UPDATE id = id
    </insert>

</mapper>