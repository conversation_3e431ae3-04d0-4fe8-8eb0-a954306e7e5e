<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.candle.CandleTableMapper">

    <sql id="createTableSql">
        CREATE TABLE IF NOT EXISTS ${table}
        (
            `id`          BIGINT UNSIGNED  NOT NULL COMMENT '主键',
            `time_range`  TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '时间范围',
            `time_type`   TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '时间范围类型 1分钟 2小时 3日 4月',
            `timestamp`   BIGINT UNSIGNED  NOT NULL COMMENT '时间戳',
            `code`        VARCHAR(20)      NOT NULL COMMENT '交易对编码',
            `high_price`  DECIMAL(20, 8)   NOT NULL DEFAULT '0.0000000' COMMENT '最高价格',
            `low_price`   DECIMAL(20, 8)   NOT NULL DEFAULT '0.0000000' COMMENT '最低价格',
            `open_price`  DECIMAL(20, 8)   NOT NULL DEFAULT '0.0000000' COMMENT '开盘价格',
            `close_price` DECIMAL(20, 8)   NOT NULL DEFAULT '0.0000000' COMMENT '收盘价格',
            `volume`      DECIMAL(32, 4)   NOT NULL DEFAULT '0.0000000' COMMENT '成交量',
            `turnover`    DECIMAL(32, 4)   NOT NULL DEFAULT '0.0000000' COMMENT '成交额',
            `create_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `is_market_close` BIT(1)       NOT NULL DEFAULT false COMMENT '是否是休市数据',
            PRIMARY KEY (`time_range`, `time_type`, `timestamp`)
        ) CHARACTER SET = utf8mb4
          COLLATE = utf8mb4_unicode_ci COMMENT '${comment}';
    </sql>

    <update id="createTable" parameterType="java.lang.String">
        <include refid="createTableSql">
            <property name="table" value="${tableName}"/>
            <property name="comment" value="${comment}"/>
        </include>
    </update>
</mapper>