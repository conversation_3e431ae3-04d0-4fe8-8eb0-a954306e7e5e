<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.controlplan.ControlPricePointMapper">

    <insert id="batchInsertPricePoints">
        INSERT INTO
        candle_control_price_point
        (
        plan_id,
        trade_pair_code,
        refer_price,
        price_diff,
        volume,
        timestamp
        )
        VALUES
        <foreach collection="priceList" item="item" index="index" separator=",">
            (
            #{item.planId},
            #{item.tradePairCode},
            #{item.referPrice},
            #{item.priceDiff},
            #{item.volume},
            #{item.timestamp}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        plan_id=VALUES(plan_id),
        trade_pair_code=VALUES(trade_pair_code),
        refer_price=VALUES(refer_price),
        price_diff=VALUES(price_diff),
        volume=VALUES(volume),
        timestamp=VALUES(timestamp)
    </insert>

</mapper>