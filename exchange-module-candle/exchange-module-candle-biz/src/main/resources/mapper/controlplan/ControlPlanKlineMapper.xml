<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.controlplan.ControlPlanKlineMapper">

    <insert id="batchInsertPlanKLines">
        INSERT INTO
        candle_control_plan_kline
        (
        plan_id,
        code,
        refer_price,
        time_range,
        time_type,
        timestamp,
        high_price,
        low_price,
        open_price,
        close_price,
        volume,
        turnover
        )
        VALUES
        <foreach collection="planKLines" item="item" index="index" separator=",">
            (
            #{item.planId},
            #{item.code},
            #{item.referPrice},
            #{item.timeRange},
            #{item.timeType},
            #{item.timestamp},
            #{item.highPrice},
            #{item.lowPrice},
            #{item.openPrice},
            #{item.closePrice},
            #{item.volume},
            #{item.turnover}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        plan_id=VALUES(plan_id),
        code=VALUES(code),
        refer_price=VALUES(refer_price),
        time_range=VALUES(time_range),
        time_type=VALUES(time_type),
        timestamp=VALUES(timestamp),
        high_price=VALUES(high_price),
        low_price=VALUES(low_price),
        open_price=VALUES(open_price),
        close_price=VALUES(close_price),
        volume=VALUES(volume),
        turnover=VALUES(turnover)
    </insert>

</mapper>