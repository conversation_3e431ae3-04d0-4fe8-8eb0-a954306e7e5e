<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.candle.dal.mysql.controlplan.ControlCandleMapper">

    <insert id="batchInsertControlCandles">
        INSERT INTO
        data_control_candle
        (
        id,
        time_range,
        time_type,
        timestamp,
        code,
        high_price,
        low_price,
        open_price,
        close_price,
        volume,
        turnover
        )
        VALUES
        <foreach collection="controlCandles" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.timeRange},
            #{item.timeType},
            #{item.timestamp},
            #{item.code},
            #{item.highPrice},
            #{item.lowPrice},
            #{item.openPrice},
            #{item.closePrice},
            #{item.volume},
            #{item.turnover}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        time_range=values(time_range),
        time_type=values(time_type),
        timestamp=VALUES(timestamp),
        code=VALUES(code),
        high_price=VALUES(high_price),
        low_price=VALUES(low_price),
        open_price=VALUES(open_price),
        close_price=VALUES(close_price),
        volume=VALUES(volume),
        turnover=VALUES(turnover)
    </insert>

</mapper>