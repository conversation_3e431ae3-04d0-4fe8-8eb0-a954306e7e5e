<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper">
    <!-- 查询总成员信息 -->
    <select id="totalMemberByAgentId" resultType="com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO">
            SELECT count(1) AS id,
            sum(usdt_balance) AS usdtBalance,
            sum(recharge) AS recharge,
            sum(withdraw) AS withdraw
            FROM member_user
            WHERE agent_id IN
            <foreach item="item" index="index" collection="agentIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <!-- 查询新成员ID列表 -->
    <select id="getNewMemberByRange" resultType="java.lang.Long">
            SELECT id
            FROM member_user
            WHERE agent_id IN
            <foreach item="item" index="index" collection="agentIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND create_time &gt;= #{startTime}
            AND create_time &lt;= #{endTime}
    </select>


</mapper>
