<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rf.exchange.module.member.dal.mysql.fundsrecord.MemberFundsRecordMapper">
    <!-- 查询用户ID和金额列表 -->
    <select id="getUserIdAndAmountListByRangeAndAgentId" resultType="com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO">
        SELECT a.user_id AS userId,
        a.usdt_amount AS usdtAmount,
        a.op_type AS opType
        FROM (SELECT * FROM member_funds_record
        WHERE create_time &gt;= #{startTime}
        AND create_time &lt;= #{endTime}) a
        INNER JOIN (SELECT * FROM member_user
        WHERE agent_id IN
        <foreach item="item" index="index" collection="agentIdList" open="(" separator="," close=")">
            #{item}
        </foreach>) b
        ON a.user_id = b.id
    </select>
</mapper>
