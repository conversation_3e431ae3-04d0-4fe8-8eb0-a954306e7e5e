package com.rf.exchange.module.member.dal.dataobject.wallet;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员钱包 DO
 *
 * <AUTHOR>
 */
@TableName("member_wallet")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberWalletDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 类型，虚拟，银行
     */
    private Integer type;
    /**
     * 类型名称，如银行名，或者链名
     */
    private String typeName;
    /**
     * 名称，如户名
     */
    private String name;
    /**
     * 账号，如银行卡号，或者钱包地址
     */
    private String account;
    /**
     * 银行地址
     */
    private String bankAddress;
    /**
     * 支行
     */
    private String bankBranch;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;
    /**
     * 地区id
     */
    private Integer areaId;
    /**
     * 地区名称
     */
    private String areaName;
    /**
     * 货币id
     */
    private Long currencyId;
    /**
     * 货币编码
     */
    private String currencyCode;
    /**
     * 货币名称
     */
    private String currencyName;

    private Long agentId;
    private String agentName;
}