package com.rf.exchange.module.member.controller.app.auth.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.framework.common.validation.Mobile;
import com.rf.exchange.module.system.enums.sms.SmsSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Schema(description = "用户 APP - 发送手机验证码 Request VO")
@Data
public class AppSmsCodeSendReqVO {

    @Schema(description = "手机号", example = "15601691234", requiredMode = Schema.RequiredMode.REQUIRED)
    @Mobile(message = "{MOBILE_FORMATTER_ERROR}")
    private String mobile;

    @Schema(description = "发送场景,1注册，2登陆，3修改手机，4修改密码，5重置密码", example = "1")
    @InEnum(value = SmsSceneEnum.class,message = "{SMS_SCENE_ERROR}")
    private Integer scene;
}
