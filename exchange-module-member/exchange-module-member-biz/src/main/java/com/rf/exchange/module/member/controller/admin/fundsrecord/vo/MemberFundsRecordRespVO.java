package com.rf.exchange.module.member.controller.admin.fundsrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员资金记录，如充值提现 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberFundsRecordRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2966")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "类型：1充值，2提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("类型：1充值，2提现")
    private Integer opType;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10723")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "币种", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种")
    private String currencyCode;

    @Schema(description = "币种金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种金额")
    private BigDecimal currencyAmount;

    @Schema(description = "币种汇率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种汇率")
    private BigDecimal currencyRate;

    @Schema(description = "usdt金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("usdt金额")
    private BigDecimal usdtAmount;

    @Schema(description = "提现手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("提现手续费")
    private BigDecimal feeAmount;

    @Schema(description = "实际到账")
    public BigDecimal getPayAmount(){
        return usdtAmount.subtract(feeAmount);
    }

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式")
    private Integer payMethod;

    @Schema(description = "会员订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("会员订单状态")
    private Integer orderMemberStatus;

    @Schema(description = "系统订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("系统订单状态")
    private Integer orderSystemStatus;

    @Schema(description = "会员备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("会员备注")
    private String memberRemark;

    @Schema(description = "系统备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("系统备注")
    private String systemRemark;

    @Schema(description = "冻结的id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27584")
    @ExcelProperty("冻结的id")
    private Long frozenId;

    @Schema(description = "钱包类型，加密货币，银行", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("钱包类型，加密货币，银行")
    private Integer walletType;

    @Schema(description = "钱包名，链名，银行名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("钱包名，链名，银行名")
    private String walletTypeName;

    @Schema(description = "银行户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("银行户名")
    private String walletName;

    @Schema(description = "银行账号/钱包哈希地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "6717")
    @ExcelProperty("银行账号/钱包哈希地址")
    private String walletAccount;

    @Schema(description = "银行地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行地址")
    private String walletBankAddress;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("地区")
    private String area;

    @Schema(description = "银行支行", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行支行")
    private String walletBankBranch;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理人")
    private String handler;

    @Schema(description = "处理时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理时间")
    private Long handleTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long agentId;

    @Schema(description = "代理名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentName;

    @Schema(description = "代理id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long agentParentId;

    @Schema(description = "上级代理名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentParentName;
}