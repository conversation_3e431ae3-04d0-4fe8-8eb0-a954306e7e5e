package com.rf.exchange.module.member.dal.dataobject.withdraw;

import lombok.*;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员提现 DO
 *
 * <AUTHOR>
 */
@TableName("member_withdraw")
@KeySequence("member_withdraw_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberWithdrawDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 货币id
     */
    private Long currencyId;
    /**
     * 货币名称
     */
    private String currencyName;
    /**
     * 币种汇率
     */
    private BigDecimal currencyRate;
    /**
     * 币种金额
     */
    private BigDecimal currencyAmount;
    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;
    /**
     * 操作金额
     */
    private BigDecimal deductionAmount;
    /**
     * 转账金额
     */
    private BigDecimal transferAmount;
    /**
     * 钱包id
     */
    private Long walletId;
    /**
     * 钱包类型
     */
    private Integer walletType;
    /**
     * 钱包类型名
     */
    private String walletTypeName;
    /**
     * 钱包名
     */
    private String walletName;
    /**
     * 钱包类型
     */
    private String walletAccount;
    /**
     * 银行地址
     */
    private String bankAddress;
    /**
     * 分支银行
     */
    private String bankBranch;
    /**
     * 会员备注
     */
    private String memberRemark;
    /**
     * 系统备注
     */
    private String systemRemark;
    /**
     * 订单会员状态
     */
    private Integer orderMemberStatus;
    /**
     * 订单系统状态
     */
    private Integer orderSystemStatus;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理时间
     */
    private Long handleTime;

    /**
     * 冻结id
     */
    private Long frozenId;

    /**
     * 提现手续费
     */
    private BigDecimal feeAmount;

    private Long agentId;
    private String agentName;
}