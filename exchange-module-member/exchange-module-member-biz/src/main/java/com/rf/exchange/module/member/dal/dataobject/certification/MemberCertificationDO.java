package com.rf.exchange.module.member.dal.dataobject.certification;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 会员身份认证 DO
 *
 * <AUTHOR>
 */
@TableName("member_certification")
@KeySequence("member_certification_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberCertificationDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户
     */
    private Long userId;
    /**
     * 账号
     */
    private String username;
    /**
     * 地区
     */
    private Integer areaId;
    /**
     * 地区
     */
    private String areaName;
    /**
     * 真名
     */
    private String realName;

    /**
     * 证件类型
     */
    private Integer credentialsType;
    /**
     * 证件号码
     */

    private String credentialsCode;
    /**
     * 证件正面
     */
    @ImageUrl
    private String credentialsFront;
    /**
     * 证件反面
     */
    @ImageUrl
    private String credentialsBack;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理时间
     */
    private Long handleTime;
    /**
     * 处理备注
     */
    private String handleRemark;

    private Long agentId;
    private String agentName;
    private Long tenantId;
}