package com.rf.exchange.module.member.controller.admin.transactions.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;


@Schema(description = "管理后台 - 会员账变分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberTransactionsPageReqVO extends PageParam {

    @Schema(description = "会员id", example = "21099")
    private Long userId;

    @Schema(description = "会员名称", example = "赵六")
    private String username;

    @Schema(description = "发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约", example = "2")
    private Integer type;

    @Schema(description = "业务订单号")
    private String bizOrderNo;

    /**
     * 管理后台发送请求的时候请求的是这个字段
     */
    @Schema(description = "订单号")
    private String tradeNo;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    private Long[] createTime;

}