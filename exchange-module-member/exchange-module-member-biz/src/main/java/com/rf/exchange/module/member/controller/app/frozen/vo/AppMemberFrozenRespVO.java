package com.rf.exchange.module.member.controller.app.frozen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 提现列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberFrozenRespVO {
    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;
    @Schema(description = "冻结原因")
    private String frozenReason;

}
