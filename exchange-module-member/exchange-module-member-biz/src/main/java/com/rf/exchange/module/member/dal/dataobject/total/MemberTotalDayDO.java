package com.rf.exchange.module.member.dal.dataobject.total;

import lombok.*;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员日统计数据 DO
 *
 * <AUTHOR>
 */
@TableName("member_day_total")
@KeySequence("member_day_total_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberTotalDayDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 代理
     */
    private Long agentId;
    /**
     * 代理
     */
    private String agentName;
    /**
     * 会员
     */
    private Long userId;
    /**
     * 会员
     */
    private String username;
    /**
     * 归属时间
     */
    private Long totalTime;
    /**
     * 余额
     */
    private BigDecimal usdtBalance;
    /**
     * 盈利
     */
    private BigDecimal profitAmount;
    /**
     * 单数
     */
    private Integer orderCount;

    private BigDecimal recharge;
    private BigDecimal withdraw;
}