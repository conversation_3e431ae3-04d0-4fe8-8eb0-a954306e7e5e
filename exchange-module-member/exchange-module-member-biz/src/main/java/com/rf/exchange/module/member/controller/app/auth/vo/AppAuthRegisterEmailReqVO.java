//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.Email;
//import jakarta.validation.constraints.NotEmpty;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
///**
// * 邮箱注册 请求
// *
// * <AUTHOR>
// * @since 2024-06-07
// */
//@Schema(description = "用户 APP - 邮箱注册 Request VO")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthRegisterEmailReqVO extends AppAuthRegisterVO {
//
//    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
//    @NotEmpty(message = "邮箱不能为空")
//    @Email
//    private String email;
//
//    @Schema(description = "验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    @NotEmpty(message = "验证码不能为空")
//    private String verifyCode;
//}
