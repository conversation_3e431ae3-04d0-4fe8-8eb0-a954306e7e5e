package com.rf.exchange.module.member.controller.admin.fundsrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

@Schema(description = "管理后台 - 会员资金记录，如充值提现新增/修改 Request VO")
@Data
public class MemberFundsRecordSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2966")
    private Long id;

    @Schema(description = "地区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2966")
    @Positive(message = "地区id不能为空")
    private Integer areaId;

    @Schema(description = "系统备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    private String systemRemark;

    @Schema(description = "钱包类型，加密货币，银行", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "钱包类型，加密货币，银行不能为空")
    private Integer walletType;

    @Schema(description = "钱包名，链名，银行名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "钱包名，链名，银行名不能为空")
    private String walletTypeName;

    @Schema(description = "银行户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "银行户名不能为空")
    private String walletName;

    @Schema(description = "银行账号/钱包哈希地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "6717")
    @NotEmpty(message = "银行账号/钱包哈希地址不能为空")
    private String walletAccount;

    @Schema(description = "银行地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String walletBankAddress;

    @Schema(description = "银行支行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String walletBankBranch;


}