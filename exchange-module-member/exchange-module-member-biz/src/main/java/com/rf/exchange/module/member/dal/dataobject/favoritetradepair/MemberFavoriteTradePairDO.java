package com.rf.exchange.module.member.dal.dataobject.favoritetradepair;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员收藏交易对 DO
 *
 * <AUTHOR>
 */
@TableName("member_favorite_trade_pair")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberFavoriteTradePairDO extends BaseDO {
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 交易对code
     */
    private String tradePairCode;
    /**
     * 交易对名称
     */
    private String tradePairName;

    private Long agentId;
    private String agentName;
}