package com.rf.exchange.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import java.math.BigDecimal;

/**
 * 会员用户 Base VO，提供给添加、修改、详细的子 VO 使用 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class MemberUserBaseVO {
    @Schema(description = "代理ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    private Long agentId;
    @Schema(description = "代理名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    private String agentName;

    @Schema(description = "登陸賬號，account，email,mobile三列中的一個", requiredMode = Schema.RequiredMode.REQUIRED,
        example = "***********")
    private String username;
    @Schema(description = "賬號", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "test123")
    private String account;
    @Schema(description = "郵箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "<EMAIL>")
    private String email;
    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "***********")
    private String mobile;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态不能为空")
    private Byte status;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotNull(message = "用户昵称不能为空")
    private String nickname;

    @Schema(description = "头像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.xxx.xx/x.png")
    @URL(message = "头像必须是 URL 格式")
    private String avatar;

    @Schema(description = "用户昵称", example = "李四")
    private String name;

    @Schema(description = "用户性别", example = "1")
    private Integer sex;

    @Schema(description = "所在地编号", example = "4371")
    private Long areaId;

    @Schema(description = "所在地全程", example = "上海上海市普陀区")
    private String areaName;

    @Schema(description = "出生日期,unix时间戳", example = "1087606800")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Long birthday;

    @Schema(description = "会员备注", example = "我是小备注")
    private String remark;
    @Schema(description = "会员等级ID", example = "1")
    private Long levelId;
    @Schema(description = "会员等级", example = "VIP1")
    private String levelName;
    @Schema(description = "余额", example = "5")
    private BigDecimal usdtBalance;
    @Schema(description = "冻结", example = "3")
    private BigDecimal usdtFrozenBalance;
    @Schema(description = "总充值", example = "1")
    private BigDecimal recharge;
    @Schema(description = "总提现", example = "3")
    private BigDecimal withdraw;
    @Schema(description = "试玩", example = "true")
    private Boolean demo;
    // @Schema(description = "会员标签", example = "[1, 2]")
    // private List<Long> tagIds;
    //
    // @Schema(description = "会员等级编号", example = "1")
    // private Long levelId;
    //
    // @Schema(description = "用户分组编号", example = "1")
    // private Long groupId;
    @Schema(description = "信用分-默认100", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer creditScore;

}
