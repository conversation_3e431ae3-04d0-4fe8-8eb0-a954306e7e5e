package com.rf.exchange.module.member.service.balance;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceDTO;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
import com.rf.exchange.module.member.dal.redis.MemberBalanceRedisDAO;
import com.rf.exchange.module.member.enums.balance.MemberBalanceUpdateEnum;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.member.service.transactions.MemberTransactionsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.VALUE_ERROR;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class MemberBalanceServiceImpl implements MemberBalanceService {

    @Resource
    private MemberTransactionsService transactionsService;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private MemberBalanceRedisDAO memberBalanceRedisDAO;

    @Override
    public MemberBalanceDTO getMemberBalanceCached(Long userId) {
        MemberBalanceDTO balanceDTO = memberBalanceRedisDAO.getBalance(userId);
        if (null != balanceDTO) {
            return balanceDTO;
        }
        LambdaQueryWrapperX<MemberUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(MemberUserDO::getId, MemberUserDO::getUsername, MemberUserDO::getUsdtBalance, MemberUserDO::getUsdtFrozenBalance);
        wrapper.eqIfPresent(MemberUserDO::getId, userId);
        wrapper.last("limit 1");

        final MemberUserDO userDO = memberUserMapper.selectOne(wrapper);
        if (null == userDO) {
            throw exception(USER_BALANCE_ERROR);
        }
        return updateBalanceCached(userDO);
    }

    @Override
    @Lock4j(keys = "#userId", acquireTimeout = 1000, expire = 2000) // 过期时间为2秒,如果业务执行的时间超过3秒则需要延长
    public void checkUSDTBalanceEnough(Long userId, BigDecimal changeAmount) {
        if (userId == null) {
            throw exception(VALUE_ERROR);
        }
        if (BigDecimal.ZERO.compareTo(changeAmount) == 0) {
            return;
        }
        BigDecimal usdtBalance = memberUserMapper.getBalance(userId);
        if (usdtBalance == null || usdtBalance.compareTo(changeAmount.abs()) < 0) {
            throw exception(USER_BALANCE_NOT_ENOUGH);
        }
    }

    @Override
    @Lock4j(keys = "#userId", acquireTimeout = 1000, expire = 2000) // 过期时间为2秒,如果业务执行的时间超过3秒则需要延长
    public void checkUSDTFrozenBalanceEnough(Long userId, BigDecimal changeAmount) {
        if (userId == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (BigDecimal.ZERO.compareTo(changeAmount) == 0) {
            return;
        }
        BigDecimal usdtBalance = memberUserMapper.getFrozenBalance(userId);
        if (usdtBalance == null || usdtBalance.compareTo(changeAmount.abs()) < 0) {
            throw exception(USER_FROZEN_BALANCE_NOT_ENOUGH);
        }
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(keys = "#context.memberUserDTO.id", acquireTimeout = 1000, expire = 2000) // 过期时间为2秒,如果业务执行的时间超过3秒则需要延长
    public void incrUserBalance(MemberBalanceUpdateContext context) {
        if (context == null || context.getBalanceReqVO() == null) {
            return;
        }
        MemberBalanceUpdateReqVO balanceReqVO = context.getBalanceReqVO();
        if (balanceReqVO.getAmount() == null) {
            throw exception(VALUE_ERROR);
        }
        MemberUserRespDTO memberUserDTO = context.getMemberUserDTO();
        if (memberUserDTO == null) {
            throw exception(VALUE_ERROR);
        }
        LambdaQueryWrapperX<MemberUserDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(MemberUserDO::getId, memberUserDTO.getId()).last("for update");
        MemberUserDO userDO = memberUserMapper.selectOne(queryWrapperX);

        BigDecimal changeAmount = balanceReqVO.getAmount().abs();
        // 变更前的余额
        BigDecimal beforeBalance = userDO.getUsdtBalance();
        BigDecimal afterBalance = beforeBalance.add(changeAmount);
        userDO.setUsdtBalance(afterBalance);

        // 如果需要修改总充值
        if (balanceReqVO.isIncrRecharge()) {
            userDO.setRecharge(userDO.getRecharge().add(changeAmount));
        }

        // 插入余额的帐变记录
        MemberTransactionSaveReqVO transactionRequestVO = context.getTransactionReqVO();
        if (transactionRequestVO != null) {
            transactionRequestVO.setUserId(userDO.getId());
            transactionRequestVO.setTenantId(memberUserDTO.getTenantId());
            transactionRequestVO.setUsername(userDO.getUsername());
            transactionRequestVO.setAmount(changeAmount);
            // 因为用户的余额只有USDT这种法币，所以这里固定写死USDT
            transactionRequestVO.setAmountCurrency("USDT");
            transactionRequestVO.setBeforeBalance(beforeBalance);
            transactionRequestVO.setAfterBalance(afterBalance);
            transactionsService.saveTransaction(transactionRequestVO);
        }

        // 如果需要修改冻结金额
        if (balanceReqVO.getFrozenUpdateEnum() != null) {
            BigDecimal beforeFrozenBalance = userDO.getUsdtFrozenBalance();
            BigDecimal frozenChangeAmount = changeAmount.abs().multiply(BigDecimal.valueOf(balanceReqVO.getFrozenUpdateEnum().getType()));
            BigDecimal afterFrozenBalance = beforeFrozenBalance.add(frozenChangeAmount);
            // 更新用户的冻结余额
            userDO.setUsdtFrozenBalance(afterFrozenBalance);

            // 如果冻结金额的值有变化则插入一条冻结金额的帐变记录
            if (!MemberBalanceUpdateEnum.SAME.equals(balanceReqVO.getFrozenUpdateEnum())) {
                MemberTransactionSaveReqVO frozenTransactionReqVO = new MemberTransactionSaveReqVO();
                frozenTransactionReqVO.setBizOrderNo(balanceReqVO.getRelatedOrderNo());
                frozenTransactionReqVO.setUserId(userDO.getId());
                frozenTransactionReqVO.setUsername(userDO.getUsername());
                frozenTransactionReqVO.setTenantId(memberUserDTO.getTenantId());
                frozenTransactionReqVO.setAmount(frozenChangeAmount);
                frozenTransactionReqVO.setAmountCurrency("USDT");
                frozenTransactionReqVO.setBeforeBalance(beforeFrozenBalance);
                frozenTransactionReqVO.setAfterBalance(afterFrozenBalance);
                frozenTransactionReqVO.setRemark(balanceReqVO.getFrozenUpdateMark());
                if (MemberBalanceUpdateEnum.INCREASE.equals(balanceReqVO.getFrozenUpdateEnum())) {
                    frozenTransactionReqVO.setTransactionType(MemberTransactionsTypeEnum.FROZEN_INCR);
                } else {
                    frozenTransactionReqVO.setTransactionType(MemberTransactionsTypeEnum.FROZEN_DECR);
                }
                transactionsService.saveTransaction(frozenTransactionReqVO);
            }
        }

        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userDO.getId());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getUsdtBalance()), MemberUserDO::getUsdtBalance, userDO.getUsdtBalance());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getUsdtFrozenBalance()), MemberUserDO::getUsdtFrozenBalance, userDO.getUsdtFrozenBalance());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getRecharge()), MemberUserDO::getRecharge, userDO.getRecharge());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getWithdraw()), MemberUserDO::getWithdraw, userDO.getWithdraw());
        memberUserMapper.update(updateWrapper);

        // 更新用户的余额缓存
        updateBalanceCached(userDO);
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(keys = "#context.memberUserDTO.id", acquireTimeout = 1000, expire = 2000) // 过期时间为2秒,如果业务执行的时间超过3秒则需要延长
    public void decrUserBalance(MemberBalanceUpdateContext context) {
        if (context == null || context.getBalanceReqVO() == null) {
            return;
        }
        MemberBalanceUpdateReqVO balanceReqVO = context.getBalanceReqVO();
        if (balanceReqVO.getAmount() == null) {
            throw exception(VALUE_ERROR);
        }
        MemberUserRespDTO memberUserDTO = context.getMemberUserDTO();
        if (memberUserDTO == null || memberUserDTO.getUsdtBalance() == null || memberUserDTO.getUsdtFrozenBalance() == null) {
            throw exception(VALUE_ERROR);
        }
        LambdaQueryWrapperX<MemberUserDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(MemberUserDO::getId, memberUserDTO.getId()).last("for update");
        MemberUserDO userDO = memberUserMapper.selectOne(queryWrapperX);

        // 金额为负数
        BigDecimal changeAmount = balanceReqVO.getAmount().abs().multiply(BigDecimal.valueOf(-1));
        // 变更前的余额
        BigDecimal beforeBalance = userDO.getUsdtBalance();
        // getAmount的值可能为负数，所以如果afterBalance小于0则用户的USDT 余额不够，抛出异常
        BigDecimal afterBalance = userDO.getUsdtBalance().add(changeAmount);
        if (context.getBalanceReqVO().isFrozenOnly()) {
            beforeBalance = userDO.getUsdtFrozenBalance();
            afterBalance = beforeBalance.add(changeAmount);
        }
        if (BigDecimal.ZERO.compareTo(afterBalance) > 0) {
            if (balanceReqVO.isDecrToZeroIfLess()) {
                afterBalance = BigDecimal.ZERO;
            } else {
                throw exception(USER_BALANCE_NOT_ENOUGH);
            }
        }
        // 如果只处理冻结金额则不改动用户的usdt余额
        if (!balanceReqVO.isFrozenOnly()) {
            userDO.setUsdtBalance(afterBalance);
        }
        if (balanceReqVO.isIncrWithdraw()) {
            userDO.setWithdraw(userDO.getWithdraw());
        }
        // 插入余额的帐变记录
        MemberTransactionSaveReqVO transactionRequestVO = context.getTransactionReqVO();
        if (transactionRequestVO != null) {
            transactionRequestVO.setTenantId(memberUserDTO.getTenantId());
            transactionRequestVO.setUserId(memberUserDTO.getId());
            transactionRequestVO.setUsername(memberUserDTO.getUsername());
            transactionRequestVO.setAmount(changeAmount);
            // 因为用户的余额只有USDT这种法币，所以这里固定写死USDT
            transactionRequestVO.setAmountCurrency("USDT");
            transactionRequestVO.setBeforeBalance(beforeBalance);
            transactionRequestVO.setAfterBalance(afterBalance);
            transactionsService.saveTransaction(transactionRequestVO);
        }
        // 如果只处理冻结余额或者需要同时处理冻结余额
        if (balanceReqVO.isFrozenOnly() || balanceReqVO.getFrozenUpdateEnum() != null) {
            BigDecimal beforeFrozenBalance = userDO.getUsdtFrozenBalance();
            BigDecimal frozenChangeAmount = changeAmount.abs().multiply(BigDecimal.valueOf(balanceReqVO.getFrozenUpdateEnum().getType()));
            if (frozenChangeAmount.compareTo(BigDecimal.ZERO) < 0 && beforeFrozenBalance.compareTo(frozenChangeAmount) < 0) {
                throw exception(USER_FROZEN_BALANCE_NOT_ENOUGH);
            }
            BigDecimal afterFrozenBalance = beforeFrozenBalance.add(frozenChangeAmount).max(BigDecimal.ZERO);
            // 更新用户的冻结余额
            userDO.setUsdtFrozenBalance(afterFrozenBalance);

            // 如果冻结金额的值有变化则插入一条冻结金额的帐变记录
            if (!MemberBalanceUpdateEnum.SAME.equals(balanceReqVO.getFrozenUpdateEnum())) {
                MemberTransactionSaveReqVO frozenTransactionReqVO = new MemberTransactionSaveReqVO();
                frozenTransactionReqVO.setBizOrderNo(balanceReqVO.getRelatedOrderNo());
                frozenTransactionReqVO.setUserId(userDO.getId());
                frozenTransactionReqVO.setUsername(userDO.getUsername());
                frozenTransactionReqVO.setTenantId(memberUserDTO.getTenantId());
                frozenTransactionReqVO.setAmount(frozenChangeAmount);
                frozenTransactionReqVO.setAmountCurrency("USDT");
                frozenTransactionReqVO.setBeforeBalance(beforeFrozenBalance);
                frozenTransactionReqVO.setAfterBalance(afterFrozenBalance);
                frozenTransactionReqVO.setRemark(balanceReqVO.getFrozenUpdateMark());
                if (MemberBalanceUpdateEnum.INCREASE.equals(balanceReqVO.getFrozenUpdateEnum())) {
                    frozenTransactionReqVO.setTransactionType(MemberTransactionsTypeEnum.FROZEN_INCR);
                } else {
                    frozenTransactionReqVO.setTransactionType(MemberTransactionsTypeEnum.FROZEN_DECR);
                }
                transactionsService.saveTransaction(frozenTransactionReqVO);
            }
        }

        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userDO.getId());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getUsdtBalance()), MemberUserDO::getUsdtBalance, userDO.getUsdtBalance());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getUsdtFrozenBalance()), MemberUserDO::getUsdtFrozenBalance, userDO.getUsdtFrozenBalance());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getRecharge()), MemberUserDO::getRecharge, userDO.getRecharge());
        updateWrapper.set(ObjectUtil.isNotNull(userDO.getWithdraw()), MemberUserDO::getWithdraw, userDO.getWithdraw());
        memberUserMapper.update(updateWrapper);

        // 更新用户的余额缓存
        updateBalanceCached(userDO);
    }

    private MemberBalanceDTO updateBalanceCached(MemberUserDO userDO) {
        MemberBalanceDTO balanceDTO = new MemberBalanceDTO();
        balanceDTO.setUserId(userDO.getId());
        balanceDTO.setUsername(userDO.getUsername());
        balanceDTO.setUsdtBalance(userDO.getUsdtBalance());
        balanceDTO.setUsdtFrozenBalance(userDO.getUsdtFrozenBalance());
        memberBalanceRedisDAO.updateBalance(balanceDTO);
        return balanceDTO;
    }
}
