package com.rf.exchange.module.member.controller.admin.transactions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员账变 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberTransactionsRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12195")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21099")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("会员名称")
    private String username;

    @Schema(description = "发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约")
    private Integer type;

    @Schema(description = "发生金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生金额")
    private BigDecimal changeAmount;

    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("货币名称")
    private String currencyCode;

    @Schema(description = "发生前余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生前余额")
    private BigDecimal beforeBalance;

    @Schema(description = "发生后余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生后余额")
    private BigDecimal afterBalance;

    @Schema(description = "业务订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务订单号")
    private String bizOrderNo;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}