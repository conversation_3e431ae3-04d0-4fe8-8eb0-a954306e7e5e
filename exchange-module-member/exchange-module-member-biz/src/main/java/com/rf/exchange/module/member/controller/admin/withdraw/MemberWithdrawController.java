//package com.rf.exchange.module.member.controller.admin.withdraw;
//
//import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.rf.exchange.framework.common.pojo.PageParam;
//import com.rf.exchange.framework.common.pojo.PageResult;
//import com.rf.exchange.framework.common.pojo.CommonResult;
//import com.rf.exchange.framework.common.util.object.BeanUtils;
//import static com.rf.exchange.framework.common.pojo.CommonResult.success;
//
//import com.rf.exchange.framework.excel.core.util.ExcelUtils;
//
//import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
//import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.rf.exchange.module.member.controller.admin.withdraw.vo.*;
//import com.rf.exchange.module.member.dal.dataobject.withdraw.MemberWithdrawDO;
//import com.rf.exchange.module.member.service.withdraw.MemberWithdrawService;
//
//@Tag(name = "管理后台 - 会员提现")
//@RestController
//@RequestMapping("/member/withdraw")
//@Validated
//public class MemberWithdrawController {
//
//    @Resource
//    private MemberWithdrawService withdrawService;
//
//    @GetMapping("/get")
//    @Operation(summary = "获得会员提现")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('member:withdraw:query')")
//    public CommonResult<MemberWithdrawRespVO> getWithdraw(@RequestParam("id") Long id) {
//        MemberWithdrawDO withdraw = withdrawService.getWithdraw(id);
//        return success(BeanUtils.toBean(withdraw, MemberWithdrawRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得会员提现分页")
//    @PreAuthorize("@ss.hasPermission('member:withdraw:query')")
//    public CommonResult<PageResult<MemberWithdrawRespVO>> getWithdrawPage(@Valid MemberWithdrawPageReqVO pageReqVO) {
//        PageResult<MemberWithdrawDO> pageResult = withdrawService.getWithdrawPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, MemberWithdrawRespVO.class));
//    }
//
//    @PostMapping("/update")
//    @Operation(summary = "修改会员提现")
//    @PreAuthorize("@ss.hasPermission('member:withdraw:update')")
//    public CommonResult<Boolean> updateWithdraw(@RequestBody @Valid MemberWithdrawSaveReqVO reqVO){
//        withdrawService.updateWithdraw(reqVO);
//        return success("修改成功");
//    }
//
//    @PostMapping("/handle")
//    @Operation(summary = "处理会员提现")
//    @PreAuthorize("@ss.hasPermission('member:withdraw:handle')")
//    public CommonResult<Boolean> handleWithdraw(@RequestBody @Valid MemberFundsRecordWithdrawHandleReqVO reqVO){
//        withdrawService.handleWithdraw(SecurityFrameworkUtils.getLoginUsername(),reqVO);
//        return success(true);
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出会员提现 Excel")
//    @PreAuthorize("@ss.hasPermission('member:withdraw:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportWithdrawExcel(@Valid MemberWithdrawPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<MemberWithdrawDO> list = withdrawService.getWithdrawPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "会员提现.xls", "数据", MemberWithdrawRespVO.class,
//                        BeanUtils.toBean(list, MemberWithdrawRespVO.class));
//    }
//
//}