package com.rf.exchange.module.member.controller.admin.fundsrecord.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员资金记录，如充值提现分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberFundsRecordPageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @JsonIgnore
    private Integer opType;

    @Schema(description = "会员id", example = "10723")
    private Long userId;

    @Schema(description = "会员账号", example = "赵六")
    private String username;

    @Schema(description = "币种")
    private String currencyCode;

    @Schema(description = "币种金额")
    private BigDecimal currencyAmount;

    @Schema(description = "币种汇率")
    private BigDecimal currencyRate;

    @Schema(description = "usdt金额")
    private BigDecimal usdtAmount;

    @Schema(description = "提现手续费")
    private BigDecimal feeAmount;

    @Schema(description = "支付方式")
    private Integer payMethod;

    @Schema(description = "会员订单状态", example = "1")
    private Integer orderMemberStatus;

    @Schema(description = "系统订单状态", example = "2")
    private Integer orderSystemStatus;

    @Schema(description = "会员备注", example = "随便")
    private String memberRemark;

    @Schema(description = "系统备注", example = "随便")
    private String systemRemark;

    @Schema(description = "冻结的id", example = "27584")
    private Long frozenId;

    @Schema(description = "钱包类型，加密货币，银行", example = "1")
    private Integer walletType;

    @Schema(description = "钱包名，链名，银行名", example = "芋艿")
    private String walletTypeName;

    @Schema(description = "银行户名", example = "李四")
    private String walletName;

    @Schema(description = "银行账号/钱包哈希地址", example = "6717")
    private String walletAccount;

    @Schema(description = "银行地址")
    private String walletBankAddress;

    @Schema(description = "银行支行")
    private String walletBankBranch;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] handleTime;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

    @Schema(description = "是否试玩用户")
    private Boolean isDemoUser = false;

}