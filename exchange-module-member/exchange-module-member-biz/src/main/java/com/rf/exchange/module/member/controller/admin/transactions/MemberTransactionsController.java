package com.rf.exchange.module.member.controller.admin.transactions;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.member.controller.admin.transactions.vo.MemberTransactionsPageReqVO;
import com.rf.exchange.module.member.controller.admin.transactions.vo.MemberTransactionsRespVO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.service.transactions.MemberTransactionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员账变")
@RestController
@RequestMapping("/member/transactions")
@Validated
public class MemberTransactionsController {

    @Resource
    private MemberTransactionsService transactionsService;

    @PostMapping("/page")
    @Operation(summary = "获得会员账变分页")
    @PreAuthorize("@ss.hasPermission('member:transactions:query')")
    public CommonResult<PageResult<MemberTransactionsRespVO>> getTransactionsPage(@Valid @RequestBody MemberTransactionsPageReqVO pageReqVO) {
        if (StrUtil.isNotEmpty(pageReqVO.getTradeNo())) {
            pageReqVO.setBizOrderNo(pageReqVO.getTradeNo());
        }
        PageResult<MemberTransactionsDO> pageResult = transactionsService.getTransactionsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberTransactionsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员账变 Excel")
    @PreAuthorize("@ss.hasPermission('member:transactions:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransactionsExcel(@Valid MemberTransactionsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberTransactionsDO> list = transactionsService.getTransactionsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员账变.xls", "数据", MemberTransactionsRespVO.class,
                        BeanUtils.toBean(list, MemberTransactionsRespVO.class));
    }

}