package com.rf.exchange.module.member.controller.app.auth.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.system.enums.sms.SmsSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import lombok.Data;

@Schema(description = "用户 APP - 发送手机验证码 Request VO")
@Data
public class AppMailCodeSendReqVO {

    @Schema(description = "邮箱", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
    @Email(message = "{MAIL_FORMATTER_ERROR}")
    private String email;

    @Schema(description = "发送场景, 1注册，2登陆，3修改邮箱，4修改密码，5重置密码", example = "1")
    @InEnum(value = SmsSceneEnum.class,message = "{MAIL_SCENE_ERROR}")
    private Integer scene;

    @Schema(description = "域名")
    private String domain;
}
