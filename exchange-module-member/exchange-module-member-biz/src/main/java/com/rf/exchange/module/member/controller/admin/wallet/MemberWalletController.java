package com.rf.exchange.module.member.controller.admin.wallet;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.member.controller.admin.wallet.vo.MemberWalletPageReqVO;
import com.rf.exchange.module.member.controller.admin.wallet.vo.MemberWalletRespVO;
import com.rf.exchange.module.member.controller.admin.wallet.vo.MemberWalletSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import com.rf.exchange.module.member.service.wallet.MemberWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员钱包")
@RestController
@RequestMapping("/member/wallet")
@Validated
public class MemberWalletController {

    @Resource
    private MemberWalletService walletService;


    @GetMapping("/get")
    @Operation(summary = "获得会员钱包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:wallet:query')")
    public CommonResult<MemberWalletRespVO> getWallet(@RequestParam("id") Long id) {
        MemberWalletDO wallet = walletService.getWallet(id);
        return success(BeanUtils.toBean(wallet, MemberWalletRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得会员钱包分页")
    @PreAuthorize("@ss.hasPermission('member:wallet:query')")
    public CommonResult<PageResult<MemberWalletRespVO>> getWalletPage(@Valid @RequestBody MemberWalletPageReqVO pageReqVO) {
        PageResult<MemberWalletDO> pageResult = walletService.getWalletPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberWalletRespVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员钱包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:wallet:delete')")
    public CommonResult<Boolean> deleteWallet(@RequestParam("id") Long id) {
        walletService.deleteWallet(id);
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "修改会员钱包")
    @PreAuthorize("@ss.hasPermission('member:wallet:update')")
    public CommonResult<Boolean> updateWallet(@Valid @RequestBody MemberWalletSaveReqVO reqVO) {
        walletService.updateWallet(reqVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员钱包 Excel")
    @PreAuthorize("@ss.hasPermission('member:wallet:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWalletExcel(@Valid MemberWalletPageReqVO pageReqVO,
                                  HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberWalletDO> list = walletService.getWalletPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员钱包.xls", "数据", MemberWalletRespVO.class,
                BeanUtils.toBean(list, MemberWalletRespVO.class));
    }

}