package com.rf.exchange.module.member.controller.admin.total.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员日统计数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberTotalDayPageReqVO extends PageParam {

    @Schema(description = "代理", example = "8969")
    private Long agentId;

    @Schema(description = "代理", example = "李四")
    private String agentName;

    @Schema(description = "会员", example = "9485")
    private Long userId;

    @Schema(description = "会员", example = "王五")
    private String username;

    @Schema(description = "归属时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] totalTime;

    @Schema(description = "余额")
    private BigDecimal usdtBalance;

    @Schema(description = "盈利")
    private BigDecimal profitAmount;

    @Schema(description = "单数", example = "4713")
    private Integer orderCount;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}