package com.rf.exchange.module.member.config;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 会员配置
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Validated
@Data
@ConfigurationProperties(prefix = "exchange.member")
@Component
public class MemberProperties {

    /**
     * 默认头像列表
     */
    @NotEmpty(message = "default-avatars没有配置")
    private List<String> defaultAvatars = Collections.emptyList();

    @Resource
    @NotNull(message = "default-level没有配置")
    private LevelConfig defaultLevel;

    @Data
    @Validated
    @ConfigurationProperties(prefix = "exchange.member.default-level")
    @Component
    public  class LevelConfig {
        private int level;
        private String name;
        private String icon;
    }
}

