package com.rf.exchange.module.member.service.fundsrecord;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.lock.annotation.Lock4j;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordRespVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordSaveReqVO;
import com.rf.exchange.module.member.controller.admin.wallet.vo.MemberWalletSaveReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRechargeCreateReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordWithdrawCreateReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordWithdrawWithWalletCreateReqVO;
import com.rf.exchange.module.member.convert.user.MemberUserConvert;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import com.rf.exchange.module.member.dal.mysql.fundsrecord.MemberFundsRecordMapper;
import com.rf.exchange.module.member.enums.MemberMsgConstants;
import com.rf.exchange.module.member.enums.balance.MemberBalanceUpdateEnum;
import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.member.enums.user.UserPwdWrongType;
import com.rf.exchange.module.member.mq.producer.user.MemberUserMQProducer;
import com.rf.exchange.module.member.service.balance.MemberBalanceService;
import com.rf.exchange.module.member.service.frozen.MemberFrozenService;
import com.rf.exchange.module.member.service.user.MemberUserService;
import com.rf.exchange.module.member.service.wallet.MemberWalletService;
import com.rf.exchange.module.system.api.agent.AgentApi;
import com.rf.exchange.module.system.api.agent.AgentStatisticApi;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import com.rf.exchange.module.system.api.currency.CurrencyApi;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantAuthConfigRespDTO;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantRechargeConfigRespDTo;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantWithdrawConfigRespDTo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_EXISTS;

/**
 * 会员资金记录，如充值提现 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class MemberFundsRecordServiceImpl implements MemberFundsRecordService {

    @Resource
    private MemberFundsRecordMapper memberFundsRecordMapper;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private CurrencyApi currencyApi;
    @Resource
    private CurrencyRateApi currencyRateApi;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private TenantDictDataApi tenantDictDataApi;
    @Resource
    private MemberWalletService memberWalletService;
    @Resource
    private MemberFrozenService memberFrozenService;
    @Resource
    private MemberBalanceService memberBalanceService;
    @Resource
    private MemberUserMQProducer memberUserMQProducer;
    @Resource
    private AgentStatisticApi agentStatisticApi;
    @Resource
    @Lazy
    private AgentApi agentApi;

    @Override
    @Master
    @DSTransactional
    public Long createFundsRecordRecharge(Long userId, AppMemberFundsRecordRechargeCreateReqVO createReqVO) {

        // 验证用户是否存在
        MemberUserDO userDO = memberUserService.checkUserExists(userId);
        if (!userDO.getDemo()) {
            // 判断是否需要认证
            TenantAuthConfigRespDTO authConfig = tenantDictDataApi.getTenantAuthConfig(userDO.getTenantId());
            if (authConfig.isRechargeNeedAuth()) {
                if (Objects.equals(userDO.getCertificationStatus(),
                    MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
                    throw exception(USER_CERTIFICATION_VERIFYING);
                }
                if (!Objects.equals(userDO.getCertificationStatus(),
                    MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
                    throw exception(USER_CERTIFICATION_NOT_VERIFY,
                        MemberCertificationStatusEnum.getI18n(userDO.getCertificationStatus()));
                }
            }
            // TenantDictDataRespDTO authConfig = tenantDictDataApi.parseDictData(userDO.getTenantId(),
            // DictTypeConstants.TENANT_AUTH_CONFIG, "recharge");
            // if (authConfig != null && Objects.equals(authConfig.getValue(), "1")) {
            // if (Objects.equals(userDO.getCertificationStatus(),
            // MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
            // throw exception(USER_CERTIFICATION_VERIFYING);
            // }
            // if (!Objects.equals(userDO.getCertificationStatus(),
            // MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
            // throw exception(USER_CERTIFICATION_NOT_VERIFY,
            // MemberCertificationStatusEnum.getI18n(userDO.getCertificationStatus()));
            // }
            // }
        }

        // ---充值配置判断---------------------
        // List<TenantDictDataRespDTO> withdrawConfigList = tenantDictDataApi.getDictDataList(userDO.getTenantId(),
        // DictTypeConstants.TENANT_RECHARGE_CONFIG);
        // Map<String, String> rechargeConfigMap = withdrawConfigList.parallelStream().collect(
        // Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        //
        // //最大提现中笔数
        // Integer maxProcess = NumberUtil.parseInt(rechargeConfigMap.get("max_process"), 1);
        // 进行中的充值条数记录
        Long recordsCount = memberFundsRecordMapper.countByStatus(userDO.getId(),
            MemberFundsRecordOpTypeEnum.RECHARGE.getType(), MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
        TenantRechargeConfigRespDTo rechargeConfig = tenantDictDataApi.getTenantRechargeConfig(userDO.getTenantId());
        if (recordsCount >= rechargeConfig.getMaxProcess()) {
            throw exception(USER_RECHARGE_LESS_MAX_PROCESS, recordsCount);
        }

        // ---币种判断---------------------
        //BigDecimal currencyRate = currencyRateApi.getTenantCurrencyRate(userDO.getTenantId(), createReqVO.getCurrencyCode());

        // 获取地区
        Area area = AreaUtils.getArea(createReqVO.getAreaId());
        if (area == null) {
            area = new Area();
            area.setId(0);
            area.setName("admin");
        }
        // 插入充值
        MemberFundsRecordDO fundsRecord = new MemberFundsRecordDO();
        fundsRecord.setOrderNo(OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.RECHARGE));
        fundsRecord.setOpType(MemberFundsRecordOpTypeEnum.RECHARGE.getType());
        fundsRecord.setUserId(userDO.getId());
        fundsRecord.setArea(area.getName());
        fundsRecord.setAreaId(area.getId());
        fundsRecord.setUsername(userDO.getUsername());
        fundsRecord.setAgentId(userDO.getAgentId());
        fundsRecord.setAgentName(userDO.getAgentName());
        fundsRecord.setCurrencyCode(createReqVO.getCurrencyCode());
        // 充值按用户选择的币种存储金额
        fundsRecord.setCurrencyAmount(new BigDecimal(createReqVO.getLegalAmount()));
        fundsRecord.setUsdtAmount(BigDecimal.ZERO);
        fundsRecord.setPayMethod(createReqVO.getPayMethod());
        fundsRecord.setOrderSystemStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
        fundsRecord.setOrderMemberStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
        // 充值币与usd的汇率
        fundsRecord.setCurrencyRate(new BigDecimal(createReqVO.getCurrencyRate()));
        fundsRecord.setUsdtAmount(new BigDecimal(createReqVO.getAmount()));
        memberFundsRecordMapper.insert(fundsRecord);

        boolean sendMq = true;
        // 如果是试玩
        if (userDO.getDemo()) {
            MemberFundsRecordHandleReqVO handleReqVO = MemberFundsRecordHandleReqVO.builder().id(fundsRecord.getId())
                .currencyRate(fundsRecord.getCurrencyRate()).systemRemark("自动审核")
                .systemStatus(MemberFundsRecordStatusEnum.SUCCESS.getValue()).build();
            handleRecharge("试玩", handleReqVO);
            sendMq = false;
        }
        if (sendMq && createReqVO.isAutoHandle()) {
            MemberFundsRecordHandleReqVO handleReqVO = MemberFundsRecordHandleReqVO.builder().id(fundsRecord.getId())
                .currencyRate(fundsRecord.getCurrencyRate()).systemRemark(createReqVO.getAutoHandleRemark())
                .systemStatus(MemberFundsRecordStatusEnum.SUCCESS.getValue()).build();
            handleRecharge(createReqVO.getAutoHandler(), handleReqVO);
            sendMq = false;
        }
        if (sendMq) {
            // 发送RocketMQ消息
            memberUserMQProducer.sendUserRechargeMessage(userDO.getTenantId(), userId);
        }

        return fundsRecord.getId();
    }

    @Override
    @Master
    @DSTransactional
    public void createFundsRecordWithdraw(Long userId, AppMemberFundsRecordWithdrawCreateReqVO createReqVO) {
        // 钱包有在吗
        MemberWalletDO memberWalletDO = memberWalletService.getWallet(userId, createReqVO.getWalletId());
        if (memberWalletDO == null) {
            throw exception(USER_WALLET_NOT_EXISTS);
        }
        createWithdraw(createReqVO, userId, memberWalletDO);
    }

    @Override
    public void createFundsRecordWithdraw(Long userId, AppMemberFundsRecordWithdrawWithWalletCreateReqVO createReqVO) {
        Area area = AreaUtils.getArea(createReqVO.getAreaId());
        if (area == null) {
            throw exception(AREA_NOT_EXISTS);
        }
        CurrencyBaseRespDTO currency = currencyApi.getCurrency(createReqVO.getCurrencyCode());
        if (currency == null) {
            throw exception(CURRENCY_NOT_EXISTS);
        }
        MemberWalletDO memberWalletDO = new MemberWalletDO();
        memberWalletDO.setName(createReqVO.getName());
        memberWalletDO.setAccount(createReqVO.getAccount());
        memberWalletDO.setBankAddress(createReqVO.getBankAddress());
        memberWalletDO.setBankBranch(createReqVO.getBankBranch());
        memberWalletDO.setAreaId(createReqVO.getAreaId());
        memberWalletDO.setAreaName(area.getName());
        memberWalletDO.setCurrencyId(currency.getId());
        memberWalletDO.setCurrencyCode(currency.getCode());
        memberWalletDO.setCurrencyName(currency.getName());
        memberWalletDO.setTypeName(createReqVO.getTypeName());
        createWithdraw(createReqVO, userId, memberWalletDO);
    }

    @Override
    @Slave
    public PageResult<MemberFundsRecordDO> getFundsRecordPageByUserId(Long userId,
        AppMemberFundsRecordPageReqVO reqVO) {
        return memberFundsRecordMapper.selectPage(userId, reqVO);
    }

    @Override
    @Master
    public void updateFundsRecord(MemberFundsRecordSaveReqVO updateReqVO) {
        // 校验存在
        MemberFundsRecordDO entity = validateFundsRecordExists(updateReqVO.getId());
        // 更新
        MemberFundsRecordDO updateObj = BeanUtils.toBean(updateReqVO, MemberFundsRecordDO.class);
        if (Objects.equals(entity.getOpType(), MemberFundsRecordOpTypeEnum.RECHARGE.getType())) {
            Area area = AreaUtils.getArea(updateReqVO.getAreaId());
            if (area == null) {
                throw exception(AREA_NOT_EXISTS);
            }
            updateObj.setAreaId(area.getId());
            updateObj.setArea(area.getName());
        }
        memberFundsRecordMapper.updateById(updateObj);
        if (Objects.equals(entity.getOpType(), MemberFundsRecordOpTypeEnum.WITHDRAW.getType())) {
            if (entity.getWalletId() != null && entity.getWalletId() > 0) {
                MemberWalletSaveReqVO walletSaveReqVO =
                    MemberWalletSaveReqVO.builder().id(entity.getWalletId()).type(updateObj.getWalletType())
                        .name(updateObj.getWalletName()).account(updateReqVO.getWalletAccount())
                        .typeName(updateReqVO.getWalletTypeName()).bankAddress(updateReqVO.getWalletBankAddress())
                        .bankBranch(updateReqVO.getWalletBankBranch()).build();
                memberWalletService.updateWallet(walletSaveReqVO);
            }
        }
    }

    @Override
    @Master
    public CommonResult<Boolean> deleteFundsRecord(Long id) {
        // 校验存在
        final MemberFundsRecordDO fundsRecordDO = validateFundsRecordExists(id);
        if (fundsRecordDO.getOrderSystemStatus().equals(MemberFundsRecordStatusEnum.SUCCESS.getValue()) ||
                fundsRecordDO.getOrderSystemStatus().equals(MemberFundsRecordStatusEnum.FAILURE.getValue())) {
            // 删除
            memberFundsRecordMapper.deleteById(id);
            return CommonResult.success(true);
        } else {
            return CommonResult.error(FUNDS_RECORD_CANNOT_DELETE.getCode(), FUNDS_RECORD_CANNOT_DELETE.getMsg());
        }
    }

    private MemberFundsRecordDO validateFundsRecordExists(Long id) {
        MemberFundsRecordDO memberFundsRecordDO = memberFundsRecordMapper.selectById(id);
        if (memberFundsRecordDO == null) {
            throw exception(FUNDS_RECORD_NOT_EXISTS);
        }
        return memberFundsRecordDO;
    }

    @Override
    @Slave
    public MemberFundsRecordDO getFundsRecord(Long id) {
        return memberFundsRecordMapper.selectById(id);
    }

    @Override
    @Slave
    public PageResult<MemberFundsRecordRespVO> getFundsRecordPage(MemberFundsRecordPageReqVO pageReqVO) {
        // 所有试玩账号
        Set<Long> demoUserIds = new HashSet<>();
        if (pageReqVO.getIsDemoUser()) {
            List<MemberUserDO> demoUserList = memberUserService.getDemoUserList();
            if (CollUtil.isNotEmpty(demoUserList)) {
                demoUserIds = demoUserList.stream().map(MemberUserDO::getId).collect(Collectors.toSet());
            }
        }
        final PageResult<MemberFundsRecordDO> pageResult = memberFundsRecordMapper.selectPageOrInIds(pageReqVO, demoUserIds, pageReqVO.getIsDemoUser());
        final PageResult<MemberFundsRecordRespVO> respPage = BeanUtils.toBean(pageResult, MemberFundsRecordRespVO.class);

        // 获取代理信息
        final Set<Long> agentIds = pageResult.getList().stream().map(MemberFundsRecordDO::getAgentId).collect(Collectors.toSet());
        final Map<Long, AgentBaseRespDTO> agentMap = agentApi.getAgentsByIds(agentIds);

        for (MemberFundsRecordRespVO recordDO : respPage.getList()) {
            final AgentBaseRespDTO agentBaseResp = agentMap.get(recordDO.getAgentId());
            if (agentBaseResp != null) {
                recordDO.setAgentParentId(agentBaseResp.getAncestorId());
                recordDO.setAgentParentName(agentBaseResp.getAncestorName());
            }
        }
        return respPage;
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(expire = 3000)
    public void handleRecharge(String handler, MemberFundsRecordHandleReqVO reqVO) {
        // 验证充值记录存在
        MemberFundsRecordDO fundsRecordDO = memberFundsRecordMapper.selectByIdForUpdate(reqVO.getId());
        if (fundsRecordDO == null) {
            throw exception(USER_RECHARGE_NOT_EXISTS);
        }
        if (!MemberFundsRecordStatusEnum.WAITHANDLE.getValue().equals(fundsRecordDO.getOrderSystemStatus())) {
            throw exception(USER_RECHARGE_HAS_HANDLE);
        }
        // 验证用户存在
        MemberUserDO userDO = memberUserService.checkUserExists(fundsRecordDO.getUserId());
        // 保存处理结果
        fundsRecordDO.setOrderMemberStatus(reqVO.getSystemStatus());// 这里预留会员看到的状态，应对以后如果需要对客户单独展示不同状态
        // 挂起的不处理会员状态
        if (!Objects.equals(reqVO.getSystemStatus(), MemberFundsRecordStatusEnum.PENDING.getValue())) {
            fundsRecordDO.setOrderSystemStatus(reqVO.getSystemStatus());
        }
        // -------------这个只有要求后台需要修改汇率时才启用
        // fundsRecordDO.setCurrencyRate(reqVO.getCurrencyRate());
        // fundsRecordDO.setUsdtAmount(NumberUtils.reciprocal(fundsRecordDO.getCurrencyRate(),
        // 2).multiply(fundsRecordDO.getCurrencyAmount()));
        // -------------这个只有要求后台需要修改汇率时才启用

        fundsRecordDO.setSystemRemark(reqVO.getSystemRemark());
        fundsRecordDO.setHandler(handler);
        fundsRecordDO.setHandleTime(DateUtils.getUnixTimestampNow());
        memberFundsRecordMapper.updateById(fundsRecordDO);

        MemberFundsRecordStatusEnum systemStatus = MemberFundsRecordStatusEnum.get(reqVO.getSystemStatus());
        switch (systemStatus) {
            case SUCCESS -> {
                // 增加用户USDT余额的基础信息
                MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
                balanceReqVO.setUserId(fundsRecordDO.getUserId());
                balanceReqVO.setAmount(fundsRecordDO.getUsdtAmount());
                balanceReqVO.setIncrRecharge(true);
                // 帐变基础信息
                MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
                transactionReqVO.setBizOrderNo(fundsRecordDO.getOrderNo());
                transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.RECHARGE);
                transactionReqVO.setRemark(MemberMsgConstants.RECHARGE_SUCCESS);
                // 创建余额变更上下文信息
                MemberUserRespDTO userDTO = MemberUserConvert.INSTANCE.convert2(userDO);
                MemberBalanceUpdateContext balanceContext =
                    new MemberBalanceUpdateContext(userDTO, balanceReqVO, transactionReqVO);
                // 扣减用户的账户USDT余额
                memberBalanceService.incrUserBalance(balanceContext);

                // -----------------增加代理总充-------------
                boolean incRechargeCount = userDO.getRecharge().compareTo(BigDecimal.ZERO) == 0;
                agentStatisticApi.incRechargeCount(userDO.getAgentId(), incRechargeCount,
                    fundsRecordDO.getUsdtAmount());
                // -----------------增加代理总充-------------
            }
            case FAILURE -> {
            }
            case PENDING -> {
            }
        }
    }

    @Override
    @Master
    @DSTransactional
    @Lock4j(expire = 3000)
    public void handleWithdraw(String handler, MemberFundsRecordHandleReqVO reqVO) {
        // 验证充值记录存在
        MemberFundsRecordDO memberFundsRecordDO = memberFundsRecordMapper.selectByIdForUpdate(reqVO.getId());
        if (memberFundsRecordDO == null) {
            throw exception(USER_WITHDRAW_NOT_EXISTS);
        }
        if (!MemberFundsRecordStatusEnum.WAITHANDLE.getValue().equals(memberFundsRecordDO.getOrderSystemStatus()) &&
                !MemberFundsRecordStatusEnum.FAILURE.getValue().equals(memberFundsRecordDO.getOrderSystemStatus())) {
            throw exception(USER_WITHDRAW_HAS_HANDLE);
        }
        // 验证用户是否存在
        MemberUserDO userDO = memberUserService.checkUserExists(memberFundsRecordDO.getUserId());
        // 保存处理结果
        // ------------这个只有要求后台需要修改汇率时才启用
        // memberFundsRecordDO.setCurrencyRate(reqVO.getCurrencyRate());
        // memberFundsRecordDO.setCurrencyAmount(memberFundsRecordDO.getUsdtAmount().multiply(memberFundsRecordDO.getCurrencyRate()));
        // ------------这个只有要求后台需要修改汇率时才启用

        MemberFundsRecordStatusEnum systemStatus = MemberFundsRecordStatusEnum.get(reqVO.getSystemStatus());
        switch (systemStatus) {
            case SUCCESS -> {
                if (!memberFundsRecordDO.getOrderSystemStatus().equals(MemberFundsRecordStatusEnum.WAITHANDLE.getValue())) {
                    throw exception(USER_WITHDRAW_NOT_WAIT_HANDLE);
                }
                memberFundsRecordDO.setOrderMemberStatus(MemberFundsRecordStatusEnum.SUCCESS.getValue());
                memberFundsRecordDO.setOrderSystemStatus(MemberFundsRecordStatusEnum.SUCCESS.getValue());
                // 创建余额更新信息
                MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
                balanceReqVO.setUserId(memberFundsRecordDO.getUserId());
                balanceReqVO.setAmount(memberFundsRecordDO.getUsdtAmount());
                balanceReqVO.setFrozenOnly(true);
                balanceReqVO.setFrozenUpdateEnum(MemberBalanceUpdateEnum.DECREASE);
                balanceReqVO.setRelatedOrderNo(memberFundsRecordDO.getOrderNo());
                balanceReqVO.setFrozenUpdateMark(MemberMsgConstants.WITHDRAW_SUCCESS);
                MemberUserRespDTO memberUserDTO = MemberUserConvert.INSTANCE.convert2(userDO);
                memberBalanceService.decrUserBalance(new MemberBalanceUpdateContext(memberUserDTO, balanceReqVO));
                // 删除冻结明细
                memberFrozenService.deleteById(memberFundsRecordDO.getFrozenId());

                // -----------------增加代理总提-------------
                boolean incWithdrawCount = userDO.getRecharge().compareTo(BigDecimal.ZERO) == 0;
                agentStatisticApi.incRechargeCount(userDO.getAgentId(), incWithdrawCount, memberFundsRecordDO.getUsdtAmount());
                // -----------------增加代理总提-------------
            }
            case FAILURE -> {
                memberFundsRecordDO.setOrderMemberStatus(MemberFundsRecordStatusEnum.FAILURE.getValue());
                memberFundsRecordDO.setOrderSystemStatus(MemberFundsRecordStatusEnum.FAILURE.getValue());

                // 增加用户USDT余额的基础信息
                MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
                balanceReqVO.setUserId(memberFundsRecordDO.getUserId());
                balanceReqVO.setAmount(memberFundsRecordDO.getUsdtAmount());
                balanceReqVO.setFrozenUpdateEnum(MemberBalanceUpdateEnum.DECREASE);
                balanceReqVO.setFrozenUpdateMark(MemberMsgConstants.WITHDRAW_BACK);
                balanceReqVO.setRelatedOrderNo(memberFundsRecordDO.getOrderNo());
                // 帐变基础信息
                MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
                transactionReqVO.setBizOrderNo(memberFundsRecordDO.getOrderNo());
                transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.RECHARGE); // 驳回提现，需要返还用户的钱，所以这里相当于是充值
                transactionReqVO.setRemark(MemberMsgConstants.WITHDRAW_BACK);
                // 创建余额变更上下文信息
                MemberUserRespDTO memberUserDTO = MemberUserConvert.INSTANCE.convert2(userDO);
                MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(memberUserDTO, balanceReqVO, transactionReqVO);
                // 扣减用户的账户USDT余额
                memberBalanceService.incrUserBalance(updateContext);
                // 删除冻结明细
                memberFrozenService.deleteById(memberFundsRecordDO.getFrozenId());
            }
            case PENDING -> {
                // 挂起订单只更新订单的系统状态，不变更订单的前台状态，也不操作用户的余额
                memberFundsRecordDO.setOrderSystemStatus(reqVO.getSystemStatus());
            }
            case WAITHANDLE -> {
                if (MemberFundsRecordStatusEnum.SUCCESS.getValue().equals(memberFundsRecordDO.getOrderSystemStatus())) {
                    throw exception(USER_WITHDRAW_HAS_SUCCESS);
                }
                // 如果当前订单的状态为失败的话，则订单是从已经处理的状态重新改为"待处理"状态，需要处理用户的余额
                else if (MemberFundsRecordStatusEnum.FAILURE.getValue().equals(memberFundsRecordDO.getOrderSystemStatus())) {

                    memberFundsRecordDO.setOrderMemberStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
                    memberFundsRecordDO.setOrderSystemStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());

                    // 余额修改信息
                    MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
                    balanceReqVO.setUserId(memberFundsRecordDO.getUserId());
                    balanceReqVO.setAmount(memberFundsRecordDO.getUsdtAmount().multiply(BigDecimal.valueOf(-1)));
                    balanceReqVO.setRelatedOrderNo(memberFundsRecordDO.getOrderNo());
                    // 需要处理冻结金额
                    balanceReqVO.setFrozenUpdateEnum(MemberBalanceUpdateEnum.INCREASE);
                    balanceReqVO.setFrozenUpdateMark(MemberMsgConstants.WITHDRAW_BACK_TO_WAIT);
                    // 帐变修改信息
                    MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
                    transactionReqVO.setBizOrderNo(memberFundsRecordDO.getOrderNo());
                    transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.RE_WITHDRAW);
                    transactionReqVO.setRemark(MemberMsgConstants.WITHDRAW_BACK_TO_WAIT);
                    // 创建余额变更上下文
                    MemberUserRespDTO userDTO = MemberUserConvert.INSTANCE.convert2(userDO);
                    MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(userDTO, balanceReqVO);
                    updateContext.setTransactionReqVO(transactionReqVO);
                    memberBalanceService.decrUserBalance(updateContext);
                }
            }
        }
        memberFundsRecordDO.setSystemRemark(reqVO.getSystemRemark());
        memberFundsRecordDO.setHandler(handler);
        memberFundsRecordDO.setHandleTime(DateUtils.getUnixTimestampNow());
        memberFundsRecordMapper.updateById(memberFundsRecordDO);
    }

    private boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public List<MemberFundsRecordDO> getMemberFundsRecordByRange(Long startTime, Long endTime) {
        LambdaQueryWrapperX<MemberFundsRecordDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.between(MemberFundsRecordDO::getCreateTime, startTime, endTime)
            .eq(MemberFundsRecordDO::getOrderSystemStatus, MemberFundsRecordStatusEnum.SUCCESS.getValue())
            .select(MemberFundsRecordDO::getUserId, MemberFundsRecordDO::getOpType, MemberFundsRecordDO::getUsdtAmount);
        return memberFundsRecordMapper.selectList(queryWrapperX);
    }

    @Override
    public List<MemberFundsRecordDO> getMemberFundsRecordByRange(List<Long> agentId, Long startTime, Long endTime) {
        return memberFundsRecordMapper.getUserIdAndAmountListByRangeAndAgentId(agentId, startTime, endTime);
    }

    private void createWithdraw(AppMemberFundsRecordWithdrawCreateReqVO createReqVO, long userId, MemberWalletDO memberWalletDO) {
        // 验证用户是否存在
        MemberUserDO userDO = memberUserService.checkUserExists(userId);
        // 判断是否需要认证
        TenantAuthConfigRespDTO authConfig = tenantDictDataApi.getTenantAuthConfig(userDO.getTenantId());
        if (authConfig.isWithdrawNeedAuth()) {
            if (Objects.equals(userDO.getCertificationStatus(),
                MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
                throw exception(USER_CERTIFICATION_VERIFYING);
            }
            if (!Objects.equals(userDO.getCertificationStatus(),
                MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
                throw exception(USER_CERTIFICATION_NOT_VERIFY,
                    MemberCertificationStatusEnum.getI18n(userDO.getCertificationStatus()));
            }
        }
        // 用户提现功能是否被禁用
        if (userDO.getDisableWithdraw()) {
            throw exception(USER_FORBIDDEN_WITHDRAW);
        }
        // TenantDictDataRespDTO authConfig = tenantDictDataApi.parseDictData(userDO.getTenantId(),
        // DictTypeConstants.TENANT_AUTH_CONFIG, "withdraw");
        // if (authConfig != null && Objects.equals(authConfig.getValue(), "1")) {
        // if (Objects.equals(userDO.getCertificationStatus(),
        // MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
        // throw exception(USER_CERTIFICATION_VERIFYING);
        // }
        // if (!Objects.equals(userDO.getCertificationStatus(),
        // MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
        // throw exception(USER_CERTIFICATION_NOT_VERIFY,
        // MemberCertificationStatusEnum.getI18n(userDO.getCertificationStatus()));
        // }
        // }

        // ---提现配置判断---------------------
        // List<TenantDictDataRespDTO> withdrawConfigList = tenantDictDataApi.getDictDataList(userDO.getTenantId(),
        // DictTypeConstants.TENANT_WITHDRAW_CONFIG);
        // Map<String, String> withdrawConfigMap = withdrawConfigList.parallelStream().collect(
        // Collectors.toMap(TenantDictDataRespDTO::getLabel, TenantDictDataRespDTO::getValue));
        TenantWithdrawConfigRespDTo withdrawConfig = tenantDictDataApi.getTenantWithdrawConfig(userDO.getTenantId());

        BigDecimal withdrawAmount = new BigDecimal(createReqVO.getAmount());
        // 最小提现金额
        if (withdrawConfig.getMin().compareTo(withdrawAmount) > 0) {
            throw exception(USER_WITHDRAW_LESS_MIN_AMOUNT, withdrawConfig.getMin());
        }
        // 最大提现金额
        if (withdrawConfig.getMax().compareTo(withdrawAmount) < 0) {
            throw exception(USER_WITHDRAW_LESS_MAX_AMOUNT, withdrawConfig.getMax());
        }
        // 最大提现中笔数
        Long recordsCount = memberFundsRecordMapper.countByStatus(userDO.getId(),
                MemberFundsRecordOpTypeEnum.WITHDRAW.getType(),
                MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
        if (recordsCount >= withdrawConfig.getMaxProcess()) {
            throw exception(USER_WITHDRAW_LESS_MAX_PROCESS, recordsCount);
        }

        // ---币种判断---------------------
        //BigDecimal currencyRate = currencyRateApi.getTenantCurrencyRate(userDO.getTenantId(), memberWalletDO.getCurrencyCode());

        // 计算提现手续费
        BigDecimal feeAmount = BigDecimal.ZERO;
        // 固定手续费
        if (withdrawConfig.getFeeFix().compareTo(BigDecimal.ZERO) > 0) {
            // 按固定手续费
            feeAmount = withdrawConfig.getFeeFix();
        } else {
            // 按提现金额比例手续费,要除100，后台设置的是百分比
            BigDecimal feeScale = NumberUtil.toBigDecimal(withdrawConfig.getFeeScale()).multiply(new BigDecimal("0.01"));
            // 提现最低手续费
            BigDecimal feeMin = NumberUtil.toBigDecimal(withdrawConfig.getFeeMin());
            // 提现最高手续费
            BigDecimal feeMax = NumberUtil.toBigDecimal(withdrawConfig.getFeeMax());
            if (feeScale.compareTo(BigDecimal.ZERO) > 0) {
                feeAmount = new BigDecimal(createReqVO.getAmount()).multiply(feeScale);
                if (feeMin.compareTo(BigDecimal.ZERO) > 0 && feeMin.compareTo(feeAmount) > 0) {
                    feeAmount = feeMin;
                }
                if (feeMax.compareTo(BigDecimal.ZERO) > 0 && feeMax.compareTo(feeAmount) < 0) {
                    feeAmount = feeMax;
                }
            }
        }
        // ---提现配置判断---------------------

        // 验证资金密码
        boolean suc = isPasswordMatch(createReqVO.getFundPassword(), userDO.getFundPassword());
        if (!suc) {
            // 增加用户的密码错误次数
            memberUserService.incrPwdWrongCount(userDO.getTenantId(), userDO.getId(), UserPwdWrongType.FUNDS_PWD);
            throw exception(USER_FUND_PASSWORD_ERROR);
        }

        // 验证用户余额
        memberBalanceService.checkUSDTBalanceEnough(userId, new BigDecimal(createReqVO.getAmount()));

        // 生成单号
        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.WITHDRAW);
        // 添加冻结明细
        MemberFrozenDO memberFrozenDO = new MemberFrozenDO();
        memberFrozenDO.setUserId(userDO.getId());
        memberFrozenDO.setUsername(userDO.getUsername());
        memberFrozenDO.setAgentId(userDO.getAgentId());
        memberFrozenDO.setAgentName(userDO.getAgentName());
        memberFrozenDO.setFrozenAmount(new BigDecimal(createReqVO.getAmount()));
        memberFrozenDO.setFrozenReason(MemberMsgConstants.WITHDRAW_APPLY);
        memberFrozenDO.setBizOrderNo(orderNo);
        memberFrozenDO.setTenantId(userDO.getTenantId());
        Long frozenId = memberFrozenService.insert(memberFrozenDO);
        // 生成资金明细记录
        MemberFundsRecordDO fundsRecord = new MemberFundsRecordDO();
        fundsRecord.setOrderNo(OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.WITHDRAW));
        fundsRecord.setOpType(MemberFundsRecordOpTypeEnum.WITHDRAW.getType());
        fundsRecord.setUserId(userDO.getId());
        fundsRecord.setUsername(userDO.getUsername());
        fundsRecord.setAgentId(userDO.getAgentId());
        fundsRecord.setAgentName(userDO.getAgentName());
        fundsRecord.setCurrencyCode(memberWalletDO.getCurrencyCode());
        fundsRecord.setFrozenId(frozenId);
        // 提现按usdt提现
        fundsRecord.setUsdtAmount(new BigDecimal(createReqVO.getAmount()));
        fundsRecord.setPayMethod(memberWalletDO.getType());
        fundsRecord.setFeeAmount(feeAmount);
        fundsRecord.setWalletId(memberWalletDO.getId());
        fundsRecord.setWalletType(memberWalletDO.getType());
        fundsRecord.setWalletName(memberWalletDO.getName());
        fundsRecord.setWalletAccount(memberWalletDO.getAccount());
        fundsRecord.setWalletTypeName(memberWalletDO.getTypeName());
        fundsRecord.setWalletBankBranch(memberWalletDO.getBankBranch());
        fundsRecord.setWalletBankAddress(memberWalletDO.getBankAddress());
        // 法币金额
        fundsRecord.setCurrencyRate(new BigDecimal(createReqVO.getCurrencyRate()));
        fundsRecord.setCurrencyAmount(new BigDecimal(createReqVO.getLegalAmount()));
        fundsRecord.setOrderSystemStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());
        fundsRecord.setOrderMemberStatus(MemberFundsRecordStatusEnum.WAITHANDLE.getValue());

        memberFundsRecordMapper.insert(fundsRecord);

        // 余额修改信息
        MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
        balanceReqVO.setUserId(userId);
        balanceReqVO.setAmount(fundsRecord.getUsdtAmount().multiply(BigDecimal.valueOf(-1)));
        balanceReqVO.setRelatedOrderNo(orderNo);
        // 需要处理冻结金额
        balanceReqVO.setFrozenUpdateEnum(MemberBalanceUpdateEnum.INCREASE);
        balanceReqVO.setFrozenUpdateMark(MemberMsgConstants.WITHDRAW_APPLY);
        // 帐变修改信息
        MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
        transactionReqVO.setBizOrderNo(orderNo);
        transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.WITHDRAW);
        transactionReqVO.setRemark(MemberMsgConstants.WITHDRAW_APPLY);
        // 创建余额变更上下文
        MemberUserRespDTO userDTO = MemberUserConvert.INSTANCE.convert2(userDO);
        MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(userDTO, balanceReqVO);
        updateContext.setTransactionReqVO(transactionReqVO);
        memberBalanceService.decrUserBalance(updateContext);

        // 如果是试玩
        if (userDO.getDemo()) {
            MemberFundsRecordHandleReqVO handleReqVO = MemberFundsRecordHandleReqVO.builder().id(fundsRecord.getId())
                .currencyRate(fundsRecord.getCurrencyRate()).systemRemark("自动审核")
                .systemStatus(MemberFundsRecordStatusEnum.SUCCESS.getValue()).build();
            handleWithdraw("试玩", handleReqVO);
        } else {
            // 发送RocketMQ消息
            try {
                memberUserMQProducer.sendUserWithdrawMessage(userDO.getTenantId(), userId);
            } catch (Exception e) {
                log.error("发送创建取款订单的RocketMQ消息失败", e);
            }
        }
    }
}