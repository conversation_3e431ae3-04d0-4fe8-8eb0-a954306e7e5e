package com.rf.exchange.module.member.controller.admin.recharge.vo;

import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordPayMethodEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员充值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberRechargeRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26506")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13660")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "币种", requiredMode = Schema.RequiredMode.REQUIRED, example = "11461")
    @ExcelProperty("币种")
    private Long currencyId;

    @Schema(description = "币种名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("币种名称")
    private String currencyName;

    @Schema(description = "法币金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("法币金额")
    private BigDecimal currencyAmount;

    @Schema(description = "到账法币汇率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("到账法币汇率")
    private BigDecimal currencyRate;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值金额")
    private BigDecimal rechargeAmount;

    @Schema(description = "到账金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("到账金额")
    private BigDecimal arrivalAmount;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("实付金额")
    private BigDecimal payAmount;

    @Schema(description = "支付方式，虚拟/银行", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式，虚拟/银行")
    private Integer payMethod;
    @Schema(description = "支付方式，虚拟/银行", requiredMode = Schema.RequiredMode.REQUIRED)
    public String getPayMethodStr(){
        if(payMethod==null)return "";
        return MemberFundsRecordPayMethodEnum.get(payMethod).getLabel();
    }

    @Schema(description = "订单用户状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("订单用户状态")
    private Integer orderMemberStatus;

    @Schema(description = "订单系统状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("订单系统状态")
    private Integer orderSystemStatus;

    @Schema(description = "会员备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("会员备注")
    private String memberRemark;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理人")
    private String handler;

    @Schema(description = "处理时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理时间")
    private Long handleTime;

    @Schema(description = "系统备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("系统备注")
    private String systemRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    public String getOrderMemberStatusStr(){
        if(orderMemberStatus==null)return "";
        return MemberFundsRecordStatusEnum.get(orderMemberStatus).getLabel();
    }
    public String getOrderSystemStatusStr(){
        if(orderSystemStatus==null)return "";
        return MemberFundsRecordStatusEnum.get(orderSystemStatus).getLabel();
    }
}