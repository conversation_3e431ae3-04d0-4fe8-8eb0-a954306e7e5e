package com.rf.exchange.module.member.controller.app.user.vo;

import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 用户个人信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberUserInfoRespVO {

    @Schema(description = "用戶id", requiredMode = Schema.RequiredMode.REQUIRED, example = "100001")
    private Long id;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String username;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String nickname;

    @Schema(description = "用户头像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.xxx.xx/xxx.png")
    private String avatar;

    @Schema(description = "用户手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    private String mobile;

    @Schema(description = "用户性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sex;

    // @Schema(description = "积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    // private Integer point;
    //
    // @Schema(description = "经验值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    // private Integer experience;

    @Schema(description = "用户等级")
    private Level level;

    @Schema(description = "是否成为推广员", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean brokerageEnabled;

    @Schema(description = "餘額", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private BigDecimal usdtBalance;

    @Schema(description = "凍結金額", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private BigDecimal usdtFrozenBalance;

    @Schema(description = "合约账户余额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal contractBalance;

    @Schema(description = "認證狀態:0未認證，1审核中，2成功，3，失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Hidden
    private Short certificationStatus;

    @Schema(description = "認證狀態字符", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    public String getCertificationStatusStr() {
        if (certificationStatus != null) {
            return MemberCertificationStatusEnum.getI18n(certificationStatus);
        }
        return "";
    }

    @Schema(description = "是否有资金密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Boolean hasSafePassword;

    @Schema(description = "总余额(可用余额/冻结/资产)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    public BigDecimal getAllBalance() {
        return usdtBalance.add(usdtFrozenBalance);
    }

    @Schema(description = "信用分-默认100", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer creditScore;

    @Schema(description = "用户 App - 会员等级")
    @Data
    public static class Level {

        @Schema(description = "等级编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "等级名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
        private String name;

        @Schema(description = "等级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer level;

        @Schema(description = "等级图标", example = "https://www.xxx.xx/level.jpg")
        private String icon;

    }

}
