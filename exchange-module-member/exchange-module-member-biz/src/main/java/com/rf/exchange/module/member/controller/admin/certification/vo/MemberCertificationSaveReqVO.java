package com.rf.exchange.module.member.controller.admin.certification.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 会员身份认证新增/修改 Request VO")
@Data
public class MemberCertificationSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13103")
    private Long id;

    @Schema(description = "用户", requiredMode = Schema.RequiredMode.REQUIRED, example = "25514")
    @NotNull(message = "用户不能为空")
    private Long userId;

    @Schema(description = "账号", example = "李四")
    private String username;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED, example = "22806")
    @NotNull(message = "地区不能为空")
    private Long areaId;

    @Schema(description = "地区", example = "赵六")
    private String areaName;

    @Schema(description = "真名", example = "芋艿")
    private String realName;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "性别不能为空")
    private Short sex;

    @Schema(description = "生日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生日不能为空")
    private Long birthday;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "证件类型不能为空")
    private Short credentialsType;

    @Schema(description = "证件号码")
    private String credentialsCode;

    @Schema(description = "证件正面")
    private String credentialsFront;

    @Schema(description = "证件反面")
    private String credentialsBack;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Short status;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "处理时间不能为空")
    private Long handleTime;

    @Schema(description = "处理备注", example = "你说的对")
    private String handleRemark;

}