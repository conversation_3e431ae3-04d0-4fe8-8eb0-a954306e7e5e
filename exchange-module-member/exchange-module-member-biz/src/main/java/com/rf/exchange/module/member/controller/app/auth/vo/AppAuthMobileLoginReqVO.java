//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.Mobile;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.validator.constraints.Length;
//
//import jakarta.validation.constraints.NotEmpty;
//
//@Schema(description = "用户 APP - 手机 + 密码登录 Request VO")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthMobileLoginReqVO {
//
//    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
//    @NotEmpty(message = "手机号不能为空")
//    @Mobile
//    private String mobile;
//
//    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "Password")
//    @NotEmpty(message = "密码不能为空")
//    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
//    private String password;
//}