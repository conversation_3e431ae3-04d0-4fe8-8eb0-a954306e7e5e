package com.rf.exchange.module.member.dal.mysql.recharge;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.dal.dataobject.recharge.MemberRechargeDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.member.controller.admin.recharge.vo.*;

/**
 * 会员充值 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberRechargeMapper extends BaseMapperX<MemberRechargeDO> {

    default MemberRechargeDO selectByIdForUpdate(Long id){
        return selectOne(new LambdaQueryWrapperX<MemberRechargeDO>().eq(MemberRechargeDO::getId,id).last("for update"));
    }

    default PageResult<MemberRechargeDO> selectPage(MemberRechargePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberRechargeDO>()
                .eqIfPresent(MemberRechargeDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(MemberRechargeDO::getUserId, reqVO.getUserId())
                .likeIfPresent(MemberRechargeDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MemberRechargeDO::getCurrencyId, reqVO.getCurrencyId())
                .likeIfPresent(MemberRechargeDO::getCurrencyName, reqVO.getCurrencyName())
                .eqIfPresent(MemberRechargeDO::getCurrencyAmount, reqVO.getCurrencyAmount())
                .eqIfPresent(MemberRechargeDO::getCurrencyRate, reqVO.getCurrencyRate())
                .eqIfPresent(MemberRechargeDO::getRechargeAmount, reqVO.getRechargeAmount())
                .eqIfPresent(MemberRechargeDO::getArrivalAmount, reqVO.getArrivalAmount())
                .eqIfPresent(MemberRechargeDO::getPayAmount, reqVO.getPayAmount())
                .eqIfPresent(MemberRechargeDO::getPayMethod, reqVO.getPayMethod())
                .eqIfPresent(MemberRechargeDO::getOrderMemberStatus, reqVO.getOrderMemberStatus())
                .eqIfPresent(MemberRechargeDO::getOrderSystemStatus, reqVO.getOrderSystemStatus())
                .eqIfPresent(MemberRechargeDO::getMemberRemark, reqVO.getMemberRemark())
                .eqIfPresent(MemberRechargeDO::getHandler, reqVO.getHandler())
                .betweenIfPresent(MemberRechargeDO::getHandleTime, reqVO.getHandleTime())
                .eqIfPresent(MemberRechargeDO::getSystemRemark, reqVO.getSystemRemark())
                .betweenIfPresent(MemberRechargeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberRechargeDO::getId));
    }


    default PageResult<MemberRechargeDO> selectPage(Long userId, AppMemberFundsRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberRechargeDO>()
                .eq(MemberRechargeDO::getUserId,userId)
                .geIfPresent(MemberRechargeDO::getCreateTime, reqVO.getCreateStartTime())
                .leIfPresent(MemberRechargeDO::getCreateTime, reqVO.getCreateEndTime())
                .orderByDesc(MemberRechargeDO::getId));
    }
}