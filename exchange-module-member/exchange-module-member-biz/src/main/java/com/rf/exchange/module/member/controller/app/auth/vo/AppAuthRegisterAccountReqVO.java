//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.CharAndNumber;
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.NotEmpty;
//import lombok.*;
//
///**
// * <AUTHOR>
// * @since 2024-06-07
// */
//@Schema(description = "用户 APP - 账号注册 Request VO")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthRegisterAccountReqVO extends AppAuthRegisterVO {
//
//    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    @NotEmpty(message = "账号不能为空")
//    @CharAndNumber
//    private String account;
//
//
//}
