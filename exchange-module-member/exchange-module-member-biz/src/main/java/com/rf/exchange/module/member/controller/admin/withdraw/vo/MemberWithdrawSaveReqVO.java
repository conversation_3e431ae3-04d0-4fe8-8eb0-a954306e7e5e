package com.rf.exchange.module.member.controller.admin.withdraw.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class MemberWithdrawSaveReqVO {
    @Schema(description = "处理的ID", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive(message = "id必须有值")
    private Long id;
    @Schema(description = "提现金额", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "提现金额必须有值")
    private BigDecimal withdrawAmount;
    @Schema(description = "钱包类型:1银行，2加密货币", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive(message = "钱包类型不正确")
    @InEnum(MemberWalletTypeEnum.class)
    private Integer walletType;
    @Schema(description = "钱包类型名，如银行名，链名", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "银行,链名不能为空")
    private String walletTypeName;
    @Schema(description = "钱包名称，如户名", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    private String walletName;
    @Schema(description = "钱包账号，如银行卡号，钱包地址", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "钱包账号不能为空")
    private String walletAccount;
    @Schema(description = "银行地址", example = "",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String bankAddress;
    @Schema(description = "支行地址", example = "",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String bankBranch;
    @Schema(description = "会员备注", example = "",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String memberRemark;
    @Schema(description = "系统备注", example = "",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String systemRemark;
}
