package com.rf.exchange.module.member.controller.admin.spotorder;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.spotorder.vo.*;
import com.rf.exchange.module.member.dal.dataobject.spotorder.MemberSpotOrderDO;
import com.rf.exchange.module.member.service.spotorder.MemberSpotOrderService;

@Tag(name = "管理后台 - 会员现货订单记录")
@RestController
@RequestMapping("/member/spot-order")
@Validated
public class MemberSpotOrderController {

    @Resource
    private MemberSpotOrderService spotOrderService;


    @GetMapping("/get")
    @Operation(summary = "获得会员现货订单记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:spot-order:query')")
    public CommonResult<MemberSpotOrderRespVO> getSpotOrder(@RequestParam("id") Long id) {
        MemberSpotOrderDO spotOrder = spotOrderService.getSpotOrder(id);
        return success(BeanUtils.toBean(spotOrder, MemberSpotOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员现货订单记录分页")
    @PreAuthorize("@ss.hasPermission('member:spot-order:query')")
    public CommonResult<PageResult<MemberSpotOrderRespVO>> getSpotOrderPage(@Valid MemberSpotOrderPageReqVO pageReqVO) {
        PageResult<MemberSpotOrderDO> pageResult = spotOrderService.getSpotOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberSpotOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员现货订单记录 Excel")
    @PreAuthorize("@ss.hasPermission('member:spot-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSpotOrderExcel(@Valid MemberSpotOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberSpotOrderDO> list = spotOrderService.getSpotOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员现货订单记录.xls", "数据", MemberSpotOrderRespVO.class,
                        BeanUtils.toBean(list, MemberSpotOrderRespVO.class));
    }

}