package com.rf.exchange.module.member.service.wallet;

import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.controller.app.wallet.vo.AppMemberWalletSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.dal.mysql.fundsrecord.MemberFundsRecordMapper;
import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import com.rf.exchange.module.system.api.currency.CurrencyApi;
import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import com.rf.exchange.module.member.controller.admin.wallet.vo.*;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import com.rf.exchange.module.member.dal.mysql.wallet.MemberWalletMapper;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_EXISTS;

/**
 * 会员钱包 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberWalletServiceImpl implements MemberWalletService {

    @Resource
    private MemberWalletMapper walletMapper;
    @Resource
    private MemberFundsRecordMapper memberFundsRecordMapper;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private CurrencyApi currencyApi;
//    @Resource
//    private MemberFundsRecordService memberFundsRecordService;

    @Override
    @Master
    public Long saveWallet(Long userId,AppMemberWalletSaveReqVO createReqVO) {
        Area area = AreaUtils.getArea(createReqVO.getAreaId());
        if(area==null){
            throw exception(AREA_NOT_EXISTS);
        }
        CurrencyBaseRespDTO currency = currencyApi.getCurrency(createReqVO.getCurrencyCode());
        if (currency == null) {
            throw exception(CURRENCY_NOT_EXISTS);
        }
        MemberUserDO userDO = memberUserMapper.selectById(userId);
        if (userDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        MemberWalletDO walletDO = null;
        if (createReqVO.getId() != null && createReqVO.getId() > 0) {
            walletDO = walletMapper.selectById(createReqVO.getId());
        }
        if (walletDO == null) {
            walletDO = new MemberWalletDO();
        }
        walletDO.setUserId(userDO.getId());
        walletDO.setUsername(userDO.getUsername());
        walletDO.setAgentId(userDO.getAgentId());
        walletDO.setAgentName(userDO.getAgentName());
        walletDO.setType(createReqVO.getWalletType());
        walletDO.setTypeName(createReqVO.getTypeName());
        walletDO.setName(createReqVO.getName());
        walletDO.setAccount(createReqVO.getAccount());
        walletDO.setBankAddress(createReqVO.getBankAddress());
        walletDO.setBankBranch(createReqVO.getBankBranch());
        walletDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        walletDO.setCurrencyId(currency.getId());
        walletDO.setCurrencyCode(currency.getCode());
        walletDO.setCurrencyName(currency.getName());
        walletDO.setAreaId(area.getId());
        walletDO.setAreaName(area.getName());
        if (walletDO.getId() != null && walletDO.getId() > 0) {
            walletMapper.updateById(walletDO);
        } else {
            walletMapper.insert(walletDO);
        }
        return walletDO.getId();
    }

    @Override
    @Master
    public void deleteWallet(Long id) {
        // 校验存在
        validateWalletExists(id);
        // 删除
        walletMapper.deleteById(id);
    }

    private void validateWalletExists(Long id) {
        if (walletMapper.selectById(id) == null) {
            throw exception(USER_WALLET_NOT_EXISTS);
        }
    }

    @Override
    @Slave
    public MemberWalletDO getWallet(Long id) {
        return walletMapper.selectById(id);
    }

    @Override
    public MemberWalletDO getWallet(Long userId, Long id) {
        MemberWalletDO memberWalletDO= walletMapper.selectOne(new LambdaQueryWrapperX<MemberWalletDO>().eq(MemberWalletDO::getUserId,userId).eq(MemberWalletDO::getId,id));
        if(memberWalletDO==null){
            throw exception(USER_WALLET_NOT_EXISTS);
        }
        return memberWalletDO;
    }

    @Override
    public void deleteWallet(Long userId, Long id) {
        MemberWalletDO memberWalletDO= walletMapper.selectOne(new LambdaQueryWrapperX<MemberWalletDO>().eq(MemberWalletDO::getUserId,userId).eq(MemberWalletDO::getId,id));
        if(memberWalletDO==null){
            throw exception(USER_WALLET_NOT_EXISTS);
        }
        walletMapper.deleteById(memberWalletDO.getId());
    }

    @Override
    @Slave
    public PageResult<MemberWalletDO> getWalletPage(MemberWalletPageReqVO pageReqVO) {
        return walletMapper.selectPage(pageReqVO);
    }

    @Override
    @Slave
    public List<MemberWalletDO> getWalletList(Long userId) {
      return  walletMapper.selectListByUserId(userId);
    }

    @Override
    public void updateWallet(MemberWalletSaveReqVO memberWalletSaveReqVO) {
        // 校验存在
        validateWalletExists(memberWalletSaveReqVO.getId());
        // 更新
        MemberWalletDO updateObj = BeanUtils.toBean(memberWalletSaveReqVO, MemberWalletDO.class);
        walletMapper.updateById(updateObj);
        //修改提示未审核的钱包信息
        LambdaUpdateWrapper<MemberFundsRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberFundsRecordDO::getWalletId, updateObj.getId()).eq(MemberFundsRecordDO::getOrderSystemStatus, MemberFundsRecordStatusEnum.WAITHANDLE.getValue())
                .eq(MemberFundsRecordDO::getOpType, MemberFundsRecordOpTypeEnum.WITHDRAW.getType())
                .set(MemberFundsRecordDO::getWalletName, updateObj.getName()).set(MemberFundsRecordDO::getWalletAccount, updateObj.getAccount())
                .set(MemberFundsRecordDO::getWalletType, updateObj.getType()).set(MemberFundsRecordDO::getWalletTypeName, updateObj.getTypeName())
                .set(MemberFundsRecordDO::getWalletBankAddress, updateObj.getBankAddress()).set(MemberFundsRecordDO::getWalletBankBranch, updateObj.getBankBranch());
        memberFundsRecordMapper.update(updateWrapper);
    }
}