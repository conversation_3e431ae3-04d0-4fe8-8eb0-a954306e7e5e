package com.rf.exchange.module.member.controller.admin.total.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员日统计数据新增/修改 Request VO")
@Data
@Builder
public class MemberTotalDaySaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21309")
    private Long id;

    @Schema(description = "代理", requiredMode = Schema.RequiredMode.REQUIRED, example = "8969")
    @NotNull(message = "代理不能为空")
    private Long agentId;

    @Schema(description = "代理", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "代理不能为空")
    private String agentName;

    @Schema(description = "会员", requiredMode = Schema.RequiredMode.REQUIRED, example = "9485")
    @NotNull(message = "会员不能为空")
    private Long userId;

    @Schema(description = "会员", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "会员不能为空")
    private String username;

    @Schema(description = "归属时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "归属时间不能为空")
    private Long totalTime;

    @Schema(description = "余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "余额不能为空")
    private BigDecimal usdtBalance;

    @Schema(description = "盈利", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "盈利不能为空")
    private BigDecimal profitAmount;

    @Schema(description = "单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    @NotNull(message = "单数不能为空")
    private Integer orderCount;
    @Schema(description = "充值", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    @NotNull(message = "充值不能为空")
    private BigDecimal recharge;
    @Schema(description = "提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    @NotNull(message = "提现不能为空")
    private BigDecimal withdraw;
}