package com.rf.exchange.module.member.service.config;

import com.rf.exchange.module.member.controller.admin.user.vo.MemberUserConfigSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserConfigDO;

import java.util.Collection;
import java.util.Map;

public interface MemberUserConfigService {

    /**
     * 更新用户的配置项
     *
     * @param reqVO 更新信息
     */
    void updateMemberUserConfig(MemberUserConfigSaveReqVO reqVO);

    /**
     * 通过用户id获取配置项
     *
     * @param userId 用户id
     * @return 配置项
     */
    MemberUserConfigDO getConfigCachedByUserId(Long userId);

    /**
     * 通过会员id获取配置列表
     *
     * @param userIdList 用户id列表
     * @return 用户配置列表
     */
    Map<Long, MemberUserConfigDO> getConfigListByUserIdList(Collection<Long> userIdList);
}
