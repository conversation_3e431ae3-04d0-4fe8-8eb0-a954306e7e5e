package com.rf.exchange.module.member.controller.admin.levelconfig;

import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.levelconfig.vo.*;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import com.rf.exchange.module.member.service.levelconfig.MemberLevelConfigService;

@Tag(name = "管理后台 - 会员等级配置")
@RestController
@RequestMapping("/member/level-config")
@Validated
public class MemberLevelConfigController {

    @Resource
    private MemberLevelConfigService levelConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建会员等级配置")
    @PreAuthorize("@ss.hasPermission('member:level-config:create')")
    public CommonResult<Long> createLevelConfig(@Valid @RequestBody LevelConfigSaveReqVO createReqVO) {
        return success(levelConfigService.createLevelConfig(TenantContextHolder.getTenantId(),createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员等级配置")
    @PreAuthorize("@ss.hasPermission('member:level-config:update')")
    public CommonResult<Boolean> updateLevelConfig(@Valid @RequestBody LevelConfigSaveReqVO updateReqVO) {
        levelConfigService.updateLevelConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/set-default")
    @Operation(summary = "设置默认等级")
    @PreAuthorize("@ss.hasPermission('member:level-config:update')")
    public CommonResult<Boolean> setDefaultLevelConfig(@RequestParam("id")Long id){
        levelConfigService.setDefault(id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员等级配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:level-config:delete')")
    public CommonResult<Boolean> deleteLevelConfig(@RequestParam("id") Long id) {
        levelConfigService.deleteLevelConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员等级配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:level-config:query')")
    public CommonResult<LevelConfigRespVO> getLevelConfig(@RequestParam("id") Long id) {
        MemberLevelConfigDO levelConfig = levelConfigService.getLevelConfig(id);
        return success(BeanUtils.toBean(levelConfig, LevelConfigRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得会员等级配置分页")
    @PreAuthorize("@ss.hasPermission('member:level-config:query')")
    public CommonResult<PageResult<LevelConfigRespVO>> getLevelConfigPage(@Valid @RequestBody LevelConfigPageReqVO pageReqVO) {
        PageResult<MemberLevelConfigDO> pageResult = levelConfigService.getLevelConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LevelConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员等级配置 Excel")
    @PreAuthorize("@ss.hasPermission('member:level-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLevelConfigExcel(@Valid LevelConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberLevelConfigDO> list = levelConfigService.getLevelConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员等级配置.xls", "数据", LevelConfigRespVO.class,
                        BeanUtils.toBean(list, LevelConfigRespVO.class));
    }


}