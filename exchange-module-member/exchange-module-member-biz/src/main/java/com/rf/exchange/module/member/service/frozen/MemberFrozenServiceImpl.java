package com.rf.exchange.module.member.service.frozen;

import com.baomidou.dynamic.datasource.annotation.Slave;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rf.exchange.module.member.controller.admin.frozen.vo.*;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import com.rf.exchange.module.member.dal.mysql.frozen.MemberFrozenMapper;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 会员冻结明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberFrozenServiceImpl implements MemberFrozenService {

    @Resource
    private MemberFrozenMapper frozenMapper;

    @Override
    @Slave
    public PageResult<MemberFrozenDO> getFrozenPage(MemberFrozenPageReqVO pageReqVO) {
        return frozenMapper.selectPage(pageReqVO);
    }

    @Override
    @Slave
    public List<MemberFrozenDO> getListByUserId(Long userId) {
        return frozenMapper.getListByUserId(userId);
    }

    @Override
    public Long insert(MemberFrozenDO memberFrozenDO) {
        frozenMapper.insert(memberFrozenDO);
        return memberFrozenDO.getId();
    }

    @Override
    public void deleteById(Long id) {
        frozenMapper.deleteById(id);
    }
}