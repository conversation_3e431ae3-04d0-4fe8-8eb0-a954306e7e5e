package com.rf.exchange.module.member.service.levelconfig;

import jakarta.validation.*;
import com.rf.exchange.module.member.controller.admin.levelconfig.vo.*;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import com.rf.exchange.framework.common.pojo.PageResult;

/**
 * 会员等级配置 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberLevelConfigService {

    /**
     * 创建会员等级配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLevelConfig(Long tenantId, LevelConfigSaveReqVO createReqVO);

    /**
     * 更新会员等级配置
     *
     * @param updateReqVO 更新信息
     */
    void updateLevelConfig(LevelConfigSaveReqVO updateReqVO);

    /**
     * 删除会员等级配置
     *
     * @param id 编号
     */
    void deleteLevelConfig(Long id);

    /**
     * 获得会员等级配置
     *
     * @param id 编号
     * @return 会员等级配置
     */
    MemberLevelConfigDO getLevelConfig(Long id);

    /**
     * 获得会员等级配置分页
     *
     * @param pageReqVO 分页查询
     * @return 会员等级配置分页
     */
    PageResult<MemberLevelConfigDO> getLevelConfigPage(LevelConfigPageReqVO pageReqVO);

    /**
     * 设置为默认等级
     * @param id
     */
    void setDefault(Long id);
}