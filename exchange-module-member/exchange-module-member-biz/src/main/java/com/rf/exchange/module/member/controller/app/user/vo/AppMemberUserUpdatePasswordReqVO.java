package com.rf.exchange.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

@Schema(description = "用户 APP - 修改密码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberUserUpdatePasswordReqVO {

    @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @NotEmpty(message = "{PASSWORD_NOT_EMPTY}")
    @Length(min = 6, max = 16, message = "{PASSWORD_NOT_LENGTH_6_16}")
    private String password;

//    @Schema(description = "手机验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
//    @NotEmpty(message = "手机验证码不能为空")
//    @Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
//    @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
//    private String code;


    @Schema(description = "旧密码，如果是设置可不填", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456")
    //@NotEmpty(message = "旧密码不能为空")
    private String oldPassword;
}
