package com.rf.exchange.module.member.controller.app.certification;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.image.ImageUrlUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.certification.vo.AppMemberCertificationRespVO;
import com.rf.exchange.module.member.controller.app.certification.vo.AppMemberCertificationSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;
import com.rf.exchange.module.member.service.certification.MemberCertificationService;
import com.rf.exchange.module.system.api.dict.DictDataApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 身份认证")
@RestController
@RequestMapping("/member/certification")
@Validated
@Slf4j
public class AppMemberCertificationController {
    @Resource
    private MemberCertificationService memberCertificationService;
    @Resource
    private DictDataApi dictDataApi;

    @PostMapping("/save")
    @Operation(summary = "保存用户身份认证")
    @PreAuthenticated
    public CommonResult<Boolean> save(@RequestBody @Valid AppMemberCertificationSaveReqVO saveCertificationReqVO) {
        saveCertificationReqVO.setCredentialsFront(ImageUrlUtil.extractPath(saveCertificationReqVO.getCredentialsFront()));
        saveCertificationReqVO.setCredentialsBack(ImageUrlUtil.extractPath(saveCertificationReqVO.getCredentialsBack()));
        memberCertificationService.saveUserCertification(getLoginUserId(), saveCertificationReqVO);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获取认证详情")
    @PreAuthenticated
    public CommonResult<AppMemberCertificationRespVO> get() {
        MemberCertificationDO memberCertificationDO = memberCertificationService.getCertificationByUserId(WebFrameworkUtils.getLoginUserId());
        AppMemberCertificationRespVO repVO= BeanUtils.toBean(memberCertificationDO, AppMemberCertificationRespVO.class);
        dictDataApi.fillS3Host(repVO, AppMemberCertificationRespVO.class);
        return success(repVO);
    }
}
