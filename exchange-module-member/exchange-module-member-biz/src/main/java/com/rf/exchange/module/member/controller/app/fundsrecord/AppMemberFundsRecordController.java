package com.rf.exchange.module.member.controller.app.fundsrecord;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.*;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.service.fundsrecord.MemberFundsRecordService;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.api.tenantdict.dto.TenantWithdrawConfigRespDTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


@Tag(name = "用户 APP - 用户充值/提现")
@RestController
@RequestMapping("/member/funds-records")
@Validated
@Slf4j
public class AppMemberFundsRecordController {

    @Resource
    private MemberFundsRecordService memberFundsRecordService;
    @Resource
    private TenantDictDataApi tenantDictDataApi;

    @GetMapping("/page")
    @Operation(summary = "会员充值提现记录")
    @PreAuthenticated
    public CommonResult<PageResult<AppMemberFundsRecordRespVO>> getRechargePage(AppMemberFundsRecordPageReqVO reqVO) {
        PageResult<MemberFundsRecordDO> pageResult = memberFundsRecordService.getFundsRecordPageByUserId(WebFrameworkUtils.getLoginUserId(), reqVO);
        return success(BeanUtils.toBean(pageResult, AppMemberFundsRecordRespVO.class));
    }

    @PostMapping("/create-recharge")
    @Operation(summary = "会员提交充值")
    @PreAuthenticated
    public CommonResult<Boolean> createRecharge(@RequestBody @Valid AppMemberFundsRecordRechargeCreateReqVO createReqVO) {
        memberFundsRecordService.createFundsRecordRecharge(getLoginUserId(), createReqVO);
        return success(true);
    }

    @PostMapping("/create-withdraw")
    @Operation(summary = "会员提交提现")
    @PreAuthenticated
    public CommonResult<Boolean> createWithdraw(@RequestBody @Valid AppMemberFundsRecordWithdrawWithWalletCreateReqVO createReqVO) {
        TenantWithdrawConfigRespDTo withdrawConfigRespDTo = tenantDictDataApi.getTenantWithdrawConfig(TenantContextHolder.getTenantId());
        if (withdrawConfigRespDTo.getUseWallet()) {
            AppMemberFundsRecordWithdrawCreateReqVO withdrawCreateReqVO = createReqVO;
            memberFundsRecordService.createFundsRecordWithdraw(WebFrameworkUtils.getLoginUserId(), withdrawCreateReqVO);
        } else {
            memberFundsRecordService.createFundsRecordWithdraw(WebFrameworkUtils.getLoginUserId(), createReqVO);
        }

        return success(true);
    }


//    @PostMapping("/create-withdraw-with-wallet")
//    @Operation(summary = "会员提交提现带钱包信息")
//    @PreAuthenticated
//    public CommonResult<Boolean> createWithdrawNoWallet(@RequestBody @Valid AppMemberFundsRecordWithdrawWithWalletCreateReqVO createReqVO){
//        memberFundsRecordService.createFundsRecordWithdraw(WebFrameworkUtils.getLoginUserId(), createReqVO);
//        return success(true);
//    }
}
