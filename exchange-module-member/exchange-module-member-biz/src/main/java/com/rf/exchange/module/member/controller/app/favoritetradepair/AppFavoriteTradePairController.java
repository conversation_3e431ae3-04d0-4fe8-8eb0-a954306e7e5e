package com.rf.exchange.module.member.controller.app.favoritetradepair;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairRespVO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairSaveRemoveReqVO;
import com.rf.exchange.module.member.service.favoritetradepair.MemberFavoriteTradePairService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 用户收藏的交易对 Controller
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Tag(name = "用户 APP - 收藏的交易对")
@RestController
@RequestMapping("/member/favorite/trade-pair")
@Validated
public class AppFavoriteTradePairController {

    @Resource
    private MemberFavoriteTradePairService memberFavoriteTradePairService;

    @GetMapping("/list")
    @Operation(summary = "获取会员收藏的交易对")
    @PreAuthenticated
    public CommonResult<List<AppMemberFavoriteTradePairRespVO>> favoriteTradePairList() {
        List<AppMemberFavoriteTradePairRespVO> appFavoriteTradeList = memberFavoriteTradePairService.getAppFavoriteTradeList(getLoginUserId());
        return success(appFavoriteTradeList);
    }

    @PostMapping("/add")
    @Operation(summary = "添加收藏的交易对")
    @PreAuthenticated
    public CommonResult<Boolean> addFavoriteTradePair(@Valid @RequestBody AppMemberFavoriteTradePairSaveRemoveReqVO reqVO) {
        return success(memberFavoriteTradePairService.appSaveFavoriteTradePair(reqVO, getLoginUserId()));
    }

    @PostMapping("/remove")
    @Operation(summary = "取消收藏的交易对")
    @PreAuthenticated
    public CommonResult<Boolean> removeFavoriteTradePair(@Valid @RequestBody AppMemberFavoriteTradePairSaveRemoveReqVO reqVO) {
        return success(memberFavoriteTradePairService.appRemoveFavoriteTradePair(reqVO, getLoginUserId()));
    }

    @GetMapping("/check")
    @Operation(summary = "检查是否收藏交易对")
    @PreAuthenticated
    public CommonResult<Boolean> removeFavoriteTradePair(@Valid @RequestParam("code") String code) {
        return success(memberFavoriteTradePairService.appCheckTradePairFavorite(code, getLoginUserId()));
    }
}
