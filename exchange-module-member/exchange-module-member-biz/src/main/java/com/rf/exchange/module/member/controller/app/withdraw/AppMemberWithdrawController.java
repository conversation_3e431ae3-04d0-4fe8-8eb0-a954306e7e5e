//package com.rf.exchange.module.member.controller.app.withdraw;
//
//import com.rf.exchange.framework.common.pojo.CommonResult;
//import com.rf.exchange.framework.common.pojo.PageResult;
//import com.rf.exchange.framework.common.util.object.BeanUtils;
//import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
//import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
//import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRespVO;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawCreateReqVO;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawPageReqVO;
//import com.rf.exchange.module.member.dal.dataobject.withdraw.MemberWithdrawDO;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import jakarta.validation.Valid;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import static com.rf.exchange.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "用户 APP - 用户提现")
//@RestController
//@RequestMapping("/member/withdraw")
//@Validated
//@Slf4j
//public class AppMemberWithdrawController {
//    @Resource
//    private MemberWithdrawService memberWithdrawService;
//    @GetMapping("/page")
//    @Operation(summary = "会员充值记录")
//    @PreAuthenticated
//    public CommonResult<PageResult<AppMemberFundsRecordRespVO>> getRechargePage(@Valid AppMemberWithdrawPageReqVO pageReqVO) {
//        PageResult<MemberWithdrawDO> pageResult =  memberWithdrawService.getWithdrawPage(WebFrameworkUtils.getLoginUserId(), pageReqVO);
//        return success(BeanUtils.toBean(pageResult, AppMemberFundsRecordRespVO.class));
//    }
//
//    @PostMapping("/create")
//    @Operation(summary = "会员申请提现")
//    @PreAuthenticated
//    public CommonResult<Boolean> create(@RequestBody @Valid AppMemberWithdrawCreateReqVO reqVO) {
//        memberWithdrawService.createWithdraw(WebFrameworkUtils.getLoginUserId(), reqVO);
//        return CommonResult.success(true);
//    }
//}
