package com.rf.exchange.module.member.framework.web.config;

import com.rf.exchange.framework.swagger.config.ExchangeSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * member 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class MemberWebConfiguration {

    /**
     * member 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi memberGroupedOpenApi() {
        return ExchangeSwaggerAutoConfiguration.buildGroupedOpenApi("member", "会员");
    }

}
