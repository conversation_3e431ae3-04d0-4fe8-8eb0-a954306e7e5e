package com.rf.exchange.module.member.controller.app.withdraw.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "APP用户 - 充值分页 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppMemberWithdrawPageReqVO extends PageParam {
    @Schema(description = "创建时间,起始，unix时间戳，如果不需要则不用传")
    private Long createStartTime;
    @Schema(description = "创建时间,结束，unix时间戳，如果不需要则不用传")
    private Long createEndTime;
}
