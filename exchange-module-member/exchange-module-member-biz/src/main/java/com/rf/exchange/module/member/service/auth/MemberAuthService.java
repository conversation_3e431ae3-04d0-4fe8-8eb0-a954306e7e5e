package com.rf.exchange.module.member.service.auth;

import com.rf.exchange.module.member.controller.app.auth.vo.*;

/**
 * 会员的认证 Service 接口
 * <p>
 * 提供用户的账号密码登录、token 的校验等认证相关的功能
 *
 * <AUTHOR>
 */
public interface MemberAuthService {

    /**
     * 注册账号
     *
     * @param reqVO 注册信息
     */
    void register(AppAuthRegisterReqVO reqVO);

//    /**
//     * 邮箱账号注册
//     * @param reqVO 注册信息
//     */
//    void mailRegister(AppAuthRegisterEmailReqVO reqVO);
//
//    /**
//     * 手机号码注册
//     *
//     * @param reqVO 注册信息
//     */
//    void mobileRegister(AppAuthRegisterMobileReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     */
    void logout(String token);

    /**
     * 账号登录 (复合账号登录, 手机、邮箱、账号三种方式的任一种账号)
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AppAuthLoginRespVO login(AppAuthLoginReqVO reqVO);

    /**
     * 发送手机验证码
     *
     * @param reqVO 请求信息
     */
    void sendSmsCode(Long userId, AppSmsCodeSendReqVO reqVO);

    /**
     * 发送邮箱验证码
     *
     * @param userId   用户id (用户注册场景下为空)
     * @param reqVO    请求信息
     * @param lang     语言
     * @param tenantId 租户id
     */
    void sendMailCode(Long userId, AppMailCodeSendReqVO reqVO, String lang, Long tenantId);

//    /**
//     * 手机 + 密码登录
//     *
//     * @param reqVO 登录信息
//     * @return 登录结果
//     */
//    AppAuthLoginRespVO mobileLogin(AppAuthMobileLoginReqVO reqVO);
//
//    /**
//     * 手机 + 验证码登陆
//     *
//     * @param reqVO    登陆信息
//     * @return 登录结果
//     */
//    AppAuthLoginRespVO smsCodeLogin(AppAuthSmsLoginReqVO reqVO);

//    /**
//     * 给用户发送短信验证码
//     *
//     * @param userId 用户编号
//     * @param reqVO 发送信息
//     */
//    void sendSmsCode(Long userId, AppAuthSmsSendReqVO reqVO);
//
//    /**
//     * 校验短信验证码是否正确
//     *
//     * @param userId 用户编号
//     * @param reqVO 校验信息
//     */
//    void validateSmsCode(Long userId, AppAuthSmsValidateReqVO reqVO);

//    /**
//     * 邮箱 + 密码登录
//     *
//     * @param reqVO 登录信息
//     * @return 登录结果
//     */
//    AppAuthLoginRespVO emailLogin(AppAuthEmailLoginReqVO reqVO);
//
//    /**
//     * 给用户发送短信验证码
//     *
//     * @param userId 用户编号
//     * @param reqVO 发送信息
//     */
//    void sendMailCode(Long userId, AppAuthEmailSendReqVO reqVO);
//
//    /**
//     * 校验邮箱验证码是否正确
//     *
//     * @param userId 用户编号
//     * @param reqVO 校验信息
//     */
//    void validateMailCode(Long userId, AppAuthEmailValidateReqVO reqVO);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AppAuthLoginRespVO refreshToken(String refreshToken);

}
