package com.rf.exchange.module.member.service.total;

import com.rf.exchange.module.member.dal.dataobject.total.MemberTotalDayDO;
import jakarta.validation.*;
import com.rf.exchange.module.member.controller.admin.total.vo.*;
import com.rf.exchange.framework.common.pojo.PageResult;

/**
 * 会员日统计数据 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberTotalDayService {

    /**
     * 创建会员日统计数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDayTotal(MemberTotalDaySaveReqVO createReqVO);

    /**
     * 获得会员日统计数据分页
     *
     * @param pageReqVO 分页查询
     * @return 会员日统计数据分页
     */
    PageResult<MemberTotalDayDO> getDayTotalPage(MemberTotalDayPageReqVO pageReqVO);

}