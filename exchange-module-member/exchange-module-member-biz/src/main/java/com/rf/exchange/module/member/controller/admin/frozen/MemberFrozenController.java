package com.rf.exchange.module.member.controller.admin.frozen;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.frozen.vo.*;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.module.member.service.frozen.MemberFrozenService;

@Tag(name = "管理后台 - 会员冻结明细")
@RestController
@RequestMapping("/member/frozen")
@Validated
public class MemberFrozenController {

    @Resource
    private MemberFrozenService frozenService;


    @PostMapping("/page")
    @Operation(summary = "获得会员冻结明细分页")
    @PreAuthorize("@ss.hasPermission('member:frozen:query')")
    public CommonResult<PageResult<MemberFrozenRespVO>> getFrozenPage(@Valid @RequestBody MemberFrozenPageReqVO pageReqVO) {
        PageResult<MemberFrozenDO> pageResult = frozenService.getFrozenPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberFrozenRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员冻结明细 Excel")
    @PreAuthorize("@ss.hasPermission('member:frozen:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFrozenExcel(@Valid MemberFrozenPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberFrozenDO> list = frozenService.getFrozenPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员冻结明细.xls", "数据", MemberFrozenRespVO.class,
                        BeanUtils.toBean(list, MemberFrozenRespVO.class));
    }

}