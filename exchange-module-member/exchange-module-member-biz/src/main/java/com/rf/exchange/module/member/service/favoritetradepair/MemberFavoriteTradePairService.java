package com.rf.exchange.module.member.service.favoritetradepair;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.member.controller.admin.favoritetradepair.vo.MemberFavoriteTradePairPageReqVO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairRespVO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairSaveRemoveReqVO;
import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;

import java.util.List;

/**
 * 会员收藏交易对 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberFavoriteTradePairService {

    /**
     * 获取会员交易对
     * @param reqVO
     * @return
     */
    PageResult<MemberFavoriteTradePairDO> getFavoriteTradePairPage(MemberFavoriteTradePairPageReqVO reqVO);
    /**
     * APP 会员收藏交易对
     *
     * @param reqVO  收藏信息
     * @param userId 会员id
     * @return 成功失败
     */
    Boolean appSaveFavoriteTradePair(AppMemberFavoriteTradePairSaveRemoveReqVO reqVO, Long userId);

    /**
     * APP 会员取消收藏交易对
     *
     * @param reqVO  收藏信息
     * @param userId 当前请求会员id
     * @return 成功失败
     */
    Boolean appRemoveFavoriteTradePair(AppMemberFavoriteTradePairSaveRemoveReqVO reqVO, Long userId);

    /**
     * 获得会员收藏交易对
     *
     * @param userId 会员id
     * @return 会员收藏交易对
     */
    List<AppMemberFavoriteTradePairRespVO> getAppFavoriteTradeList(Long userId);

    /**
     * 检查交易对是否收藏
     * @param code 交易对代码
     * @param userId 用户id
     * @return true已经收藏
     */
    Boolean appCheckTradePairFavorite(String code, Long userId);
}