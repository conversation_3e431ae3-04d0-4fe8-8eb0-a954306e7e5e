package com.rf.exchange.module.member.dal.dataobject.levelconfig;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员等级配置 DO
 *
 * <AUTHOR>
 */
@TableName("member_level_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberLevelConfigDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 编码
     */
    private int level;
    /**
     * 名称
     */
    private String name;
    /**
     * 图标
     */
    private String icon;

    /**
     * 默认
     */
    private Boolean first;

}