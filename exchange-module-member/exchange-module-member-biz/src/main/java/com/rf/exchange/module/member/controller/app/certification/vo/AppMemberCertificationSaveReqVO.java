package com.rf.exchange.module.member.controller.app.certification.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.certification.MemberCertificationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "用户 App - 会员用户保存身份认证")
@Data
public class AppMemberCertificationSaveReqVO {
//    @Schema(description = "性别,1男，2女，3未知", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @InEnum(SexEnum.class)
//    private Integer sex;

    @Schema(description = "国家id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer areaId;

    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{REAL_NAME_NOT_EMPTY}")
    private String realName;

//    @Schema(description = "生日，unix时间戳(秒)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1087606800")
//    private Long birthday;

    @Schema(description = "证件类型：1身份证，2护照，3驾驶证 4日本健康保险证", example = "1")
    @InEnum(value = MemberCertificationTypeEnum.class,message = "{CERTIFICATION_TYPE_ERROR}")
    private Integer credentialsType;

    @Schema(description = "证件号码", example = "150283198902209212")
    @NotEmpty(message = "{CERTIFICATION_CODE_NOT_EMPTY}")
    private String credentialsCode;

    @Schema(description = "证件正面照URL,^(https?://)[\\w.-]+(/[^?]+)?\\.(jpg|jpeg|png|gif|bmp)(\\?.*)?$", example = "http://test.s3.amazonaws.com/member/credentialfront.jpg")
    @ImageUrl(message = "{IMAGE_URL_ERROR}")
    private String credentialsFront;

    @Schema(description = "证件反面照URL,^(https?://)[\\w.-]+(/[^?]+)?\\.(jpg|jpeg|png|gif|bmp)(\\?.*)?$", example = "http://test.s3.amazonaws.com/member/credentialback.jpg")
    @ImageUrl(message = "{IMAGE_URL_ERROR}")
    private String credentialsBack;


}
