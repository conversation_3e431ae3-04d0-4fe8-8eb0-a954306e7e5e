package com.rf.exchange.module.member.controller.admin.certification.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员身份认证 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberCertificationRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21953")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "用户", requiredMode = Schema.RequiredMode.REQUIRED, example = "18826")
    @ExcelProperty("用户")
    private Long userId;

    @Schema(description = "账号", example = "李四")
    @ExcelProperty("账号")
    private String username;

    @Schema(description = "地区", requiredMode = Schema.RequiredMode.REQUIRED, example = "2514")
    @ExcelProperty("地区")
    private Long areaId;

    @Schema(description = "地区", example = "王五")
    @ExcelProperty("地区")
    private String areaName;

    @Schema(description = "真名", example = "芋艿")
    @ExcelProperty("真名")
    private String realName;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("性别")
    private Short sex;

    @Schema(description = "生日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生日")
    private Long birthday;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("证件类型")
    private Short credentialsType;

    @Schema(description = "证件号码")
    @ExcelProperty("证件号码")
    private String credentialsCode;

    @Schema(description = "证件正面")
    @ExcelProperty("证件正面")
    @ImageUrl
    private String credentialsFront;

    @Schema(description = "证件反面")
    @ExcelProperty("证件反面")
    @ImageUrl
    private String credentialsBack;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Short status;

    @Schema(description = "处理人")
    @ExcelProperty("处理人")
    private String handler;

    @Schema(description = "处理时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理时间")
    private Long handleTime;

    @Schema(description = "处理备注", example = "你说的对")
    @ExcelProperty("处理备注")
    private String handleRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}