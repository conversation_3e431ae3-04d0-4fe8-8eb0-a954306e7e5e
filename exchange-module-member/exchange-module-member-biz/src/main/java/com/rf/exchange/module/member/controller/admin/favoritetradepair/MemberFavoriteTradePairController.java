package com.rf.exchange.module.member.controller.admin.favoritetradepair;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.favoritetradepair.vo.*;
import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;
import com.rf.exchange.module.member.service.favoritetradepair.MemberFavoriteTradePairService;

@Tag(name = "管理后台 - 会员收藏交易对")
@RestController
@RequestMapping("/member/favorite-trade-pair")
public class MemberFavoriteTradePairController {

    @Resource
    private MemberFavoriteTradePairService favoriteTradePairService;

    @GetMapping("/page")
    @Operation(summary = "获得会员收藏交易对分页")
    @PreAuthorize("@ss.hasPermission('member:favorite-trade-pair:query')")
    public CommonResult<PageResult<MemberFavoriteTradePairRespVO>> getFavoriteTradePairPage(@Valid MemberFavoriteTradePairPageReqVO pageReqVO) {
        PageResult<MemberFavoriteTradePairDO> pageResult = favoriteTradePairService.getFavoriteTradePairPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberFavoriteTradePairRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员收藏交易对 Excel")
    @PreAuthorize("@ss.hasPermission('member:favorite-trade-pair:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFavoriteTradePairExcel(@Valid MemberFavoriteTradePairPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberFavoriteTradePairDO> list = favoriteTradePairService.getFavoriteTradePairPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员收藏交易对.xls", "数据", MemberFavoriteTradePairRespVO.class,
                        BeanUtils.toBean(list, MemberFavoriteTradePairRespVO.class));
    }

}