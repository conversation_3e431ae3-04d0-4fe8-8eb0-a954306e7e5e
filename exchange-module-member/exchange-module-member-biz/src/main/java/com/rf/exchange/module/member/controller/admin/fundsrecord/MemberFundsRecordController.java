package com.rf.exchange.module.member.controller.admin.fundsrecord;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordRespVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import com.rf.exchange.module.member.service.fundsrecord.MemberFundsRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员资金记录，如充值提现")
@RestController
@RequestMapping("/member/funds-record")
@Validated
public class MemberFundsRecordController extends TenantBaseController {

    @Resource
    private MemberFundsRecordService fundsRecordService;

    @PostMapping("/update")
    @Operation(summary = "更新会员资金记录-充值提现")
    @PreAuthorize("@ss.hasPermission('member:funds-record:update')")
    public CommonResult<Boolean> updateFundsRecord(@Valid @RequestBody MemberFundsRecordSaveReqVO updateReqVO) {
        setupTenantId();
        fundsRecordService.updateFundsRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员资金记录-充值提现")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:funds-record:delete')")
    public CommonResult<Boolean> deleteFundsRecord(@RequestParam("id") Long id) {
        setupTenantId();
        return fundsRecordService.deleteFundsRecord(id);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员资金记录-充值提现")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:funds-record:query')")
    public CommonResult<MemberFundsRecordRespVO> getFundsRecord(@RequestParam("id") Long id) {
        setupTenantId();
        MemberFundsRecordDO fundsRecord = fundsRecordService.getFundsRecord(id);
        return success(BeanUtils.toBean(fundsRecord, MemberFundsRecordRespVO.class));
    }

    @PostMapping("/page-recharge")
    @Operation(summary = "获得充值分页")
    @PreAuthorize("@ss.hasPermission('member:funds-record:query')")
    public CommonResult<PageResult<MemberFundsRecordRespVO>> getRechargePage(@Valid @RequestBody MemberFundsRecordPageReqVO pageReqVO) {
        setupTenantId();
        pageReqVO.setOpType(MemberFundsRecordOpTypeEnum.RECHARGE.getType());
        return success(fundsRecordService.getFundsRecordPage(pageReqVO));
    }

    @PostMapping("/page-withdraw")
    @Operation(summary = "获得提现分页")
    @PreAuthorize("@ss.hasPermission('member:funds-record:query')")
    public CommonResult<PageResult<MemberFundsRecordRespVO>> getWithdrawPage(@Valid @RequestBody MemberFundsRecordPageReqVO pageReqVO) {
        setupTenantId();
        pageReqVO.setOpType(MemberFundsRecordOpTypeEnum.WITHDRAW.getType());
        return success(fundsRecordService.getFundsRecordPage(pageReqVO));
    }

    @PostMapping("/handle-recharge")
    @Operation(summary = "处理会员充值")
    @PreAuthorize("@ss.hasPermission('member:funds-record:handle-recharge')")
    public CommonResult<Boolean> handleRecharge(@Valid @RequestBody MemberFundsRecordHandleReqVO reqVO){
        setupTenantId();
        fundsRecordService.handleRecharge(SecurityFrameworkUtils.getLoginUsername(),reqVO);
        return success(true);
    }
    @PostMapping("/handle-withdraw")
    @Operation(summary = "处理会员提现")
    @PreAuthorize("@ss.hasPermission('member:funds-record:handle-withdraw')")
    public CommonResult<Boolean> handleWithdraw(@Valid @RequestBody MemberFundsRecordHandleReqVO reqVO){
        setupTenantId();
        fundsRecordService.handleWithdraw(SecurityFrameworkUtils.getLoginUsername(),reqVO);
        return success(true);
    }
}