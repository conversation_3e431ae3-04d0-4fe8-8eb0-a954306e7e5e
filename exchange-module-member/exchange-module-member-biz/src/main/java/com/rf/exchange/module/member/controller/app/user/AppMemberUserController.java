package com.rf.exchange.module.member.controller.app.user;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import java.math.BigDecimal;

import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.module.member.controller.app.user.vo.*;
import com.rf.exchange.module.member.convert.user.MemberUserConvert;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.service.levelconfig.MemberLevelConfigService;
import com.rf.exchange.module.member.service.user.MemberUserService;
import com.rf.exchange.module.trade.api.TradeContractApi;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@Tag(name = "用户 APP - 用户个人中心")
@RestController
@RequestMapping("/member/user")
@Validated
@Slf4j
public class AppMemberUserController {

    @Resource
    private MemberUserService userService;
    @Resource
    private MemberLevelConfigService memberLevelConfigService;
    @Resource
    @Lazy
    private TradeContractApi tradeContractApi;

    @GetMapping("/get")
    @Operation(summary = "获得基本信息")
    @PreAuthenticated
    public CommonResult<AppMemberUserInfoRespVO> getUserInfo() {
        MemberUserDO user = userService.getUser(getLoginUserId());

        AppMemberUserInfoRespVO respVO = MemberUserConvert.INSTANCE.convert(user);
        respVO.setHasSafePassword(StringUtils.hasText(user.getFundPassword()));
        MemberLevelConfigDO levelConfigDO = memberLevelConfigService.getLevelConfig(user.getLevelId());
        AppMemberUserInfoRespVO.Level level = BeanUtils.toBean(levelConfigDO, AppMemberUserInfoRespVO.Level.class);
        respVO.setLevel(level);
        // 合约账户余额
        final BigDecimal positionValue = tradeContractApi.getContractPositionTotalMargin(getLoginUserId());
        respVO.setContractBalance(positionValue);
        return success(respVO);
    }

    @PostMapping("/update")
    @Operation(summary = "修改基本信息")
    @PreAuthenticated
    public CommonResult<Boolean> updateUser(@RequestBody @Valid AppMemberUserUpdateReqVO reqVO) {
        userService.updateUser(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/update-mobile")
    @Operation(summary = "修改用户手机")
    @PreAuthenticated
    public CommonResult<Boolean> updateUserMobile(@RequestBody @Valid AppMemberUserUpdateMobileReqVO reqVO) {
        userService.updateUserMobile(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/update-password")
    @Operation(summary = "修改用户密码", description = "用户修改密码时使用")
    @PreAuthenticated
    public CommonResult<Boolean> updateUserPassword(@RequestBody @Valid AppMemberUserUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "用户忘记密码时使用")
    public CommonResult<Boolean> resetUserPassword(@RequestBody @Valid AppMemberUserResetPasswordReqVO reqVO) {
        userService.resetUserPassword(reqVO);
        return success(true);
    }

    @PostMapping("/update-mail")
    @Operation(summary = "修改用户邮箱")
    @PreAuthenticated
    public CommonResult<Boolean> updateUserMail(@RequestBody @Valid AppMemberUserUpdateMailReqVO reqVO) {
        userService.updateUserMail(getLoginUserId(), reqVO);
        return success(true);
    }

    @PostMapping("/set-fund-password")
    @Operation(summary = "设置支付密码", description = "用户修改密码时使用")
    @PreAuthenticated
    public CommonResult<Boolean> setUserFundPassword(@RequestBody @Valid AppMemberUserUpdatePasswordReqVO reqVO) {
        userService.setFundPassword(getLoginUserId(), reqVO.getPassword());
        return success(true);
    }

    @PostMapping("/update-fund-password")
    @Operation(summary = "修改支付密码", description = "用户修改密码时使用")
    @PreAuthenticated
    public CommonResult<Boolean> updateUserFundPassword(@RequestBody @Valid AppMemberUserUpdatePasswordReqVO reqVO) {
        userService.updateFundPassword(getLoginUserId(), reqVO);
        return success(true);
    }
}
