package com.rf.exchange.module.member.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.dynamic.datasource.tx.DsPropagation;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.annotations.VisibleForTesting;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.order.OrderUtil;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceDTO;
import com.rf.exchange.module.member.api.balance.dto.MemberBalanceUpdateReqVO;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.api.user.dto.MemberUserTotalRespVO;
import com.rf.exchange.module.member.config.MemberProperties;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFrozenUpdateReqVO;
import com.rf.exchange.module.member.controller.admin.user.vo.*;
import com.rf.exchange.module.member.controller.app.auth.vo.AppAuthRegisterReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRechargeCreateReqVO;
import com.rf.exchange.module.member.controller.app.user.vo.*;
import com.rf.exchange.module.member.convert.user.MemberUserConvert;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.dal.mysql.levelconfig.MemberLevelConfigMapper;
import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
import com.rf.exchange.module.member.dal.redis.MemberExtraRedisDAO;
import com.rf.exchange.module.member.enums.MemberMsgConstants;
import com.rf.exchange.module.member.enums.balance.MemberBalanceUpdateEnum;
import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordPayMethodEnum;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import com.rf.exchange.module.member.enums.user.AuthAccountTypeEnum;
import com.rf.exchange.module.member.enums.user.UserPwdWrongType;
import com.rf.exchange.module.member.mq.producer.user.MemberUserMQProducer;
import com.rf.exchange.module.member.service.balance.MemberBalanceService;
import com.rf.exchange.module.member.service.config.MemberUserConfigService;
import com.rf.exchange.module.member.service.frozen.MemberFrozenService;
import com.rf.exchange.module.member.service.fundsrecord.MemberFundsRecordService;
import com.rf.exchange.module.system.api.agent.AgentApi;
import com.rf.exchange.module.system.api.agent.AgentStatisticApi;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import com.rf.exchange.module.system.api.currency.CurrencyApi;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.module.system.api.mail.MailCodeApi;
import com.rf.exchange.module.system.api.mail.dto.MailCodeUseReqDTO;
import com.rf.exchange.module.system.api.oauth2.OAuth2TokenApi;
import com.rf.exchange.module.system.api.permission.RoleApi;
import com.rf.exchange.module.system.api.sms.SmsCodeApi;
import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static com.rf.exchange.module.member.enums.DictTypeConstants.*;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.AGENT_ANCESTOR_NOT_AVAILABLE;
import static com.rf.exchange.module.system.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;

/**
 * 会员 User Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberUserServiceImpl implements MemberUserService {

    @Resource
    private MemberExtraRedisDAO memberExtraRedisDAO;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private MailCodeApi mailCodeApi;
    @Resource
    @Lazy
    private AgentApi agentApi;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private MemberProperties memberProperties;
    @Resource
    private MemberLevelConfigMapper memberLevelConfigMapper;
    @Resource
    private CurrencyApi currencyApi;
    @Resource
    private MemberBalanceService memberBalanceService;
    @Resource
    private MemberUserConfigService memberUserConfigService;
    @Resource
    private MemberUserMQProducer memberUserMQProducer;
    @Resource
    @Lazy
    private AgentStatisticApi agentStatisticApi;
    @Resource
    private MemberFundsRecordService memberFundsRecordService;
    @Resource
    private TenantDictDataApi tenantDictDataApi;
    @Resource
    @Lazy
    private OAuth2TokenApi oAuth2TokenApi;
    @Resource
    private CurrencyRateApi currencyRateApi;
    @Resource
    @Lazy
    private RoleApi roleApi;
    @Resource
    private MemberFrozenService memberFrozenService;

    @Override
    @Master
    @DSTransactional
    public MemberUserDO register(AppAuthRegisterReqVO reqVO) {
        AgentBaseRespDTO agentDTO = null;
        // 有传邀请码
        if (StringUtils.hasText(reqVO.getAgentCode())) {
            agentDTO = agentApi.getAgentByCode(reqVO.getAgentCode());
        }
        // 邀请 码没找到代理，找默认
        if (agentDTO == null) {
            agentDTO = agentApi.getDefault(getTenantId());
        }
        // 默认也没有
        if (agentDTO == null) {
            agentDTO = new AgentBaseRespDTO();
            agentDTO.setId(1L);
            agentDTO.setTenantId(1L);
            agentDTO.setName("system");
        }
        Area area = AreaUtils.getArea(reqVO.getAreaId());
        if (area == null) {
            throw exception(AREA_NOT_EXISTS);
        }
        // 插入用户
        MemberUserDO user = new MemberUserDO();
        user.setAgentId(agentDTO.getId());
        user.setTenantId(agentDTO.getTenantId());
        user.setAgentName(agentDTO.getName());
        user.setAreaId(area.getId());
        user.setAreaName(area.getName());
        user.setRecharge(BigDecimal.ZERO);
        user.setWithdraw(BigDecimal.ZERO);

        // 这里暂时没用
        user.setCurrencyId(0);
        user.setCurrencyName("");

        // 判断账号类型
        AuthAccountTypeEnum type = AuthAccountTypeEnum.get(reqVO.getAccountType());
        switch (type) {
            case MOBILE: {
                // FIXME 等待短信验证
                // smsCodeApi.useSmsCode(new
                // SmsCodeUseReqDTO().setUsedIp(reqVO.getRegisterIp()).setCode(reqVO.getVerifyCode()).setScene(SmsSceneEnum.MEMBER_REGISTER.getScene()).setMobile(reqVO.getMobile()));
                user.setMobile(reqVO.getMobile());
                user.setUsername(reqVO.getMobile());
                break;
            }
            case EMAIL: {
                mailCodeApi
                        .useMailCode(new MailCodeUseReqDTO().setUsedIp(reqVO.getRegisterIp()).setCode(reqVO.getVerifyCode())
                                .setScene(MailSceneEnum.MEMBER_REGISTER.getScene()).setEmail(reqVO.getEmail()));
                user.setEmail(reqVO.getEmail());
                user.setUsername(reqVO.getEmail());
                break;
            }
            case ACCOUNT: {
                user.setAccount(reqVO.getUsername());
                user.setUsername(reqVO.getUsername());
                break;
            }
            default: {
                throw exception(AUTH_ACCOUNT_FORMAT_ERROR);
            }
        }
        // 验证账号是否存在
        MemberUserDO userDO = memberUserMapper.selectByUsername(user.getUsername());
        if (userDO != null) {
            throw exception(USER_ACCOUNT_USED);
        }
        // if (StrUtil.isNotBlank(reqVO.getUsername())) {
        // user.setAccount(reqVO.getUsername());
        // user.setUsername(reqVO.getUsername());
        // } else if (StrUtil.isNotBlank(reqVO.getEmail())) {
        // user.setEmail(reqVO.getEmail());
        // user.setUsername(reqVO.getEmail());
        // } else if (StrUtil.isNotBlank(reqVO.getMobile())) {
        // user.setMobile(reqVO.getMobile());
        // user.setUsername(reqVO.getMobile());
        // } else {
        // throw exception(AUTH_ACCOUNT_FORMAT_ERROR);
        // }
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(reqVO.getPassword())); // 加密密码
        user.setRegisterIp(reqVO.getRegisterIp()).setRegisterTerminal(reqVO.getTerminal());
        user.setNickname(reqVO.getNickname()); // 基础信息
        if (StrUtil.isEmpty(user.getNickname())) {
            // 昵称为空时，随机一个名字，避免一些依赖 nickname 的逻辑报错，或者有点丑。例如说，短信发送有昵称时~
            user.setNickname("USER" + RandomUtil.randomNumbers(6));
        }
        // 随机一个头像
        List<String> defaultAvatars = memberProperties.getDefaultAvatars();
        String avatar = defaultAvatars.get(RandomUtil.randomInt(defaultAvatars.size()));
        user.setAvatar(avatar);
        user.setCreator("");
        user.setCreateTime(DateUtils.getUnixTimestampNow());
        user.setUpdater("");
        user.setUpdateTime(DateUtils.getUnixTimestampNow());
        user.setCertificationStatus(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_NOT.getType());

        MemberLevelConfigDO defaultLevel = memberLevelConfigMapper.getDefault();
        user.setLevelId(defaultLevel.getId());
        user.setLevelName(defaultLevel.getName());
        user.setDemo(reqVO.getDemo());
        if (user.getDemo()) {
            // TODO 暂时默认试玩余额5W
            user.setUsdtBalance(new BigDecimal(50000));
        }

        memberUserMapper.insert(user);

        // 用户配置项
        MemberUserConfigSaveReqVO configSaveReqVO = new MemberUserConfigSaveReqVO();
        configSaveReqVO.setMemberId(user.getId());
        configSaveReqVO.setProfitType(MemberConfigProfitTypeEnum.REAL_PRICE.getType());
        configSaveReqVO.setRandomRate(BigDecimal.ZERO);
        memberUserConfigService.updateMemberUserConfig(configSaveReqVO);

        agentStatisticApi.incRegisterCount(agentDTO.getId());
        //// 发送用户注册的MQ消息
        memberUserMQProducer.sendUserRegisterMessage(user.getTenantId(), user.getId());
        return user;
    }

    @Override
    public MemberUserDO checkUserExists(Long userId) {
        MemberUserDO user = getUser(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @Override
    @Slave
    public MemberUserDO getUser(Long id) {
        return memberUserMapper.selectById(id);
    }

    @Override
    @Slave
    public MemberUserRespVO getUserAndConfig(Long id) {
        Map<String, Object> info = memberUserMapper.selectUserAndConfig(id);
        return BeanUtils.toBean(info, MemberUserRespVO.class);
    }

    @Override
    @Slave
    public MemberUserDO getUser(String account) {
        return memberUserMapper.selectByUsername(account);
    }

    @Override
    public MemberUserDO getUserByEmail(String email) {
        return memberUserMapper.selectByEmail(email);
    }

    @Override
    @Slave
    public List<MemberUserDO> getUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return memberUserMapper.selectBatchIds(ids);
    }

    // @Override
    // @Slave
    // public MemberUserDO getUserByMobile(String mobile) {
    // return memberUserMapper.selectByMobile(mobile);
    // }
    //
    // @Override
    // @Slave
    // public MemberUserDO getUserByEmail(String email) {
    // return memberUserMapper.selectByEmail(email);
    // }
    //
    // @Override
    // @Slave
    // public List<MemberUserDO> getUserListByNickname(String nickname) {
    // return memberUserMapper.selectListByNicknameLike(nickname);
    // }

    // @Override
    // @Master
    // @DSTransactional(rollbackFor = Exception.class)
    // public MemberUserDO createUserWithAccount(Area area, String agentCode, String account, String password, String
    // registerIp, Integer terminal) {
    // // 检查用户名是否已经存在
    // MemberUserDO user = memberUserMapper.selectByAccount(account);
    // if (user != null) {
    // throw exception(USER_ACCOUNT_USED);
    // }
    // return innerCreateUser(area, agentCode, account, null, null, password, null, registerIp, terminal);
    // }
    //
    // @Override
    // @Master
    // @DSTransactional(rollbackFor = Exception.class)
    // public MemberUserDO createUserWithEmail(Area area, String agentCode, String email, String password, String
    // registerIp, Integer terminal) {
    // // 检查邮箱是否已经存在
    // MemberUserDO user = memberUserMapper.selectByEmail(email);
    // if (user != null) {
    // throw exception(USER_EMAIL_USED);
    // }
    // return innerCreateUser(area, agentCode, null, null, email, password, null, registerIp, terminal);
    // }
    //
    // @Override
    // @Master
    // @DSTransactional(rollbackFor = Exception.class)
    // public MemberUserDO createUserWithMobile(Area area, String agentCode, String mobile, String password, String
    // registerIp, Integer terminal) {
    // // FIXME:
    // // 用户已经存在
    // MemberUserDO user = memberUserMapper.selectByMobile(mobile);
    // if (user != null) {
    // throw exception(USER_MOBILE_USED);
    // }
    // // 用户不存在，则进行创建
    // return innerCreateUser(area, agentCode, null, mobile, null, password, null, registerIp, terminal);
    // }
    //
    // @Override
    // @Master
    // @DSTransactional(rollbackFor = Exception.class)
    // public MemberUserDO createUserWithMobileWithoutPasswordIfAbsent(Area area, String agentCode, String mobile,
    // String registerIp, Integer terminal) {
    // // 用户已经存在
    // MemberUserDO user = memberUserMapper.selectByMobile(mobile);
    // if (user != null) {
    // return user;
    // }
    // // 生成密码
    // String password = IdUtil.fastSimpleUUID();
    // // 用户不存在，则进行创建
    // return innerCreateUser(area, agentCode, null, mobile, null, password, null, registerIp, terminal);
    // }

    // private MemberUserDO innerCreateUser(Area area,
    // String agentCode, String username, String mobile,
    // String email, String password, String nickname,
    // String registerIp, Integer terminal) {
    // AgentBaseRespDTO agentDTO = agentApi.getAgentByCode(agentCode);
    // if (agentDTO == null) {
    // agentDTO = new AgentBaseRespDTO();
    // agentDTO.setId(0L);
    // agentDTO.setName("");
    // }
    // // 插入用户
    // MemberUserDO user = new MemberUserDO();
    // user.setAgentId(agentDTO.getId());
    // user.setAgentName(agentDTO.getName());
    // user.setTenantId(agentDTO.getTenantId());
    // user.setAreaId(area.getId());
    // user.setAreaName(area.getName());
    //
    // //FIXME 这里需要从字典中获取国家使用的币种，现在默认写死
    // user.setCurrencyId(1);
    // user.setCurrencyName("美元");
    //
    // if (StrUtil.isNotBlank(username)) {
    // user.setAccount(username);
    // user.setUsername(username);
    // } else if (StrUtil.isNotBlank(email)) {
    // user.setEmail(email);
    // user.setUsername(email);
    // } else if (StrUtil.isNotBlank(mobile)) {
    // user.setMobile(mobile);
    // user.setUsername(mobile);
    // } else {
    // throw exception(AUTH_ACCOUNT_FORMAT_ERROR);
    // }
    // user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
    // user.setPassword(encodePassword(password)); // 加密密码
    // user.setRegisterIp(registerIp).setRegisterTerminal(terminal);
    // user.setNickname(nickname); // 基础信息
    // if (StrUtil.isEmpty(nickname)) {
    // // 昵称为空时，随机一个名字，避免一些依赖 nickname 的逻辑报错，或者有点丑。例如说，短信发送有昵称时~
    // user.setNickname("用户" + RandomUtil.randomNumbers(6));
    // }
    // // 随机一个头像
    // List<String> defaultAvatars = memberProperties.getDefaultAvatars();
    // String avatar = defaultAvatars.get(RandomUtil.randomInt(defaultAvatars.size()));
    // user.setAvatar(avatar);
    // user.setCreator("");
    // user.setCreateTime(LocalDateTime.now());
    // user.setUpdater("");
    // user.setUpdateTime(LocalDateTime.now());
    // user.setCertificationStatus(MemberCertificationStatusEnum.Not_Certified.getType());
    // memberUserMapper.insert(user);
    // return user;
    // }

    @Override
    @Master
    public void updateUserLogin(Long id, String loginIp) {
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, id).set(MemberUserDO::getLoginIp, loginIp).set(MemberUserDO::getLoginDate,
                DateUtils.getUnixTimestampNow());
        memberUserMapper.update(updateWrapper);
    }

    @Override
    @Master
    public void updateUser(Long userId, AppMemberUserUpdateReqVO reqVO) {
        MemberUserDO updateObj = BeanUtils.toBean(reqVO, MemberUserDO.class).setId(userId);
        memberUserMapper.updateById(updateObj);
    }

    @Override
    public void updateUserMobile(Long userId, AppMemberUserUpdateMobileReqVO reqVO) {
        // 1.1 检测用户是否存在
        MemberUserDO user = validateUserExists(userId);
        // 1.2 校验新手机是否已经被绑定
        validateMobileUnique(null, reqVO.getMobile());

        // FIXME 是否需要验证码
        // // 2.1 校验旧手机和旧验证码
        // // 补充说明：从安全性来说，老手机也校验 oldCode 验证码会更安全。但是由于 uni-app 商城界面暂时没做，所以这里不强制校验
        // if (StrUtil.isNotEmpty(reqVO.getOldCode())) {
        // smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(user.getMobile()).setCode(reqVO.getOldCode())
        // .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));
        // }
        // // 2.2 使用新验证码
        // smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(reqVO.getMobile()).setCode(reqVO.getCode())
        // .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));

        // FIXME 这边需要连username也更改吗 3. 更新用户手机
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userId).set(MemberUserDO::getMobile, reqVO.getMobile());
        memberUserMapper.update(updateWrapper);
    }

    @Override
    @Master
    public void updateUserMail(Long userId, AppMemberUserUpdateMailReqVO reqVO) {
        // 1.1 检测用户是否存在
        MemberUserDO user = validateUserExists(userId);
        // 1.2 校验新手机是否已经被绑定
        validateMailUnique(null, reqVO.getMail());

        // FIXME 2.2 使用新验证码
        // mailCodeApi.useMailCode(new
        // MailCodeUseReqDTO().setEmail(reqVO.getMail()).setCode(reqVO.getCode()).setScene(MailSceneEnum.MEMBER_UPDATE_EMAIL.getScene()));

        // FIXME 这边需要连username也更改吗 3. 更新用户手机
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(MemberUserDO::getId, userId).set(MemberUserDO::getEmail, reqVO.getMail());
        memberUserMapper.update(updateWrapper);
    }

    @Override
    @Master
    public void updateUserPassword(Long userId, AppMemberUserUpdatePasswordReqVO reqVO) {
        // 检测用户是否存在
        MemberUserDO user = validateUserExists(userId);
        // 校验验证码
        // TODO 这里如果是账号注册的用户是没有手机号的，考虑是否需要校验手机验证码
        // smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(user.getMobile()).setCode(reqVO.getCode())
        // .setScene(SmsSceneEnum.MEMBER_UPDATE_PASSWORD.getScene()).setUsedIp(getClientIP()));
        boolean isMatch = isPasswordMatch(reqVO.getOldPassword(), user.getPassword());
        if (!isMatch) {
            throw exception(USER_OLD_PASSWORD_NOT_MATCH);
        }
        // 更新用户密码
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userId).set(MemberUserDO::getPassword,
                passwordEncoder.encode(reqVO.getPassword()));
        memberUserMapper.update(updateWrapper);
    }

    @Override
    public void setUserPassword(Long userId, String password) {
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userId).set(MemberUserDO::getPassword, passwordEncoder.encode(password));
        memberUserMapper.update(updateWrapper);
    }

    @Override
    @Master
    public void resetUserPassword(AppMemberUserResetPasswordReqVO reqVO) {
        // 检验用户是否存在
        MemberUserDO user = validateUserExists(reqVO.getMobile());
        // FIXME 使用验证码
        // smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.MEMBER_RESET_PASSWORD,
        // getClientIP()));
        // 更新密码
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, user.getId()).set(MemberUserDO::getPassword,
                passwordEncoder.encode(reqVO.getPassword()));
        memberUserMapper.update(updateWrapper);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    @Master
    public void updateUser(MemberUserUpdateReqVO updateReqVO) {
        // 校验存在
        validateUserExists(updateReqVO.getId());
        // 校验手机唯一
        validateMobileUnique(updateReqVO.getId(), updateReqVO.getMobile());
        if (MemberConfigProfitTypeEnum.RANDOM.getType().equals(updateReqVO.getProfitType())) {
            throw exception(USER_CONFIG_NOT_SUPPORTED);
        }

        // 如果用户被禁用了则使token失效
        if (CommonStatusEnum.DISABLE.getStatus() == updateReqVO.getStatus().intValue()) {
            oAuth2TokenApi.removeAccessToken(updateReqVO.getId());
        }

        // 更新
        MemberUserDO updateObj = MemberUserConvert.INSTANCE.convert(updateReqVO);
        // 更新会员信息时忽略对余额的更新使用单独的接口更新余额和冻结金额
        updateObj.setUsdtBalance(null);
        updateObj.setUsdtFrozenBalance(null);
        memberUserMapper.updateById(updateObj);

        // 更新配置
        MemberUserConfigSaveReqVO configSaveReqVO = BeanUtils.toBean(updateReqVO, MemberUserConfigSaveReqVO.class);
        configSaveReqVO.setMemberId(updateReqVO.getId());
        memberUserConfigService.updateMemberUserConfig(configSaveReqVO);
    }

    @Override
    public void deleteUser(Long id) {
        memberUserMapper.deleteById(id);
    }

    @Override
    @Slave
    public PageResult<MemberUserDO> getUserPage(MemberUserPageReqVO pageReqVO) {
        return memberUserMapper.selectPage(pageReqVO);
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    @VisibleForTesting
    MemberUserDO validateUserExists(Long id) {
        if (id == null) {
            return null;
        }
        MemberUserDO user = memberUserMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @VisibleForTesting
    void validateMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        MemberUserDO user = memberUserMapper.selectByMobile(mobile);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_USED, mobile);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_MOBILE_USED, mobile);
        }
    }

    @VisibleForTesting
    void validateMailUnique(Long id, String mail) {
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_EMAIL_USED, mail);
        }
        if (StrUtil.isBlank(mail)) {
            return;
        }
        MemberUserDO user = memberUserMapper.selectByEmail(mail);
        if (user == null) {
            return;
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_EMAIL_USED, mail);
        }
    }

    private MemberUserDO validateUserExists(String mobile) {
        MemberUserDO user = memberUserMapper.selectByMobile(mobile);
        if (user == null) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
        return user;
    }

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public void updateUserBalance(String handler, MemberUserUpdateBalanceReqVO reqVO) {
        // 检查用户是否存在
        MemberUserDO userDO = checkUserExists(reqVO.getId());
        TenantUtils.execute(userDO.getTenantId(), () -> {

            // 法币金额，汇率-生成充值单需要
            String legalAmount = reqVO.getAmount().toPlainString();
            String currencyRate = "1";
            // 币种不为空时需要转换金额
            if (StrUtil.isNotEmpty(reqVO.getCurrencyCode())) {
                // 获取上分币种与USD的汇率
                BigDecimal tenantCurrencyRateToUSD =
                        currencyRateApi.getTenantCurrencyRateToUSD(getTenantId(), reqVO.getCurrencyCode());
                reqVO.setAmount(reqVO.getAmount().divide(tenantCurrencyRateToUSD, 2, RoundingMode.HALF_UP));
                currencyRate = tenantCurrencyRateToUSD.toPlainString();
            }

            // 生成订单号
            String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.UPDATE_BALANCE);
            // 增加用户USDT余额的基础信息
            MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
            balanceReqVO.setUserId(reqVO.getId());
            balanceReqVO.setAmount(reqVO.getAmount());
            balanceReqVO.setIncrRecharge(false);

            // String tenantCurrencyCode = tenantDictDataApi.getTenantCurrencyCode(userDO.getTenantId());

            // 创建余额变更上下文信息
            MemberUserRespDTO memberUserDTO = MemberUserConvert.INSTANCE.convert2(userDO);
            // 如果大于0就是增加余额
            if (reqVO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (reqVO.getFundsRecordType() > 0) {
                    // 判断是否生成充值提现
                    switch (MemberFundsRecordOpTypeEnum.get(reqVO.getFundsRecordType())) {
                        case RECHARGE: {
                            AppMemberFundsRecordRechargeCreateReqVO createReqVO =
                                    AppMemberFundsRecordRechargeCreateReqVO.builder().currencyCode(reqVO.getCurrencyCode())
                                            .amount(reqVO.getAmount().toPlainString()).autoHandle(true).legalAmount(legalAmount)
                                            .currencyRate(currencyRate).autoHandleRemark("后台增加余额").autoHandler(handler)
                                            .payMethod(MemberFundsRecordPayMethodEnum.FIAT.getValue()).build();
                            Long id = memberFundsRecordService.createFundsRecordRecharge(userDO.getId(), createReqVO);
                            break;
                        }
                    }
                } else {
                    // 帐变基础信息
                    MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
                    transactionReqVO.setBizOrderNo(orderNo);
                    transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.UPDATE_BALANCE);
                    transactionReqVO.setRemark(MemberMsgConstants.ADMIN_UPDATE_BALANCE);
                    memberBalanceService
                            .incrUserBalance(new MemberBalanceUpdateContext(memberUserDTO, balanceReqVO, transactionReqVO));
                }
            } else {
                // 减少余额
                // TODO 生成提现，需要关联钱包，这里先不做
                // case WITHDRAW: {
                // AppMemberFundsRecordWithdrawCreateReqVO createReqVO =
                // AppMemberFundsRecordWithdrawCreateReqVO.builder()
                // .walletId(0l)
                // .amount(reqVO.getAmount())
                // .fundPassword("").build();
                // memberFundsRecordService.createFundsRecordWithdraw(userDO.getId(), createReqVO);
                // }
                // 帐变基础信息
                MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
                transactionReqVO.setBizOrderNo(orderNo);
                transactionReqVO.setTransactionType(MemberTransactionsTypeEnum.UPDATE_BALANCE);
                transactionReqVO.setRemark(MemberMsgConstants.ADMIN_UPDATE_BALANCE);
                memberBalanceService
                        .decrUserBalance(new MemberBalanceUpdateContext(memberUserDTO, balanceReqVO, transactionReqVO));
            }
        });
    }

    @Override
    public void updateUserParentAgent(LoginUser loginUser, MemberUserUpdateAgentReqVO reqVO) {
        final MemberUserDO memberUserDO = memberUserMapper
                .selectOne(new LambdaQueryWrapperX<MemberUserDO>().eqIfPresent(MemberUserDO::getId, reqVO.getUserId()));
        if (null == memberUserDO) {
            throw exception(USER_NOT_EXISTS);
        }

        // 先获取用户所在代理的根代理
        final AgentBaseRespDTO rootAgent = agentApi.getAgentChainRoot(memberUserDO.getAgentId());
        // 获取根代理的所有子代理
        final List<AgentBaseRespDTO> allDescendants = agentApi.getAllDescendants(rootAgent.getId());
        final Set<Long> allAgentIds = allDescendants.stream().map(AgentBaseRespDTO::getId).collect(Collectors.toSet());

        // 判断用户新的父代理是否在是当前租户的代理
        if (!allAgentIds.contains(reqVO.getParentAgentId())) {
            throw exception(AGENT_ANCESTOR_NOT_AVAILABLE);
        }
        final AgentBaseRespDTO agent = agentApi.getAgent(reqVO.getParentAgentId());
        if (null == agent) {
            throw exception(AGENT_NOT_EXISTS);
        }

        // 更新用户的代理id
        final MemberUserDO userUpdate = new MemberUserDO();
        userUpdate.setId(memberUserDO.getId());
        userUpdate.setAgentId(agent.getId());
        userUpdate.setAgentName(agent.getName());
        memberUserMapper.updateById(userUpdate);
    }

    @Override
    public void setFundPassword(Long userId, String fundPassword) {
        MemberUserDO memberUserDO = memberUserMapper.selectOne(new LambdaQueryWrapperX<MemberUserDO>()
                .eq(MemberUserDO::getId, userId).select(MemberUserDO::getId, MemberUserDO::getFundPassword));
        if (memberUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // if (StringUtils.hasText(memberUserDO.getFundPassword())) {
        // throw exception(USER_FUND_PASSWORD_EXISTS);
        // }
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, memberUserDO.getId()).set(MemberUserDO::getFundPassword,
                encodePassword(fundPassword));
        memberUserMapper.update(updateWrapper);
    }

    @Override
    public void updateFundPassword(Long userId, AppMemberUserUpdatePasswordReqVO reqVO) {
        MemberUserDO memberUserDO = memberUserMapper.selectOne(new LambdaQueryWrapperX<MemberUserDO>()
                .eq(MemberUserDO::getId, userId).select(MemberUserDO::getId, MemberUserDO::getFundPassword));
        if (memberUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (!StringUtils.hasText(memberUserDO.getFundPassword())) {
            throw exception(USER_FUND_PASSWORD_NOT_EXISTS);
        }
        if (!isPasswordMatch(reqVO.getOldPassword(), memberUserDO.getFundPassword())) {
            throw exception(USER_OLD_PASSWORD_NOT_MATCH);
        }
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, memberUserDO.getId()).set(MemberUserDO::getFundPassword,
                encodePassword(reqVO.getPassword()));
        memberUserMapper.update(updateWrapper);
    }

    @Override
    public List<MemberUserDO> getAllMemberBalance() {
        LambdaQueryWrapperX<MemberUserDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(MemberUserDO::getDemo, false);
        queryWrapperX.select(MemberUserDO::getId, MemberUserDO::getUsername, MemberUserDO::getAgentId,
                MemberUserDO::getAgentName, MemberUserDO::getUsdtBalance);
        return memberUserMapper.selectList(queryWrapperX);
    }

    @Override
    public List<MemberUserDO> getAgentUserList(Long agentId, Collection<Long> descendantIds) {
        Set<Long> allAgentIds = new HashSet<>(descendantIds);
        allAgentIds.add(agentId);
        List<MemberUserDO> resultList = new ArrayList<>();
        LambdaQueryWrapperX<MemberUserDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.select(MemberUserDO::getId, MemberUserDO::getUsername, MemberUserDO::getAgentId,
                MemberUserDO::getAgentName);
        wrapperX.in(MemberUserDO::getAgentId, allAgentIds);
        boolean run;
        do {
            final PageParam pageParam = new PageParam();
            pageParam.setPageSize(1000);
            final PageResult<MemberUserDO> pageResult = memberUserMapper.selectPage(pageParam, wrapperX);
            if (CollUtil.isNotEmpty(pageResult.getList())) {
                resultList.addAll(pageResult.getList());
                run = pageResult.getTotalPage() > 0 && !pageResult.getPageNo().equals(pageResult.getTotalPage());
            } else {
                run = false;
            }
        } while (run);
        return resultList;
    }

    @Override
    public MemberUserTotalRespVO totalByAgentIdList(List<Long> agentId) {
        MemberUserTotalRespVO ret = new MemberUserTotalRespVO();
        // 总的会员统计
        MemberUserDO userTotal = memberUserMapper.totalMemberByAgentId(agentId);
        ret.setTotalMemberCount(userTotal.getId());
        ret.setTotalMemberBalance(userTotal.getUsdtBalance());
        ret.setTotalMemberRechargeAmount(userTotal.getRecharge());
        ret.setTotalMemberWithdrawAmount(userTotal.getWithdraw());

        // 今日时间范围
        long startTime = DateUtils.getTodayStart();
        long endTime = DateUtils.getUnixTimestampNow();

        // 新用户数
        List<Long> newMemberIdList = memberUserMapper.getNewMemberByRange(agentId, startTime, endTime);
        ret.setTodayNewMemberCount(newMemberIdList.size());

        // 今日代理充提记录
        List<MemberFundsRecordDO> list =
                memberFundsRecordService.getMemberFundsRecordByRange(agentId, startTime, endTime);

        // 新用户充值人数
        Set<Long> newMemberRechargeIdList = new HashSet<Long>();
        // 新用户充值金额
        Set<Long> newMemberWithdrawIdList = new HashSet<Long>();
        Set<Long> allMemberRechargeList = new HashSet<>();
        Set<Long> allMemberWithdrawList = new HashSet<>();
        for (MemberFundsRecordDO item : list) {
            if (Objects.equals(item.getOpType(), MemberFundsRecordOpTypeEnum.RECHARGE.getType())) {
                if (newMemberIdList.contains(item.getUserId())) {
                    newMemberRechargeIdList.add(item.getUserId());// 今日新用户充值人数
                    ret.setTodayNewMemberRechargeCount(ret.getTodayNewMemberRechargeCount() + 1);// 今日新用户充值笔数
                    ret.setTodayNewMemberRechargeAmount(
                            ret.getTodayNewMemberRechargeAmount().add(item.getUsdtAmount()));// 今日新用户充值金额
                }
                allMemberRechargeList.add(item.getUserId());// 今日总充值人数
                ret.setTodayRechargeCount(ret.getTodayRechargeCount() + 1);// 今日总充值笔数
                ret.setTodayRechargeAmount(ret.getTodayRechargeAmount().add(item.getUsdtAmount()));// 今日新用户充值金额
            }
            if (Objects.equals(item.getOpType(), MemberFundsRecordOpTypeEnum.WITHDRAW.getType())) {
                if (newMemberIdList.contains(item.getUserId())) {
                    newMemberWithdrawIdList.add(item.getUserId());// 今日新用户提现人数
                    ret.setTodayNewMemberWithdrawCount(ret.getTodayNewMemberWithdrawCount() + 1);// 今日新用户提现笔数
                    ret.setTodayNewMemberWithdrawAmount(
                            ret.getTodayNewMemberWithdrawAmount().add(item.getUsdtAmount()));// 今日新用户提现金额
                }
                allMemberWithdrawList.add(item.getUserId());// 今日总提现人数
                ret.setTodayWithdrawCount(ret.getTodayWithdrawCount() + 1);// 今日总提现笔数
                ret.setTodayWithdrawAmount(ret.getTodayWithdrawAmount().add(item.getUsdtAmount()));// 今日总提现金额
            }
        }

        ret.setTodayNewMemberRechargeCount(newMemberRechargeIdList.size());
        ret.setTodayNewMemberWithdrawCount(newMemberWithdrawIdList.size());
        ret.setTodayMemberRechargeCount(allMemberRechargeList.size());
        ret.setTodayMemberWithdrawCount(allMemberWithdrawList.size());
        return ret;
    }

    @Override
    @Master
    @DSTransactional(propagation = DsPropagation.NOT_SUPPORTED)
    public void incrPwdWrongCount(Long tenantId, Long userId, UserPwdWrongType wrongType) {
        // 如果wrongType对应的密码错误类型不算作统计项则
        final boolean isNeedStatistic =
                tenantDictDataApi.parseDictDataBoolValue(tenantId, USER_PASSWORD_WRONG_CONFIG, wrongType.getLabel());
        if (!isNeedStatistic) {
            return;
        }
        // 获取redis中的错误次数，如果为null的话则获取数据库中保存的值
        Integer oldCount = memberExtraRedisDAO.getUserPassWrongCount(userId);
        if (null == oldCount) {
            oldCount = memberUserMapper.selectUserPwdWrongCount(userId);
        }
        int newCount = oldCount + 1;
        final String expireValue = tenantDictDataApi.parseDictDataStringValue(tenantId, USER_PASSWORD_WRONG_CONFIG,
                USER_PASSWORD_WRONG_TIME_EXPIRE);
        if (StrUtil.isNotEmpty(expireValue) && "0".equals(expireValue)) {
            memberExtraRedisDAO.updateUserPassWrongCount(userId, newCount, null);
            // 如果是永久有效的话则存下数据库
            memberUserMapper.updateUserPwdWrongCount(userId, newCount);
        } else {
            try {
                final Duration duration = Duration.parse(expireValue);
                memberExtraRedisDAO.updateUserPassWrongCount(userId, newCount, duration);
            } catch (DateTimeParseException ex) {
                log.error("时间解析错误: [{}]", ex.getMessage(), ex);
            }
        }

        // 自动禁用功能
        if (isPwdWrongOverCount(tenantId, userId)) {
            MemberUserDO userUpdate = new MemberUserDO();
            userUpdate.setId(userId);
            userUpdate.setDisableContract(true);
            userUpdate.setDisableWithdraw(true);
            userUpdate.setDisableTimeContract(true);
            memberUserMapper.updateById(userUpdate);
        }
    }

    @Override
    public boolean isPwdWrongOverCount(Long tenantId, Long userId) {
        // 如果密码错误次数功能没有启用则直接返回false
        final boolean isEnable =
                tenantDictDataApi.parseDictDataBoolValue(tenantId, USER_PASSWORD_WRONG_CONFIG, USER_PASSWORD_WRONG_ENABLE);
        if (!isEnable) {
            return false;
        }
        final int wrongCount = memberExtraRedisDAO.getUserPassWrongCount(userId);
        if (wrongCount <= 0) {
            return false;
        }
        final int threshold = tenantDictDataApi.parseDictDataIntValue(tenantId, USER_PASSWORD_WRONG_CONFIG,
                USER_PASSWORD_WRONG_THRESHOLD);
        // threshold大于0且错误次数大于threshold
        return threshold != 0 && wrongCount >= threshold;
    }

    @Override
    public void resetPasswordWrongCount(Long userId) {
        memberExtraRedisDAO.updateUserPassWrongCount(userId, 0, null);
        memberUserMapper.updateUserPwdWrongCount(userId, 0);
    }

    @Override
    @Slave
    public List<MemberUserDO> getDemoUserList() {
        LambdaQueryWrapperX<MemberUserDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(MemberUserDO::getDemo, true);
        final Long count = memberUserMapper.selectCount(wrapper);

        int pageSize = 1000;
        long totalPage = count / 1000 + 1;
        List<MemberUserDO> userList = new ArrayList<>();
        for (int i = 1; i <= totalPage; i++) {
            final PageParam page = new PageParam();
            page.setPageNo(i);
            page.setPageSize(pageSize);

            final PageResult<MemberUserDO> pageResult = memberUserMapper.selectPage(page, wrapper);
            userList.addAll(pageResult.getList());
        }
        return userList;
    }

    @Override
    public void updateFrozenFundsRecord(MemberFrozenUpdateReqVO reqVO) {
        // 校验用户是否存在
        final MemberUserDO member = validateUserExists(reqVO.getUserId());

        boolean isFrozen = reqVO.getOpType() != null && reqVO.getOpType() == 1;
        boolean isUnFrozen = reqVO.getOpType() != null && reqVO.getOpType() == 2;
        final MemberBalanceDTO memberBalance = memberBalanceService.getMemberBalanceCached(member.getId());
        // 校验余额和冻结余额是否足够
        if (isFrozen) {
            if (memberBalance.getUsdtBalance().compareTo(reqVO.getAmount().abs()) < 0) {
                throw exception(USER_BALANCE_NOT_ENOUGH);
            }
        } else if (isUnFrozen) {
            if (memberBalance.getUsdtFrozenBalance().compareTo(reqVO.getAmount().abs()) < 0) {
                throw exception(USER_FROZEN_BALANCE_NOT_ENOUGH);
            }
        }

        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.UPDATE_BALANCE);
        MemberBalanceUpdateEnum balanceUpdateEnum;
        String frozenRemark;
        BigDecimal updateAmount = reqVO.getAmount().abs();
        MemberTransactionsTypeEnum transactionsTypeEnum;

        if (isFrozen) {
            balanceUpdateEnum = MemberBalanceUpdateEnum.INCREASE;
            frozenRemark = MemberMsgConstants.ADMIN_FROZEN;
            transactionsTypeEnum = MemberTransactionsTypeEnum.BALANCE_TO_FROZEN;
        } else if (isUnFrozen) {
            balanceUpdateEnum = MemberBalanceUpdateEnum.DECREASE;
            frozenRemark = MemberMsgConstants.ADMIN_UNFROZEN;
            transactionsTypeEnum = MemberTransactionsTypeEnum.FROZEN_TO_BALANCE;
        } else {
            throw exception(GlobalErrorCodeConstants.BAD_REQUEST);
        }

        if (StrUtil.isNotBlank(reqVO.getRemark())) {
            frozenRemark = reqVO.getRemark();
        }

        // 余额修改信息
        MemberBalanceUpdateReqVO balanceReqVO = new MemberBalanceUpdateReqVO();
        balanceReqVO.setUserId(member.getId());
        balanceReqVO.setAmount(updateAmount);
        balanceReqVO.setRelatedOrderNo(orderNo);
        // 需要处理冻结金额
        balanceReqVO.setFrozenUpdateEnum(balanceUpdateEnum);
        balanceReqVO.setFrozenUpdateMark(frozenRemark);
        // 帐变修改信息
        MemberTransactionSaveReqVO transactionReqVO = new MemberTransactionSaveReqVO();
        transactionReqVO.setBizOrderNo(orderNo);
        transactionReqVO.setTransactionType(transactionsTypeEnum);
        transactionReqVO.setRemark(frozenRemark);
        // 创建余额变更上下文
        MemberUserRespDTO userDTO = MemberUserConvert.INSTANCE.convert2(member);
        MemberBalanceUpdateContext updateContext = new MemberBalanceUpdateContext(userDTO, balanceReqVO);
        updateContext.setTransactionReqVO(transactionReqVO);
        if (isFrozen) {
            memberBalanceService.decrUserBalance(updateContext);
            // 增加冻结明细
            MemberFrozenDO memberFrozenDO = new MemberFrozenDO();
            memberFrozenDO.setUserId(member.getId());
            memberFrozenDO.setUsername(member.getUsername());
            memberFrozenDO.setAgentId(member.getAgentId());
            memberFrozenDO.setAgentName(member.getAgentName());
            memberFrozenDO.setFrozenAmount(updateAmount);
            memberFrozenDO.setFrozenReason(MemberMsgConstants.ADMIN_FROZEN);
            memberFrozenDO.setBizOrderNo(orderNo);
            memberFrozenDO.setTenantId(member.getTenantId());
            memberFrozenService.insert(memberFrozenDO);
        } else {
            memberBalanceService.incrUserBalance(updateContext);
        }
    }
}
