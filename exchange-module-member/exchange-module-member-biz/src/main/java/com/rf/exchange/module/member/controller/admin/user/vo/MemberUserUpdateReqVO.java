package com.rf.exchange.module.member.controller.admin.user.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 会员用户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserUpdateReqVO extends MemberUserBaseVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23788")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "盈利类型：", requiredMode = Schema.RequiredMode.REQUIRED, example = "23788")
    @InEnum(MemberConfigProfitTypeEnum.class)
    @NotNull(message = "profitType must not be null")
    private Integer profitType;

    @Schema(description = "会员备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "有钱")
    private String remark;

    @Schema(description = "禁止提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "有钱")
    private Boolean disableWithdraw;

    @Schema(description = "禁止限时合约", requiredMode = Schema.RequiredMode.REQUIRED, example = "有钱")
    private Boolean disableTimeContract;

    @Schema(description = "禁止合约下单", requiredMode = Schema.RequiredMode.REQUIRED, example = "有钱")
    private Boolean disableContract;
}
