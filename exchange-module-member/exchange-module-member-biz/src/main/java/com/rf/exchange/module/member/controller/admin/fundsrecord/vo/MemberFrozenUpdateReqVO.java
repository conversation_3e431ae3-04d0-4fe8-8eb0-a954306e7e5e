package com.rf.exchange.module.member.controller.admin.fundsrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员资金记录-冻结更新 Request VO")
@Data
public class MemberFrozenUpdateReqVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2966")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "冻结金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "冻结金额不能为空")
    private BigDecimal amount;

    @Schema(description = "操作类型 1:冻结 2:解冻", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "操作类型不能为空")
    @Range(min = 1, max = 2, message = "操作类型只能为1或2")
    private Integer opType;

    @Schema(description = "冻结备注")
    private String remark;
}
