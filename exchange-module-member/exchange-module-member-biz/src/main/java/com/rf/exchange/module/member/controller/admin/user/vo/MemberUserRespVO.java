package com.rf.exchange.module.member.controller.admin.user.vo;

import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 会员用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserRespVO extends MemberUserBaseVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23788")
    private Long id;

    @Schema(description = "注册 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String registerIp;

    @Schema(description = "最后登录IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    private String loginIp;

    @Schema(description = "最后登录时间,unix時間戳，秒為單位", requiredMode = Schema.RequiredMode.REQUIRED)
    private long loginDate;

    @Schema(description = "创建时间，LocalDateTime", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;
    @Schema(description = "盈利类型:0必亏，1必赢，2实际价格，3随机盈利", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer profitType;
    @Schema(description = "盈利随机值", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal randomRate;
    @Schema(description = "当前余额")
    private BigDecimal usdtBalance;
    @Schema(description = "认证状态")
    private Integer certificationStatus;
    @Schema(description = "试玩账号")
    private Boolean demo;

    @Schema(description = "认证状态")
    public String getCertificationStatusStr() {
        return MemberCertificationStatusEnum.get(certificationStatus).getLabel();
    }

    @Schema(description = "盈利类型", requiredMode = Schema.RequiredMode.REQUIRED)
    public String getProfitTypeStr() {
        return MemberConfigProfitTypeEnum.get(profitType).getLabel();
    }
    // ========== 其它信息 ==========

    // @Schema(description = "积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    // private Integer point;
    //
    // @Schema(description = "总积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "2000")
    // private Integer totalPoint;
    //
    // @Schema(description = "会员标签", example = "[红色, 快乐]")
    // private List<String> tagNames;
    //
    // @Schema(description = "会员等级", example = "黄金会员")
    // private String levelName;
    //
    // @Schema(description = "用户分组", example = "购物达人")
    // private String groupName;
    //
    // @Schema(description = "用户经验值", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    // private Integer experience;

    /**
     * 信用分
     */
    @Schema(description = "信用分-默认100", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer creditScore;

    @Schema(description = "禁止提现")
    private Boolean disableWithdraw;

    @Schema(description = "禁止限时合约")
    private Boolean disableTimeContract;

    @Schema(description = "禁止合约下单")
    private Boolean disableContract;

    @Schema(description = "代理树")
    private List<AgentNode> agentNodeList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgentNode {

        @Schema(description = "是否是根代理 true:根代理 false:非根代理")
        private Boolean first;

        @Schema(description = "级差深度 值越大层级越高")
        private Integer dept;

        @Schema(description = "代理名称")
        private String name;

        @Schema(description = "代理id")
        private Long id;
    }
}
