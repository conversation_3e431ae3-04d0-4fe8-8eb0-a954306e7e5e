package com.rf.exchange.module.member.service.spotorder;

import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import com.rf.exchange.module.member.controller.admin.spotorder.vo.*;
import com.rf.exchange.module.member.dal.dataobject.spotorder.MemberSpotOrderDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import com.rf.exchange.module.member.dal.mysql.spotorder.MemberSpotOrderMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员现货订单记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberSpotOrderServiceImpl implements MemberSpotOrderService {

    @Resource
    private MemberSpotOrderMapper spotOrderMapper;

//    @Override
//    public Long createSpotOrder(MemberSpotOrderSaveReqVO createReqVO) {
//        // 插入
//        MemberSpotOrderDO spotOrder = BeanUtils.toBean(createReqVO, MemberSpotOrderDO.class);
//        spotOrderMapper.insert(spotOrder);
//        // 返回
//        return spotOrder.getId();
//    }
//
//    @Override
//    public void updateSpotOrder(MemberSpotOrderSaveReqVO updateReqVO) {
//        // 校验存在
//        validateSpotOrderExists(updateReqVO.getId());
//        // 更新
//        MemberSpotOrderDO updateObj = BeanUtils.toBean(updateReqVO, MemberSpotOrderDO.class);
//        spotOrderMapper.updateById(updateObj);
//    }

    @Override
    @Master
    public void deleteSpotOrder(Long id) {
        // 校验存在
        validateSpotOrderExists(id);
        // 删除
        spotOrderMapper.deleteById(id);
    }

    private void validateSpotOrderExists(Long id) {
        if (spotOrderMapper.selectById(id) == null) {
            throw exception(USER_SPOT_ORDER_NOT_EXISTS);
        }
    }

    @Override
    @Slave
    public MemberSpotOrderDO getSpotOrder(Long id) {
        return spotOrderMapper.selectById(id);
    }

    @Override
    @Slave
    public PageResult<MemberSpotOrderDO> getSpotOrderPage(MemberSpotOrderPageReqVO pageReqVO) {
        return spotOrderMapper.selectPage(pageReqVO);
    }

}