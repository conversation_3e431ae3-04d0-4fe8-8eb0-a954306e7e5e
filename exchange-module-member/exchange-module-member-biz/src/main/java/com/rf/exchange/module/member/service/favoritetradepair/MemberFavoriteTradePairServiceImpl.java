package com.rf.exchange.module.member.service.favoritetradepair;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairTenantApi;
import com.rf.exchange.module.member.controller.admin.favoritetradepair.vo.MemberFavoriteTradePairPageReqVO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairRespVO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairSaveRemoveReqVO;
import com.rf.exchange.module.member.convert.favoritetradepair.FavoriteTradePairConvert;
import com.rf.exchange.module.member.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.rf.exchange.module.member.dal.dataobject.favoritetradepair.MemberFavoriteTradePairDO;

import com.rf.exchange.module.member.dal.mysql.favoritetradepair.MemberFavoriteTradePairMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.USER_FAVORITE_TRADE_PAIR_EXISTS;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.USER_FAVORITE_TRADE_PAIR_NOT_EXISTS;

/**
 * 会员收藏交易对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberFavoriteTradePairServiceImpl implements MemberFavoriteTradePairService {

    // 会员收藏的交易对信息 有效期30天
    private static final String CACHE_KEY = RedisKeyConstants.MEMBER_FAVORITE_TRADE_PAIR + "#30d";

    @Resource
    private MemberFavoriteTradePairMapper favoriteTradePairMapper;

    @Resource
    @Lazy
    private TradePairTenantApi tradePairTenantApi;

    @Resource
    @Lazy
    private CandleDataApi candleDataApi;

    @Override
    public PageResult<MemberFavoriteTradePairDO> getFavoriteTradePairPage(MemberFavoriteTradePairPageReqVO reqVO) {
        return favoriteTradePairMapper.selectPage(reqVO);
    }

    @Override
    @DSTransactional
    @Master
    @CacheEvict(cacheNames = CACHE_KEY, key = "#userId")
    public Boolean appSaveFavoriteTradePair(AppMemberFavoriteTradePairSaveRemoveReqVO reqVO, Long userId) {
        MemberFavoriteTradePairDO favoriteTradePair = favoriteTradePairMapper.selectByCode(userId, reqVO.getCode());
        if (favoriteTradePair != null) {
            throw exception(USER_FAVORITE_TRADE_PAIR_EXISTS);
        }
        // 校验存在
        TradePairRespDTO tradePairRespDTO = tradePairTenantApi.validateTradePairExists(getTenantId(), reqVO.getCode());
        //TODO 代理数据未完成 插入收藏数据
        MemberFavoriteTradePairDO tradePair = MemberFavoriteTradePairDO.builder()
//                .agentId(0l)
//                .agentName("")
                .userId(getLoginUserId())
                .username(getLoginUsername())
                .tradePairCode(tradePairRespDTO.getCode())
                .tradePairName(tradePairRespDTO.getName()).build();
        favoriteTradePairMapper.insert(tradePair);
        return true;
    }

    @Override
    @DSTransactional
    @Master
    @CacheEvict(cacheNames = CACHE_KEY, key = "#userId")
    public Boolean appRemoveFavoriteTradePair(AppMemberFavoriteTradePairSaveRemoveReqVO reqVO, Long userId) {
        // 校验收藏交易对是否存在
        MemberFavoriteTradePairDO favoriteTradePair = favoriteTradePairMapper.selectByCode(userId, reqVO.getCode());
        if (favoriteTradePair == null) {
            throw exception(USER_FAVORITE_TRADE_PAIR_NOT_EXISTS);
        }
        // 删除
        favoriteTradePairMapper.deleteForceByCode(reqVO.getCode(), getLoginUserId());
        return true;
    }

    @Override
    @Slave
    @CachePut(cacheNames = CACHE_KEY, key = "#userId", unless = "#result == null or #result.isEmpty()")
    public List<AppMemberFavoriteTradePairRespVO> getAppFavoriteTradeList(Long userId) {
        // 会员收藏对交易对id列表
        List<MemberFavoriteTradePairDO> favoriteList = favoriteTradePairMapper.selectListByUserId(userId);
        if (CollectionUtil.isEmpty(favoriteList)) {
            return Collections.emptyList();
        }
        // 会员收藏交易对的code集合
        Set<String> favoriteIdSet = favoriteList.stream().map(MemberFavoriteTradePairDO::getTradePairCode).collect(Collectors.toSet());
        // 租户开通的所有交易对列表中用户收藏的交易对列表
        List<TradePairRespDTO> resultTradePairDTOList = tradePairTenantApi.getTradePairListByCodes(getTenantId(), favoriteIdSet);
        if (CollectionUtil.isEmpty(resultTradePairDTOList)) {
            return Collections.emptyList();
        }
        // 会员收藏交易对的code集合
        Set<String> favoriteCodeSet = resultTradePairDTOList.stream().map(TradePairRespDTO::getCode).collect(Collectors.toSet());
        // 从redis中获取交易对的实时价格
        Map<String, CurrentPriceRespDTO> currentPriceMap = candleDataApi.getCurrentPriceListByIdSet(favoriteCodeSet);
        resultTradePairDTOList.forEach(tradePairRespDTO -> {
            if (currentPriceMap.containsKey(tradePairRespDTO.getCode())) {
                CurrentPriceRespDTO priceDto = currentPriceMap.get(tradePairRespDTO.getCode());
                String currentPrice = DecimalFormatUtil.formatWithScale(priceDto.getCurrentPrice(), tradePairRespDTO.getScale());
                tradePairRespDTO.setCurrentPrice(currentPrice);
                tradePairRespDTO.setPercentage(NumberUtils.formatPercent(priceDto.getPercentage().doubleValue(),2));
            }
        });
        return FavoriteTradePairConvert.INSTANCE.convertList(resultTradePairDTOList);
    }

    @Override
    public Boolean appCheckTradePairFavorite(String code, Long userId) {
        final List<MemberFavoriteTradePairDO> doList = favoriteTradePairMapper.selectListByUserId(userId);
        for (MemberFavoriteTradePairDO tradePairDO : doList) {
            if (tradePairDO.getTradePairCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}