package com.rf.exchange.module.member.controller.admin.certification.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-11-14
 */
@Schema(description = "管理后台 - 手动添加会员的身份认证 Request VO")
@Data
public class MemberCertificationManualSaveReqVO {

    @Schema(description = "用户", requiredMode = Schema.RequiredMode.REQUIRED, example = "25514")
    @NotNull(message = "用户不能为空")
    private Long userId;

    @Schema(description = "真名", example = "张三")
    private String realName;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "证件类型不能为空")
    private Integer credentialsType;

    @Schema(description = "证件号码")
    private String credentialsCode;

    @Schema(description = "证件正面")
    @ImageUrl(message = "{IMAGE_URL_ERROR}")
    private String credentialsFront;

    @Schema(description = "证件反面")
    @ImageUrl(message = "{IMAGE_URL_ERROR}")
    private String credentialsBack;

    @Schema(description = "处理备注", example = "你说的对")
    private String handleRemark;
}
