package com.rf.exchange.module.member.service.balance;

import com.rf.exchange.module.member.api.balance.dto.MemberBalanceDTO;
import com.rf.exchange.module.member.context.MemberBalanceUpdateContext;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface MemberBalanceService {

    /**
     * 获取缓存的用户余额
     *
     * @param userId 用户id
     * @return 余额信息
     */
    MemberBalanceDTO getMemberBalanceCached(Long  userId);

    /**
     * 检查余额用户余额是否足够
     *
     * @param userId       用户id
     * @param changeAmount 变更金额
     */
    void checkUSDTBalanceEnough(Long userId, BigDecimal changeAmount);

    /**
     * 检查用户的USDT冻结余额是否足够
     *
     * @param userId       用户id
     * @param changeAmount 变更金额
     */
    void checkUSDTFrozenBalanceEnough(Long userId, BigDecimal changeAmount);

    /**
     * 增加会员的USDT余额
     * <p>
     * 用户的余额变更了，一定会有相应的帐变记录产生
     *
     * @param context 更新上下文
     */
    void incrUserBalance(MemberBalanceUpdateContext context);

    /**
     * 减少会员的USDT余额
     *
     * @param context 更新上下文
     */
    void decrUserBalance(MemberBalanceUpdateContext context);
}
