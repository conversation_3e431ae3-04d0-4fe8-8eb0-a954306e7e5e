package com.rf.exchange.module.member.dal.dataobject.transactions;

import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import lombok.*;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员账变 DO
 *
 * <AUTHOR>
 */
@TableName("member_transactions")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberTransactionsDO extends TenantBaseDO {
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员名称
     */
    private String username;
    /**
     * 发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约
     * {@link MemberTransactionsTypeEnum}
     */
    private Integer type;
    /**
     * 变更金额(有正负)
     */
    private BigDecimal changeAmount;
    /**
     * 货币名称
     */
    private String currencyCode;
    /**
     * 发生前余额
     */
    private BigDecimal beforeBalance;
    /**
     * 发生后余额
     */
    private BigDecimal afterBalance;
    /**
     * 业务订单号
     */
    private String bizOrderNo;
    /**
     * 备注
     */
    private String remark;

    private Long agentId;
    private String agentName;
}