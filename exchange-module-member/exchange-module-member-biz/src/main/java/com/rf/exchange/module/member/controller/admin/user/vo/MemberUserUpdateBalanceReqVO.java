package com.rf.exchange.module.member.controller.admin.user.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 用户修改积分 Request VO")
@Data
@ToString(callSuper = true)
public class MemberUserUpdateBalanceReqVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Positive
    private Long id;

    @Schema(description = "变动的币种，非必填，默认为USDT", example = "JPY")
    private String currencyCode;

    @Schema(description = "变动金额，正数为增加，负数为减少", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private BigDecimal amount;

    @Schema(description = "资金变动类型，0仅修改，1生成充值记录", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "变动类型不能为空")
    private Integer fundsRecordType;

    @Schema(description = "增加还是减少,1增加，2减少")
    @Positive(message = "操作类型")
    private Integer opType;

    @Schema(description = "备注")
    private String remark;
}
