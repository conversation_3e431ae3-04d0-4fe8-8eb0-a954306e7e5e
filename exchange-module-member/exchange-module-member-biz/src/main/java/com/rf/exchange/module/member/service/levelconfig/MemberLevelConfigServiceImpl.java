package com.rf.exchange.module.member.service.levelconfig;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.util.TenantUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rf.exchange.module.member.controller.admin.levelconfig.vo.*;
import com.rf.exchange.module.member.dal.dataobject.levelconfig.MemberLevelConfigDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.member.dal.mysql.levelconfig.MemberLevelConfigMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员等级配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberLevelConfigServiceImpl implements MemberLevelConfigService {

    @Resource
    private MemberLevelConfigMapper levelConfigMapper;

    @Override
    public Long createLevelConfig(Long tenantId,LevelConfigSaveReqVO createReqVO) {
        return TenantUtils.execute(tenantId,()->{
            // 插入
            MemberLevelConfigDO levelConfig = BeanUtils.toBean(createReqVO, MemberLevelConfigDO.class);
//        long exists = levelConfigMapper.selectCount(new LambdaQueryWrapperX<MemberLevelConfigDO>().eq(MemberLevelConfigDO::getName, createReqVO.getName()));
//        if (exists > 0) {
//            throw exception(LEVEL_CONFIG_NAME_EXISTS);
//        }
            //如果这个是默认，先把其它默认置为非默认
            if (createReqVO.getFirst()) {
                levelConfigMapper.update(new LambdaUpdateWrapper<MemberLevelConfigDO>().set(MemberLevelConfigDO::getFirst, false));
            }
            levelConfigMapper.insert(levelConfig);
            // 返回
            return levelConfig.getId();
        });
    }

    @Override
    public void updateLevelConfig(LevelConfigSaveReqVO updateReqVO) {
        // 校验存在
        MemberLevelConfigDO memberLevelConfigDO = validateLevelConfigExists(updateReqVO.getId());
        long exists = levelConfigMapper.selectCount(new LambdaQueryWrapperX<MemberLevelConfigDO>().eq(MemberLevelConfigDO::getName, updateReqVO.getName())
                .ne(MemberLevelConfigDO::getId, updateReqVO.getId()));
        if (exists > 0) {
            throw exception(LEVEL_CONFIG_NAME_EXISTS);
        }
        //不让修改默认等级，通过单独接口设置默认等级
        updateReqVO.setFirst(null);
//        //如果这个是默认，先把其它默认置为非默认
//        if (updateReqVO.getFirst()) {
//            levelConfigMapper.update(new LambdaUpdateWrapper<MemberLevelConfigDO>().set(MemberLevelConfigDO::getFirst, false));
//        }
        // 更新
        MemberLevelConfigDO updateObj = BeanUtils.toBean(updateReqVO, MemberLevelConfigDO.class);
        levelConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteLevelConfig(Long id) {
        // 校验存在
        MemberLevelConfigDO memberLevelConfigDO = validateLevelConfigExists(id);
        if (memberLevelConfigDO.getFirst()) {
            throw exception(LEVEL_CONFIG_DEFAULT_DELETED_FORBID);
        }
        // 删除
        levelConfigMapper.deleteById(id);
    }

    private MemberLevelConfigDO validateLevelConfigExists(Long id) {
        MemberLevelConfigDO memberLevelConfigDO = levelConfigMapper.selectById(id);
        if (memberLevelConfigDO == null) {
            throw exception(LEVEL_CONFIG_NOT_EXISTS);
        }
        return memberLevelConfigDO;
    }

    @Override
    public MemberLevelConfigDO getLevelConfig(Long id) {
        return levelConfigMapper.selectById(id);
    }

    @Override
    public PageResult<MemberLevelConfigDO> getLevelConfigPage(LevelConfigPageReqVO pageReqVO) {
        return levelConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public void setDefault(Long id) {
        //先设置其它等级为非默认
        levelConfigMapper.update(new LambdaUpdateWrapper<MemberLevelConfigDO>().set(MemberLevelConfigDO::getFirst, false));
        levelConfigMapper.update(new LambdaUpdateWrapper<MemberLevelConfigDO>().eq(MemberLevelConfigDO::getId,id).set(MemberLevelConfigDO::getFirst, true));
    }
}