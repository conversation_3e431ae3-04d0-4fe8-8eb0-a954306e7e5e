package com.rf.exchange.module.member.controller.admin.levelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 会员等级配置新增/修改 Request VO")
@Data
public class LevelConfigSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13321")
    private Long id;

    @Schema(description = "等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive(message = "等级不能为空")
    private Integer level;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "图标不能为空")
    private String icon;

    @Schema(description = "默认等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "默认等级不能为空")
    private Boolean first;
}