package com.rf.exchange.module.member.controller.app.fundsrecord.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordOpTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.*;

@Schema(description = "APP用户 - 充值分页 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppMemberFundsRecordPageReqVO extends PageParam {
    @Schema(description = "操作类型：0充值，1提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(value = MemberFundsRecordOpTypeEnum.class,message = "{FUNDS_RECORD_OP_TYPE}")
    private Integer opType;
    @Schema(description = "创建时间,起始，unix时间戳，如果不需要则不用传")
    private Long createStartTime;
    @Schema(description = "创建时间,结束，unix时间戳，如果不需要则不用传")
    private Long createEndTime;
    @Schema(description = "状态，0处理中,1成功，2失败")
    private Integer status;
}
