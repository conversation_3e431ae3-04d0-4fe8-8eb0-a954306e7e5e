package com.rf.exchange.module.member.controller.app.auth.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.framework.common.validation.Mobile;
import com.rf.exchange.module.member.enums.user.AuthAccountTypeEnum;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Positive;

/**
 * 注册请求参数
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Schema(description = "用户 APP - 注册账号 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppAuthRegisterReqVO {

    /**
     * 账号类型，用于区分使用何种方式注册
     * {@link AuthAccountTypeEnum}
     */
    @Schema(description = "账号类型,1账号，2邮箱，3手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(value = AuthAccountTypeEnum.class,message = "{ACCOUNT_TYPE_ERROR}")
    private Integer accountType;

    @Schema(description = "账号,邮箱，手机号必选一个", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "lisa")
    //@Size(min = 6, max = 12, message = "用户账号长度为 6-12 个字符")
    //@Pattern(regexp = "^[a-zA-Z0-9]+$", message = "账号必须包含至少一个字母，并且只能由数字和字母组成")
    private String username;

    @Schema(description = "账号,邮箱，手机号必选一个", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "<EMAIL>")
    @Email(message = "{MAIL_FORMATTER_ERROR}")
    private String email;

    @Schema(description = "账号,邮箱，手机号必选一个", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "***********")
    @Mobile(message = "MOBILE_FORMATTER_ERROR")
    private String mobile;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "Password")
    //@NotEmpty(message = "{PASSWORD_NOT_EMPTY}")
    //@Size(min = 6, max = 16, message = "{PASSWORD_NOT_LENGTH_6_16}")
    //@Pattern(regexp = "^[a-zA-Z0-9]+$", message = "{PASSWORD_FORMATTER_ERROR}")
    private String password;

    @Schema(description = "邀请码", requiredMode = Schema.RequiredMode.REQUIRED, example = "default")
    //@NotEmpty(message = "邀请码不能为空")
    private String agentCode;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "张三")
    private String nickname;

    @Schema(description = "国家id", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @Positive(message = "{AREA_NOT_EMPTY}")
    private int areaId;

    @Schema(description = "手机或者邮箱注册需要验证码", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "9999")
    private String verifyCode;

    @Hidden
    private String registerIp;
    @Hidden
    private Integer terminal;
    @Hidden
    private Boolean demo;


    @AssertTrue(message = "账号/邮箱/手机号任意一个不能为空")
    @JsonIgnore
    public Boolean isAccountValid() {
        return StrUtil.isNotBlank(username) || StrUtil.isNotBlank(email) || StrUtil.isNotBlank(mobile);
    }


}
