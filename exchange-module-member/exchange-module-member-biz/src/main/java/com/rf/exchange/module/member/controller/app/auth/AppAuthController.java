package com.rf.exchange.module.member.controller.app.auth;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.servlet.ServletUtils;
import com.rf.exchange.framework.i18n.I;
import com.rf.exchange.framework.security.config.SecurityProperties;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.auth.vo.*;
import com.rf.exchange.module.member.service.auth.MemberAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.USER_ACCOUNT_NOT_EMPTY;

@Tag(name = "用户 APP - 认证")
@RestController
@RequestMapping("/member/auth")
@Validated
@Slf4j
public class AppAuthController {

    @Resource
    private MemberAuthService authService;
    @Resource
    private Validator validator;
    @Resource
    private SecurityProperties securityProperties;

    @PermitAll
    @PostMapping("/register")
    @Operation(summary = "注册")
    public CommonResult<Boolean> register(@RequestBody @Valid AppAuthRegisterReqVO reqVO) {
        if(!StringUtils.hasText(reqVO.getEmail()) && !StringUtils.hasText(reqVO.getMobile()) && !StringUtils.hasText(reqVO.getUsername())){
            return CommonResult.error(USER_ACCOUNT_NOT_EMPTY);
        }
        reqVO.setRegisterIp(ServletUtils.getClientIP());
        reqVO.setTerminal(WebFrameworkUtils.getTerminal());
        reqVO.setDemo(false);
        authService.register(reqVO);
        return success(true);
    }

    @PermitAll
    @PostMapping("/login")
    @Operation(summary = "登录")
    public CommonResult<AppAuthLoginRespVO> login(@RequestBody @Valid AppAuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token);
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<AppAuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

//    // ========== 短信登录相关 ==========
//    @PermitAll
//    @PostMapping("/mobile-register")
//    @Operation(summary = "手机账号注册")
//    public CommonResult<Boolean> mobileRegister(@RequestBody @Valid AppAuthRegisterMobileReqVO reqVO) {
//        authService.mobileRegister(reqVO);
//        return success(true);
//    }
//
//    @PermitAll
//    @PostMapping("/mobile-login")
//    @Operation(summary = "使用手机 + 密码登录")
//    public CommonResult<AppAuthLoginRespVO> mobileLogin(@RequestBody @Valid AppAuthMobileLoginReqVO reqVO) {
//        return success(authService.mobileLogin(reqVO));
//    }

    //@PostMapping("/sms-login")
    //@Operation(summary = "使用手机 + 验证码登录")
    //public CommonResult<AppAuthLoginRespVO> smsLogin(@RequestBody @Valid AppAuthSmsLoginReqVO reqVO) {
    //    return success(authService.smsCodeLogin(reqVO));
    //}

    @PermitAll
    @PostMapping("/send-sms-code")
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> sendSmsCode(@RequestBody @Valid AppSmsCodeSendReqVO reqVO) {
        authService.sendSmsCode(WebFrameworkUtils.getLoginUserId(),reqVO);
        return success(true);
    }
//    @PermitAll
//    @PostMapping("/validate-sms-code")
//    @Operation(summary = "校验手机验证码")
//    public CommonResult<Boolean> validateSmsCode(@RequestBody @Valid AppAuthSmsValidateReqVO reqVO) {
//        authService.validateSmsCode(getLoginUserId(), reqVO);
//        return success(true);
//    }

//    // ========== 邮箱登录相关 ==========
//    @PermitAll
//    @PostMapping("/email-register")
//    @Operation(summary = "邮箱账号注册")
//    public CommonResult<Boolean> mailRegister(@RequestBody @Valid AppAuthRegisterEmailReqVO reqVO) {
//        authService.mailRegister(reqVO);
//        return success(true);
//    }
//    @PermitAll
//    @PostMapping("/email-login")
//    @Operation(summary = "使用邮箱 + 密码登录")
//    public CommonResult<AppAuthLoginRespVO> emailLogin(@RequestBody @Valid AppAuthEmailLoginReqVO reqVO) {
//        return success(authService.emailLogin(reqVO));
//    }
    @PermitAll
    @PostMapping("/send-mail-code")
    @Operation(summary = "发送邮箱验证码")
    public CommonResult<Boolean> sendMailCode(@RequestBody @Valid AppMailCodeSendReqVO reqVO) {
        authService.sendMailCode(getLoginUserId(), reqVO, I.getLang(), getTenantId());
        return success(true);
    }

//    @PermitAll
//    @PostMapping("/validate-email-code")
//    @Operation(summary = "校验邮箱验证码")
//    public CommonResult<Boolean> validateEmailCode(@RequestBody @Valid AppAuthEmailValidateReqVO reqVO) {
//        authService.validateMailCode(getLoginUserId(), reqVO);
//        return success(true);
//    }
}
