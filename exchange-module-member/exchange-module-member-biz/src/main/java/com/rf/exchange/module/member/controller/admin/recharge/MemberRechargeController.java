//package com.rf.exchange.module.member.controller.admin.recharge;
//
//import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
//import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.rf.exchange.framework.common.pojo.PageParam;
//import com.rf.exchange.framework.common.pojo.PageResult;
//import com.rf.exchange.framework.common.pojo.CommonResult;
//import com.rf.exchange.framework.common.util.object.BeanUtils;
//import static com.rf.exchange.framework.common.pojo.CommonResult.success;
//
//import com.rf.exchange.framework.excel.core.util.ExcelUtils;
//
//import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
//import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;
//
//import com.rf.exchange.module.member.controller.admin.recharge.vo.*;
//import com.rf.exchange.module.member.dal.dataobject.recharge.MemberRechargeDO;
//import com.rf.exchange.module.member.service.recharge.MemberRechargeService;
//
//@Tag(name = "管理后台 - 会员充值")
//@RestController
//@RequestMapping("/member/recharge")
//@Validated
//public class MemberRechargeController {
//
//    @Resource
//    private MemberRechargeService rechargeService;
//
//
//    @GetMapping("/page")
//    @Operation(summary = "获得会员充值分页")
//    @PreAuthorize("@ss.hasPermission('member:recharge:query')")
//    public CommonResult<PageResult<MemberRechargeRespVO>> getRechargePage(@Valid MemberRechargePageReqVO pageReqVO) {
//        PageResult<MemberRechargeDO> pageResult = rechargeService.getRechargePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, MemberRechargeRespVO.class));
//    }
//
//
//    @GetMapping("/get")
//    @Operation(summary = "获得会员充值")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('member:recharge:query')")
//    public CommonResult<MemberRechargeRespVO> getRecharge(@RequestParam("id") Long id) {
//        MemberRechargeDO recharge = rechargeService.getRecharge(id);
//        return success(BeanUtils.toBean(recharge, MemberRechargeRespVO.class));
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除会员充值")
//    @Parameter(name = "id", description = "编号", required = true,example = "2")
//    @PreAuthorize("@ss.hasPermission('member:recharge:delete')")
//    public CommonResult<Boolean> deleteRecharge(@RequestParam("id") Long id) {
//        rechargeService.deleteRecharge(id);
//        return success(true);
//    }
//
//    @PostMapping("/handle")
//    @Operation(summary = "处理会员充值")
//    @PreAuthorize("@ss.hasPermission('member:recharge:handle')")
//    public CommonResult<Boolean> handle(@RequestBody @Valid MemberFundsRecordHandleReqVO reqVO){
//        rechargeService.handleRecharge(SecurityFrameworkUtils.getLoginUsername(),reqVO);
//        return success(true);
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出会员充值 Excel")
//    @PreAuthorize("@ss.hasPermission('member:recharge:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportRechargeExcel(@Valid MemberRechargePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<MemberRechargeDO> list = rechargeService.getRechargePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "会员充值.xls", "数据", MemberRechargeRespVO.class,
//                        BeanUtils.toBean(list, MemberRechargeRespVO.class));
//    }
//
//}