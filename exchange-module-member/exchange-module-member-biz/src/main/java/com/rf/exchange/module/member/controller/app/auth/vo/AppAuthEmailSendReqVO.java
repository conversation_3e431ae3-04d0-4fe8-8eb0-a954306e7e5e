//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.InEnum;
//import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.Email;
//import jakarta.validation.constraints.NotEmpty;
//import jakarta.validation.constraints.NotNull;
//import lombok.Data;
//import lombok.experimental.Accessors;
//
//@Schema(description = "用户 APP - 发送邮箱验证码 Request VO")
//@Data
//@Accessors(chain = true)
//public class AppAuthEmailSendReqVO {
//
//    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
//    @Email
//    private String email;
//
//    @Schema(description = "发送场景,对应 SmsSceneEnum 枚举", example = "1")
//    @NotNull(message = "发送场景不能为空")
//    @InEnum(MailSceneEnum.class)
//    private Integer scene;
//
//}
