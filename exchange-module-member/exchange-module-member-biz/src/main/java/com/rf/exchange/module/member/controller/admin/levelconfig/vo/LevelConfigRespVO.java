package com.rf.exchange.module.member.controller.admin.levelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员等级配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LevelConfigRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13321")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("等级")
    private Integer level;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图标")
    private String icon;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "默认等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("默认等级")
    private Boolean first;
}