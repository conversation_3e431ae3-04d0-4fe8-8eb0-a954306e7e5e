package com.rf.exchange.module.member.controller.admin.frozen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员冻结明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberFrozenRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5207")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12247")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("用户账号")
    private String username;

    @Schema(description = "冻结金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("冻结金额")
    private BigDecimal frozenAmount;

    @Schema(description = "冻结原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @ExcelProperty("冻结原因")
    private String frozenReason;

    @Schema(description = "关联单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关联单号")
    private String bizOrderNo;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}