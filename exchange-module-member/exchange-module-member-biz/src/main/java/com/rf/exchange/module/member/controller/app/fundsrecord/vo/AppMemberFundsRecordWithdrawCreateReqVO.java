package com.rf.exchange.module.member.controller.app.fundsrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 提现请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberFundsRecordWithdrawCreateReqVO  {

    @Schema(description = "钱包ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private Long walletId;

    @Schema(description = "提现法币金额")
    @NotEmpty(message = "{ARG_VALUE_ERROR}")
    private String amount;

    @Schema(description = "法币金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.5")
    @Positive(message = "{WITHDRAW_AMOUNT_ERROR}")
    private String legalAmount;

    @Schema(description = "法币汇率")
    @NotEmpty(message = "{ARG_VALUE_ERROR}")
    private String currencyRate;

    @Schema(description = "提现金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "123456789")
    @NotEmpty(message = "{FUNDS_PASSWORD_ERROR}")
    private String fundPassword;
}
