package com.rf.exchange.module.member.controller.admin.frozen.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员冻结明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberFrozenPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "12247")
    private Long userId;

    @Schema(description = "用户账号", example = "李四")
    private String username;

    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;

    @Schema(description = "冻结原因", example = "不好")
    private String frozenReason;

    @Schema(description = "关联单号")
    private String bizOrderNo;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}