package com.rf.exchange.module.member.convert.auth;

import com.rf.exchange.module.member.controller.app.auth.vo.*;
import com.rf.exchange.module.member.controller.app.user.vo.AppMemberUserResetPasswordReqVO;
import com.rf.exchange.module.system.api.mail.dto.MailCodeSendReqDTO;
import com.rf.exchange.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import com.rf.exchange.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.rf.exchange.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.rf.exchange.module.system.enums.sms.SmsSceneEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

//    SmsCodeSendReqDTO convert(AppAuthSmsSendReqVO reqVO);
//    SmsCodeUseReqDTO convert(AppAuthSmsLoginReqVO reqVO, Integer scene, String usedIp);
//
//    SmsCodeValidateReqDTO convert(AppAuthSmsValidateReqVO bean);
//
//    MailCodeSendReqDTO convert(AppAuthEmailSendReqVO reqVO);
//    MailCodeSendReqDTO convert(AppAuthEmailLoginReqVO reqVO);
//
//    MailCodeValidateReqDTO convert(AppAuthEmailValidateReqVO reqVO);

    SmsCodeUseReqDTO convert(AppMemberUserResetPasswordReqVO reqVO, SmsSceneEnum scene, String usedIp);
    AppAuthLoginRespVO convert(OAuth2AccessTokenRespDTO bean);

    SmsCodeSendReqDTO convert(AppSmsCodeSendReqVO reqVO);

    MailCodeSendReqDTO convert(AppMailCodeSendReqVO reqVO);
}
