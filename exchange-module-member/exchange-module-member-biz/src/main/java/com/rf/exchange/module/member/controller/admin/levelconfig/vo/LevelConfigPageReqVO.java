package com.rf.exchange.module.member.controller.admin.levelconfig.vo;

import lombok.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 会员等级配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LevelConfigPageReqVO extends PageParam {


    @Schema(description = "名称", example = "张三")
    private String name;

}