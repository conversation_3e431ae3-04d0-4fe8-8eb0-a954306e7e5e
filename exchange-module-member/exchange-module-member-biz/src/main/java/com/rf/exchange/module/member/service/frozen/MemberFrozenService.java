package com.rf.exchange.module.member.service.frozen;

import jakarta.validation.*;
import com.rf.exchange.module.member.controller.admin.frozen.vo.*;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 会员冻结明细 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberFrozenService {

    /**
     * 获得会员冻结明细分页
     *
     * @param pageReqVO 分页查询
     * @return 会员冻结明细分页
     */
    PageResult<MemberFrozenDO> getFrozenPage(MemberFrozenPageReqVO pageReqVO);

    List<MemberFrozenDO> getListByUserId(Long userId);

    Long insert(MemberFrozenDO memberFrozenDO);

    void deleteById(Long id);
}