package com.rf.exchange.module.member.controller.admin.assetsspot;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.assetsspot.vo.*;
import com.rf.exchange.module.member.dal.dataobject.assetsspot.MemberAssetsSpotDO;
import com.rf.exchange.module.member.service.assetsspot.MemberAssetsSpotService;

@Tag(name = "管理后台 - 会员现货资产")
@RestController
@RequestMapping("/member/assets-spot")
@Validated
public class MemberAssetsSpotController {

    @Resource
    private MemberAssetsSpotService assetsSpotService;


    @GetMapping("/page")
    @Operation(summary = "获得会员现货资产分页")
    @PreAuthorize("@ss.hasPermission('member:assets-spot:query')")
    public CommonResult<PageResult<MemberAssetsSpotRespVO>> getAssetsSpotPage(@Valid MemberAssetsSpotPageReqVO pageReqVO) {
        PageResult<MemberAssetsSpotDO> pageResult = assetsSpotService.getAssetsSpotPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberAssetsSpotRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员现货资产")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:assets-spot:query')")
    public CommonResult<MemberAssetsSpotRespVO> getAssetsSpot(@RequestParam("id") Long id) {
        MemberAssetsSpotDO assetsSpot = assetsSpotService.getAssetsSpot(id);
        return success(BeanUtils.toBean(assetsSpot, MemberAssetsSpotRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员现货资产 Excel")
    @PreAuthorize("@ss.hasPermission('member:assets-spot:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssetsSpotExcel(@Valid MemberAssetsSpotPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberAssetsSpotDO> list = assetsSpotService.getAssetsSpotPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员现货资产.xls", "数据", MemberAssetsSpotRespVO.class,
                        BeanUtils.toBean(list, MemberAssetsSpotRespVO.class));
    }

}