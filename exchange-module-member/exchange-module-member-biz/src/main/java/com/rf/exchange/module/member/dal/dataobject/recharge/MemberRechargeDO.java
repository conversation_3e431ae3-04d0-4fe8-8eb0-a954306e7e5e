package com.rf.exchange.module.member.dal.dataobject.recharge;

import lombok.*;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员充值 DO
 *
 * <AUTHOR>
 */
@TableName("member_recharge")
@KeySequence("member_recharge_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberRechargeDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 币种
     */
    private Long currencyId;
    /**
     * 币种名称
     */
    private String currencyName;
    /**
     * 法币金额
     */
    private BigDecimal currencyAmount;
    /**
     * 到账法币汇率
     */
    private BigDecimal currencyRate;
    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;
    /**
     * 到账金额
     */
    private BigDecimal arrivalAmount;
    /**
     * 实付金额
     */
    private BigDecimal payAmount;
    /**
     * 支付方式，虚拟/银行
     */
    private Integer payMethod;
    /**
     * 订单用户状态
     */
    private Integer orderMemberStatus;
    /**
     * 订单系统状态
     */
    private Integer orderSystemStatus;
    /**
     * 会员备注
     */
    private String memberRemark;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理时间
     */
    private Long handleTime;
    /**
     * 系统备注
     */
    private String systemRemark;

    private Long agentId;
    private String agentName;
}