package com.rf.exchange.module.member.service.user;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.security.core.LoginUser;
import com.rf.exchange.module.member.api.user.dto.MemberUserTotalRespVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFrozenUpdateReqVO;
import com.rf.exchange.module.member.controller.admin.user.vo.*;
import com.rf.exchange.module.member.controller.app.auth.vo.AppAuthRegisterReqVO;
import com.rf.exchange.module.member.controller.app.user.vo.*;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.enums.user.UserPwdWrongType;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 会员用户 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberUserService {

    /**
     * 检查用户是否存在
     *
     * @param userId 用户id
     * @return 用户信息
     */
    MemberUserDO checkUserExists(Long userId);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    MemberUserDO getUser(Long id);

    /**
     * 通过用户 ID 获取用户详情
     *
     * @param id 用户id
     * @return 用户信息
     */
    MemberUserRespVO getUserAndConfig(Long id);

    /**
     * 通过账号获取会员
     *
     * @param account 账号
     * @return 会员信息
     */
    MemberUserDO getUser(String account);

    /**
     * 通过邮件获取会员
     *
     * @param email 电子邮件
     * @return 会员信息
     */
    MemberUserDO getUserByEmail(String email);
    //    /**
    //     * 通过账号查询用户
    //     *
    //     * @param account 账号
    //     * @return 用户对象
    //     */
    //    MemberUserDO getUserByAccount(String account);
    //
    //    /**
    //     * 通过手机查询用户
    //     *
    //     * @param mobile 手机
    //     * @return 用户对象
    //     */
    //    MemberUserDO getUserByMobile(String mobile);
    //
    //    /**
    //     * 通过邮箱查询用户
    //     * @param email 邮箱
    //     * @return 用户对象
    //     */
    //    MemberUserDO getUserByEmail(String email);

    /**
     * 通过用户 ID 查询用户们
     *
     * @param ids 用户 ID
     * @return 用户对象信息数组
     */
    List<MemberUserDO> getUserList(Collection<Long> ids);

    //    /**
    //     * 基于用户昵称，模糊匹配用户列表
    //     *
    //     * @param nickname 用户昵称，模糊匹配
    //     * @return 用户信息的列表
    //     */
    //    List<MemberUserDO> getUserListByNickname(String nickname);

    /**
     * 用户注册
     *
     * @param reqVO 注册信息
     * @return 用户信息
     */
    MemberUserDO register(AppAuthRegisterReqVO reqVO);

    //    /**
    //     * 基于账号密码创建用户
    //     * 如果用户已经存在，则直接返回
    //     *
    //     * @param account    账号
    //     * @param password   密码
    //     * @param registerIp 注册 IP
    //     * @param terminal   终端 {@link TerminalEnum}
    //     * @return 用户对象
    //     */
    //    MemberUserDO createUserWithAccount(Area area, String agentCode, String account, String password, String registerIp, Integer terminal);
    //
    //    /**
    //     * 基于手机号创建用户。
    //     * 如果用户已经存在，则直接进行返回
    //     *
    //     * @param mobile     手机号
    //     * @param registerIp 注册 IP
    //     * @param terminal   终端 {@link TerminalEnum}
    //     * @return 用户对象
    //     */
    //    MemberUserDO createUserWithMobile(Area area,String agentCode,@Mobile String mobile, String password, String registerIp, Integer terminal);

    //    /**
    //     * 基于手机号创建用户。
    //     * 如果用户已经存在，则直接进行返回
    //     *
    //     * @param mobile     手机号
    //     * @param registerIp 注册 IP
    //     * @param terminal   终端 {@link TerminalEnum}
    //     * @return 用户对象
    //     */
    //    MemberUserDO createUserWithMobileWithoutPasswordIfAbsent(Area area,String agentCode,@Mobile String mobile, String registerIp, Integer terminal);
    //
    //    /**
    //     * 使用邮箱+密码创建用户
    //     *
    //     * @param email      邮箱
    //     * @param password   密码
    //     * @param registerIp 注册 IP
    //     * @param terminal   终端 {@link TerminalEnum}
    //     * @return 用户对象
    //     */
    //    MemberUserDO createUserWithEmail(Area area,String agentCode,@Email String email, String password, String registerIp, Integer terminal);

    ///**
    // * 创建用户
    // * 目的：三方登录时，如果未绑定用户时，自动创建对应用户
    // *
    // * @param nickname   昵称
    // * @param avtar      头像
    // * @param registerIp 注册 IP
    // * @param terminal   终端 {@link TerminalEnum}
    // * @return 用户对象
    // */
    //MemberUserDO createUser(String nickname, String avtar, String registerIp, Integer terminal);

    /**
     * 更新用户的最后登陆信息
     *
     * @param id      用户编号
     * @param loginIp 登陆 IP
     */
    void updateUserLogin(Long id, String loginIp);

    /**
     * 【会员】修改基本信息
     *
     * @param userId 用户编号
     * @param reqVO  基本信息
     */
    void updateUser(Long userId, AppMemberUserUpdateReqVO reqVO);

    /**
     * 【会员】修改手机，基于手机验证码
     *
     * @param userId 用户编号
     * @param reqVO  请求信息
     */
    void updateUserMobile(Long userId, AppMemberUserUpdateMobileReqVO reqVO);

    /**
     * 【会员】修改邮箱，基于邮箱验证码
     *
     * @param userId 用户编号
     * @param reqVO  请求信息
     */
    void updateUserMail(Long userId, AppMemberUserUpdateMailReqVO reqVO);

    /**
     * 【会员】修改密码
     *
     * @param userId 用户编号
     * @param reqVO  请求信息
     */
    void updateUserPassword(Long userId, AppMemberUserUpdatePasswordReqVO reqVO);

    /**
     * 设置会员密码
     *
     * @param userId   用户编号
     * @param password 密码
     */
    void setUserPassword(Long userId, String password);

    /**
     * 【会员】忘记密码
     *
     * @param reqVO 请求信息
     */
    void resetUserPassword(AppMemberUserResetPasswordReqVO reqVO);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword     未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 【管理员】更新会员用户
     *
     * @param updateReqVO 更新信息
     */
    void updateUser(MemberUserUpdateReqVO updateReqVO);

    /**
     * 【管理员】删除会员用户
     *
     * @param id 会员id
     */
    void deleteUser(Long id);

    /**
     * 【管理员】获得会员用户分页
     *
     * @param pageReqVO 分页查询
     * @return 会员用户分页
     */
    PageResult<MemberUserDO> getUserPage(MemberUserPageReqVO pageReqVO);

    /**
     * 修改用户余额
     *
     * @param reqVO 请求信息
     */
    void updateUserBalance(String handler, MemberUserUpdateBalanceReqVO reqVO);

    /**
     * 更新用户的父代理
     *
     * @param loginUser 登录用户
     * @param reqVO     请求信息
     */
    void updateUserParentAgent(LoginUser loginUser, MemberUserUpdateAgentReqVO reqVO);

    /**
     * 设置资金密码
     *
     * @param userId       会员id
     * @param fundPassword 资金密码
     */
    void setFundPassword(Long userId, String fundPassword);

    /**
     * 修改资金密码
     *
     * @param userId 会员id
     * @param reqVO  请求信息
     */
    void updateFundPassword(Long userId, AppMemberUserUpdatePasswordReqVO reqVO);

    /**
     * 统计代理的会员余额
     *
     * @return 会员列表
     */
    List<MemberUserDO> getAllMemberBalance();

    /**
     * 获取代理id下的所有会员
     *
     * @param agentId       代理id
     * @param descendantIds
     * @return 会员列表
     */
    List<MemberUserDO> getAgentUserList(Long agentId, Collection<Long> descendantIds);

    /**
     * 通过代理id统计
     *
     * @param agentId
     * @return
     */
    MemberUserTotalRespVO totalByAgentIdList(List<Long> agentId);

    /**
     * 增加用户密码错误次数
     *
     * @param tenantId  租户id
     * @param userId    用户id
     * @param wrongType 错误类型
     */
    void incrPwdWrongCount(Long tenantId, Long userId, UserPwdWrongType wrongType);

    /**
     * 用户的密码错误次数是否超过阀值
     *
     * @param tenantId 租户id
     * @param userId   用户
     * @return true:超过 false:没有
     */
    boolean isPwdWrongOverCount(Long tenantId, Long userId);

    /**
     * 重置用户的密码错误次数
     *
     * @param userId 用户id
     */
    void resetPasswordWrongCount(Long userId);

    /**
     * 获取租户下的所有试玩用户
     * <p>
     * 如果当前租户是超级管理员的话则忽略租户，获取所有的试玩用户
     *
     * @return 试玩用户信息列表
     */
    List<MemberUserDO> getDemoUserList();

    /**
     * 更新会员用户冻结金额
     *
     * @param reqVO 更新信息
     */
    void updateFrozenFundsRecord(@Valid MemberFrozenUpdateReqVO reqVO);
}
