package com.rf.exchange.module.member.service.transactions;

import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.controller.app.transactions.vo.AppMemberTransactionsPageReqVO;
import com.rf.exchange.module.member.controller.admin.transactions.vo.*;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 会员账变 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberTransactionsService {

    /**
     * 获得会员账变分页
     *
     * @param pageReqVO 分页查询
     * @return 会员账变分页
     */
    PageResult<MemberTransactionsDO> getTransactionsPage(MemberTransactionsPageReqVO pageReqVO);

    PageResult<MemberTransactionsDO> getTransactionsPage(Long userId, AppMemberTransactionsPageReqVO pageReqVO);

    /**
     * 获得会员账变分页（排除充值和提现类型）
     *
     * @param userId 用户ID
     * @param pageReqVO 分页查询
     * @return 会员账变分页
     */
    PageResult<MemberTransactionsDO> getTransactionsPageExcludeRechargeWithdraw(Long userId, AppMemberTransactionsPageReqVO pageReqVO);

    //void insert(MemberTransactionsDO memberTransactionsDO);

    /**
     * 保存会员的账变信息
     *
     * @param requestVO 帐变信息
     */
    MemberTransactionsDO saveTransaction(MemberTransactionSaveReqVO requestVO);

    /**
     * 获取时间范围内会员的最后一条账变信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<MemberTransactionsDO> getLastTransactionByRange(Long startTime, Long endTime);

    /**
     * 通过关联订单获取账变信息
     *
     * @param orderNo 关联订单号
     * @return 账变信息
     */
    List<MemberTransactionsDO> getTransactionsByRelationOrderNo(String orderNo);
}