//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.Mobile;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.validator.constraints.Length;
//
//import jakarta.validation.constraints.NotEmpty;
//import jakarta.validation.constraints.Pattern;
//
//@Schema(description = "用户 APP - 手机 + 验证码登录 Request VO,如果登录并绑定社交用户，需要传递 social 开头的参数")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthSmsLoginReqVO {
//
//    @Schema(description = "国家id", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    private String areaId;
//
//    @Schema(description = "邀请码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    private String icode;
//
//    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
//    @NotEmpty(message = "手机号不能为空")
//    @Mobile
//    private String mobile;
//
//    @Schema(description = "手机验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
//    @NotEmpty(message = "手机验证码不能为空")
//    @Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
//    @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
//    private String code;
//}