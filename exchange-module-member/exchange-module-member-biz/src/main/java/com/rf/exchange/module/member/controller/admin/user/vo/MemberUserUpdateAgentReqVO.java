package com.rf.exchange.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024-10-11
 */
@Data
@Schema(description = "管理后台 - 会员用户 Response VO")
@ToString(callSuper = true)
public class MemberUserUpdateAgentReqVO {

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "父代理id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long parentAgentId;
}
