//package com.rf.exchange.module.member.service.recharge;
//
//import com.baomidou.dynamic.datasource.annotation.DSTransactional;
//import com.baomidou.dynamic.datasource.annotation.Master;
//import com.baomidou.dynamic.datasource.annotation.Slave;
//import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
//import com.rf.exchange.framework.common.util.date.DateUtils;
//import com.rf.exchange.framework.common.util.order.OrderUtil;
//import com.rf.exchange.framework.i18n.message.MessageConstants;
//import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
//import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRechargeCreateReqVO;
//import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordPageReqVO;
//import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
//import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
//import com.rf.exchange.module.member.dal.mysql.transactions.MemberTransactionsMapper;
//import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
//import com.rf.exchange.module.member.enums.recharge.MemberRechargeStatusEnum;
//import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
//import com.rf.exchange.module.system.api.currency.CurrencyApi;
//import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
//import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
//import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import jakarta.annotation.Resource;
//
//import com.rf.exchange.module.member.controller.admin.recharge.vo.*;
//import com.rf.exchange.module.member.dal.dataobject.recharge.MemberRechargeDO;
//import com.rf.exchange.framework.common.pojo.PageResult;
//
//import com.rf.exchange.module.member.dal.mysql.recharge.MemberRechargeMapper;
//
//import java.math.BigDecimal;
//import java.util.Collection;
//import java.util.Optional;
//
//import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
//import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_EXISTS;
//
///**
// * 会员充值 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Slf4j
//public class MemberRechargeServiceImpl implements MemberRechargeService {
//    @Resource
//    private MemberRechargeMapper memberRechargeMapper;
//    @Resource
//    private MemberUserMapper memberUserMapper;
//    @Resource
//    private CurrencyApi currencyApi;
//    @Resource
//    private MemberTransactionsMapper transactionsMapper;
//    @Resource
//    private CurrencyRateApi currencyRateApi;
//
//    @Override
//    @Master
//    public Long createRecharge(Long userId, AppMemberFundsRecordRechargeCreateReqVO createReqVO) {
//        //沒有餘額用戶就不存在
//        MemberUserDO userDO = memberUserMapper.selectById(userId);
//        if (userDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//
//        CurrencyBaseRespDTO currency = currencyApi.getCurrency(createReqVO.getCurrencyCode());
//        if (currency == null) {
//            throw exception(CURRENCY_NOT_EXISTS);
//        }
//        // 插入
//        MemberRechargeDO recharge = new MemberRechargeDO();
//        recharge.setOrderNo(OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.RECHARGE));
//        recharge.setUserId(userDO.getId());
//        recharge.setUsername(userDO.getUsername());
//        recharge.setCurrencyId(currency.getId());
//        recharge.setCurrencyName(currency.getName());
//        recharge.setRechargeAmount(createReqVO.getAmount());
//        recharge.setPayMethod(createReqVO.getPayMethod());
//        recharge.setOrderSystemStatus(MemberRechargeStatusEnum.WAIT_HANDLE.getValue());
//        recharge.setOrderMemberStatus(MemberRechargeStatusEnum.WAIT_HANDLE.getValue());
//        Collection<CurrencyRateDTO> rateList = currencyRateApi.getTenantCurrencyRateList(userDO.getTenantId());
//        Optional<CurrencyRateDTO> rate = rateList.stream().filter(r -> r.getQuoteCurrency().equals(createReqVO.getCurrencyCode())).findFirst();
//        if (rate.isPresent()) {
//            recharge.setCurrencyRate(rate.get().getRate());
//            recharge.setCurrencyAmount(recharge.getCurrencyRate().multiply(recharge.getRechargeAmount()));
//        }
//        memberRechargeMapper.insert(recharge);
//        // 返回
//        return recharge.getId();
//    }
//
//    @Override
//    @Master
//    public void deleteRecharge(Long id) {
//        // 校验存在
//        //validateRechargeExists(id);
//        // 删除
//        memberRechargeMapper.deleteById(id);
//    }
//
//    private void validateRechargeExists(Long id) {
//        if (memberRechargeMapper.selectById(id) == null) {
//            throw exception(USER_RECHARGE_NOT_EXISTS);
//        }
//    }
//
//    @Override
//    @Slave
//    public MemberRechargeDO getRecharge(Long id) {
//        return memberRechargeMapper.selectById(id);
//    }
//
//    @Override
//    @Slave
//    public PageResult<MemberRechargeDO> getRechargePage(MemberRechargePageReqVO pageReqVO) {
//        return memberRechargeMapper.selectPage(pageReqVO);
//    }
//
//    @Override
//    @Slave
//    public PageResult<MemberRechargeDO> getRechargePage(Long userId, AppMemberFundsRecordPageReqVO pageReqVO) {
//        return memberRechargeMapper.selectPage(userId, pageReqVO);
//    }
//
//    @Override
//    @Master
//    @DSTransactional(rollbackFor = Exception.class)
//    public void handleRecharge(String handler, MemberFundsRecordHandleReqVO reqVO) {
//        //验证充值记录存在
//        MemberRechargeDO rechargeDO = memberRechargeMapper.selectByIdForUpdate(reqVO.getId());
//        if (rechargeDO == null) {
//            throw exception(USER_RECHARGE_NOT_EXISTS);
//        }
//        if (rechargeDO.getOrderSystemStatus() != MemberRechargeStatusEnum.WAIT_HANDLE.getValue()) {
//            throw exception(USER_RECHARGE_HAS_HANDLE);
//        }
//        //验证用户存在
//        MemberUserDO userDO = memberUserMapper.selectByIdForUpdate(rechargeDO.getUserId());
//        if (userDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//        //保存处理结果
//        rechargeDO.setArrivalAmount(reqVO.getArrivalAmount());
//        rechargeDO.setPayAmount(reqVO.getPayAmount());
//        rechargeDO.setPayMethod(reqVO.getPayMethod());
//        rechargeDO.setOrderMemberStatus(reqVO.getMemberStatus());
//        rechargeDO.setOrderSystemStatus(reqVO.getSystemStatus());
//        rechargeDO.setCurrencyRate(reqVO.getCurrencyRate());
//        rechargeDO.setCurrencyAmount(rechargeDO.getCurrencyRate().multiply(rechargeDO.getArrivalAmount()));
//        rechargeDO.setSystemRemark(reqVO.getRemark());
//        rechargeDO.setHandler(handler);
//        rechargeDO.setHandleTime(DateUtils.getUnixTimestampNow());
//        memberRechargeMapper.updateById(rechargeDO);
//
//        CurrencyBaseRespDTO currency = currencyApi.getCurrency(rechargeDO.getCurrencyId());
//        MemberRechargeStatusEnum systemStatus = MemberRechargeStatusEnum.get(reqVO.getSystemStatus());
//        switch (systemStatus) {
//            case SUCCESS -> {
//                //记录账变
//                BigDecimal beforeBalance = userDO.getBalance();
//                BigDecimal afterbalance = userDO.getBalance().add(rechargeDO.getArrivalAmount());
//                MemberTransactionsDO transactionsDO = new MemberTransactionsDO();
//                transactionsDO.setUserId(userDO.getId());
//                transactionsDO.setUsername(userDO.getUsername());
//                transactionsDO.setType(MemberTransactionsTypeEnum.RECHARGE.getValue());
//                transactionsDO.setAmount(rechargeDO.getArrivalAmount());
//                transactionsDO.setCurrencyId(rechargeDO.getCurrencyId());
//                transactionsDO.setCurrencyName(rechargeDO.getCurrencyName());
//                transactionsDO.setCurrencySymbol(currency.getSymbol());
//                transactionsDO.setCurrencyRate(rechargeDO.getCurrencyRate());
//                transactionsDO.setCurrencyAmount(rechargeDO.getCurrencyRate().multiply(rechargeDO.getArrivalAmount()));
//                transactionsDO.setBizOrderNo(rechargeDO.getOrderNo());
//                transactionsDO.setRemark(MessageConstants.RECHARGE_SUCCESS);
//                transactionsDO.setBeforeBalance(beforeBalance);
//                transactionsDO.setAfterBalance(afterbalance);
//                transactionsMapper.insert(transactionsDO);
//
//                //修改用户余额
//                memberUserMapper.updateBalanceIncr(userDO, rechargeDO.getArrivalAmount(), false, true);
//                //增加会员的充值金额
//
//            }
//            case BACK -> {
//
//            }
//            case PENDING -> {
//
//            }
//        }
//
//    }
//}