package com.rf.exchange.module.member.controller.admin.total.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员日统计数据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberTotalDayRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21309")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "代理", requiredMode = Schema.RequiredMode.REQUIRED, example = "8969")
    @ExcelProperty("代理")
    private Long agentId;

    @Schema(description = "代理", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("代理")
    private String agentName;

    @Schema(description = "会员", requiredMode = Schema.RequiredMode.REQUIRED, example = "9485")
    @ExcelProperty("会员")
    private Long userId;

    @Schema(description = "会员", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("会员")
    private String username;

    @Schema(description = "归属时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("归属时间")
    private Long totalTime;

    @Schema(description = "余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("余额")
    private BigDecimal usdtBalance;

    @Schema(description = "盈利", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("盈利")
    private BigDecimal usdtProfitAmount;

    @Schema(description = "单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    @ExcelProperty("单数")
    private Integer usdtOrderCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;
    @Schema(description = "充值", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    private BigDecimal recharge;
    @Schema(description = "提现", requiredMode = Schema.RequiredMode.REQUIRED, example = "4713")
    private BigDecimal withdraw;
}