package com.rf.exchange.module.member.controller.app.transactions;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.transactions.vo.AppMemberTransactionsPageReqVO;
import com.rf.exchange.module.member.controller.app.transactions.vo.AppMemberTransactionsRespVO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.service.transactions.MemberTransactionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 用户账变")
@RestController
@RequestMapping("/member/transaction")
@Validated
@Slf4j
public class AppMemberTransactionController {

   @Resource
   private MemberTransactionsService memberTransactionsService;
   @GetMapping("/page")
   @Operation(summary = "获得会员账变分页（排除充值和提现类型）")
   @PreAuthenticated
   public CommonResult<PageResult<MemberTransactionsDO>> getTransactionsPage(@Valid AppMemberTransactionsPageReqVO pageReqVO) {
       PageResult<MemberTransactionsDO> pageResult = memberTransactionsService.getTransactionsPageExcludeRechargeWithdraw(WebFrameworkUtils.getLoginUserId(),pageReqVO);
       return success(BeanUtils.toBean(pageResult, MemberTransactionsDO.class));
   }

}
