package com.rf.exchange.module.member.controller.admin.fundsrecord.vo;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;

import java.math.BigDecimal;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 处理会员充值")
@Data
@ToString(callSuper = true)
@Builder
public class MemberFundsRecordHandleReqVO {

    @Schema(description = "记录编号", example = "1")
    @Positive
    private Long id;
    @Schema(description = "处理备注", example = "",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String systemRemark;
    @Schema(description = "法币汇率", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal currencyRate;
//    @Schema(description = "会员状态,1成功，2驳回，3挂起", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
//    @InEnum(MemberFundsRecordStatusEnum.class)
//    @Positive(message = "会员状态错误")
//    private Integer memberStatus;
    @Schema(description = "系统状态, 0待处理, 1成功, 2驳回, 3挂起", example = "",requiredMode = Schema.RequiredMode.REQUIRED)
    @InEnum(MemberFundsRecordStatusEnum.class)
    @PositiveOrZero(message = "系统状态错误")
    private Integer systemStatus;
}
