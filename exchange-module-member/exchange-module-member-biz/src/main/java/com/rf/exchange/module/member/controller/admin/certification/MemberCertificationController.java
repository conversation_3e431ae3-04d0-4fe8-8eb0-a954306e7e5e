package com.rf.exchange.module.member.controller.admin.certification;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.module.member.controller.admin.certification.vo.*;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;
import com.rf.exchange.module.member.service.certification.MemberCertificationService;
import com.rf.exchange.module.system.api.dict.DictDataApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUsername;

@Tag(name = "管理后台 - 会员身份认证")
@RestController
@RequestMapping("/member/certification")
@Validated
public class MemberCertificationController {

    @Resource
    private MemberCertificationService certificationService;
    @Resource
    private DictDataApi dictDataApi;

    @PostMapping("/create")
    @Operation(summary = "添加会员身份认证")
    @PreAuthorize("@ss.hasPermission('member:certification:create')")
    public CommonResult<Boolean> createCertification(@Valid @RequestBody MemberCertificationManualSaveReqVO reqVO) {
        certificationService.createCertification(reqVO, getLoginUserId(), getLoginUsername());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员身份认证")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:certification:query')")
    public CommonResult<MemberCertificationRespVO> getCertification(@RequestParam("id") Long id) {
        MemberCertificationDO certification = certificationService.getCertification(id);
        return success(BeanUtils.toBean(certification, MemberCertificationRespVO.class));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员身份认证")
    @PreAuthorize("@ss.hasPermission('member:certification:update')")
    public CommonResult<Boolean> updateCertification(@Valid @RequestBody MemberCertificationSaveReqVO updateReqVO) {
        certificationService.updateCertification(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员身份认证分页")
    @PreAuthorize("@ss.hasPermission('member:certification:query')")
    public CommonResult<PageResult<MemberCertificationRespVO>> getCertificationPage(@Valid MemberCertificationPageReqVO pageReqVO) {
        PageResult<MemberCertificationDO> pageResult = certificationService.getCertificationPage(pageReqVO);
        PageResult<MemberCertificationRespVO> list=BeanUtils.toBean(pageResult, MemberCertificationRespVO.class);
        dictDataApi.fillS3Host(list.getList(),MemberCertificationRespVO.class);
        return success(list);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员身份认证")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:certification:delete')")
    public CommonResult<Boolean> deleteCertification(@RequestParam("id") Long id) {
        certificationService.deleteCertification(id);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员身份认证 Excel")
    @PreAuthorize("@ss.hasPermission('member:certification:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCertificationExcel(@Valid MemberCertificationPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberCertificationDO> list = certificationService.getCertificationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员身份认证.xls", "数据", MemberCertificationRespVO.class,
                BeanUtils.toBean(list, MemberCertificationRespVO.class));
    }

    @PostMapping("/handle")
    @Operation(summary = "处理会员认证")
    @PreAuthorize("@ss.hasPermission('member:certification:handle')")
    public CommonResult<Boolean> handle(@RequestBody @Valid MemberCertificationHandleReqVO reqVO) {
        certificationService.handle(getLoginUsername(), reqVO);
        return success(true);
    }

    @PostMapping("/manual-cert")
    @Operation(summary = "手动认证会员")
    @PreAuthorize("@ss.hasPermission('member:certifcation:update')")
    public CommonResult<Boolean> manualCert(@Valid @RequestBody MemberCertificationManualSaveReqVO reqVO) {
        certificationService.manualAddCertification(reqVO, getLoginUserId(), getLoginUsername());
        return success(true);
    }
}