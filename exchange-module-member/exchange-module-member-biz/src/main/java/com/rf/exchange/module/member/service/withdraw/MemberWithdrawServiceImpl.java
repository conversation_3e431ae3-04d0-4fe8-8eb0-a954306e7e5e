//package com.rf.exchange.module.member.service.withdraw;
//
//import cn.hutool.core.util.NumberUtil;
//import com.baomidou.dynamic.datasource.annotation.DSTransactional;
//import com.baomidou.dynamic.datasource.annotation.Master;
//import com.baomidou.dynamic.datasource.annotation.Slave;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.rf.exchange.framework.common.enums.OrderNoTypeEnum;
//import com.rf.exchange.framework.common.util.date.DateUtils;
//import com.rf.exchange.framework.common.util.object.BeanUtils;
//import com.rf.exchange.framework.common.util.order.OrderUtil;
//import com.rf.exchange.framework.i18n.message.MessageConstants;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawCreateReqVO;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawPageReqVO;
//import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
//import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
//import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
//import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
//import com.rf.exchange.module.member.dal.mysql.frozen.MemberFrozenMapper;
//import com.rf.exchange.module.member.dal.mysql.transactions.MemberTransactionsMapper;
//import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
//import com.rf.exchange.module.member.dal.mysql.wallet.MemberWalletMapper;
//import com.rf.exchange.module.member.enums.DictTypeConstants;
//import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
//import com.rf.exchange.module.member.enums.withdraw.MemberWithdrawStatusEnum;
//import com.rf.exchange.module.system.api.currency.CurrencyApi;
//import com.rf.exchange.module.system.api.currency.dto.CurrencyBaseRespDTO;
//import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
//import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
//import com.rf.exchange.module.system.api.tenantdict.TenantDictDataApi;
//import com.rf.exchange.module.system.api.tenantdict.dto.TenantDictDataRespDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.stereotype.Service;
//import jakarta.annotation.Resource;
//
//import com.rf.exchange.module.member.controller.admin.withdraw.vo.*;
//import com.rf.exchange.module.member.dal.dataobject.withdraw.MemberWithdrawDO;
//import com.rf.exchange.framework.common.pojo.PageResult;
//
//import com.rf.exchange.module.member.dal.mysql.withdraw.MemberWithdrawMapper;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;
//import static com.rf.exchange.module.system.enums.ErrorCodeConstants.CURRENCY_NOT_EXISTS;
//
///**
// * 会员提现 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Slf4j
//public class MemberWithdrawServiceImpl implements MemberWithdrawService {
//
//    @Resource
//    private MemberWithdrawMapper withdrawMapper;
//    @Resource
//    private MemberUserMapper memberUserMapper;
//    @Resource
//    private MemberWalletMapper memberWalletMapper;
//    @Resource
//    private MemberTransactionsMapper memberTransactionsMapper;
//    @Resource
//    private MemberFrozenMapper memberFrozenMapper;
//    @Resource
//    private TenantDictDataApi tenantDictDataApi;
//    @Resource
//    private PasswordEncoder passwordEncoder;
//    @Resource
//    private CurrencyRateApi currencyRateApi;
//    @Resource
//    private CurrencyApi currencyApi;
//
//    @Override
//    @Master
//    @DSTransactional(rollbackFor = Exception.class)
//    public Long createWithdraw(Long userId, AppMemberWithdrawCreateReqVO createReqVO) {
//        //验证用户存在
//        MemberUserDO userDO = memberUserMapper.selectByIdForUpdate(userId);
//        if (userDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//        //验证用户余额
//        if (userDO.getBalance().compareTo(createReqVO.getAmount()) < 0) {
//            throw exception(USER_BALANCE_NOT_ENOUGH);
//        }
//        //验证资金密码
//        boolean suc = isPasswordMatch(createReqVO.getFundPassword(), userDO.getFundPassword());
//        if (!suc) {
//            throw exception(USER_FUND_PASSWORD_ERROR);
//        }
//        List<TenantDictDataRespDTO> withdrawConfigList = tenantDictDataApi.getDictDataList(userDO.getTenantId(), DictTypeConstants.TENANT_WITHDRAW_CONFIG);
//        Map<String, String> withdrawConfigMap = withdrawConfigList.parallelStream().collect(
//                Collectors.toMap(TenantDictDataRespDTO::getLabel, p -> p.getValue()));
//        //最小提现金额
//        BigDecimal minWithdrawMoney = NumberUtil.toBigDecimal(withdrawConfigMap.get("min"));
//        if (minWithdrawMoney.compareTo(createReqVO.getAmount()) > 0) {
//            throw exception(USER_WITHDRAW_LESS_MIN_AMOUNT);
//        }
//        //计算提现手续费
//        BigDecimal feeAmount = BigDecimal.ZERO;
//        BigDecimal feeFix = NumberUtil.toBigDecimal(withdrawConfigMap.get("fee_fix"));
//
//        if (feeFix.compareTo(BigDecimal.ZERO) > 0) {
//            //按固定手续费
//            feeAmount = feeFix;
//        } else {
//            //按提现金额比例手续费
//            BigDecimal feeScale = NumberUtil.toBigDecimal(withdrawConfigMap.get("fee_scale"));
//            //提现最低手续费
//            BigDecimal feeMin = NumberUtil.toBigDecimal(withdrawConfigMap.get("fee_min"));
//            //提现最高手续费
//            BigDecimal feeMax = NumberUtil.toBigDecimal(withdrawConfigMap.get("fee_max"));
//            if (feeScale.compareTo(BigDecimal.ZERO) > 0) {
//                feeAmount = createReqVO.getAmount().multiply(feeScale);
//                if (feeMin.compareTo(BigDecimal.ZERO) > 0 && feeMin.compareTo(feeAmount) > 0) {
//                    feeAmount = feeMin;
//                }
//                if (feeMax.compareTo(BigDecimal.ZERO) > 0 && feeMax.compareTo(feeAmount) < 0) {
//                    feeAmount = feeMax;
//                }
//            }
//        }
//
//        //验证钱包存在
//        MemberWalletDO memberWalletDO = memberWalletMapper.selectListByUserIdAndWalletId(userId, createReqVO.getWalletId());
//        if (memberWalletDO == null) {
//            throw exception(USER_WALLET_NOT_EXISTS);
//        }
//        //生成单号
//        String orderNo = OrderUtil.generateOrderNumberSuffix4(OrderNoTypeEnum.WITHDRAW);
//        //添加冻结明细
//        MemberFrozenDO memberFrozenDO = new MemberFrozenDO();
//        memberFrozenDO.setUserId(userDO.getId());
//        memberFrozenDO.setUsername(userDO.getUsername());
//        memberFrozenDO.setFrozenAmount(createReqVO.getAmount());
//        memberFrozenDO.setFrozenReason(MessageConstants.WITHDRAW_APPLY);
//        memberFrozenDO.setBizOrderNo(orderNo);
//        memberFrozenMapper.insert(memberFrozenDO);
//
//        CurrencyBaseRespDTO currency = currencyApi.getCurrency(memberWalletDO.getCurrencyCode());
//        if (currency == null) {
//            throw exception(CURRENCY_NOT_EXISTS);
//        }
//
//        //插入提现记录
//        MemberWithdrawDO withdrawDO = new MemberWithdrawDO();
//        withdrawDO.setOrderNo(orderNo);
//        withdrawDO.setUserId(userDO.getId());
//        withdrawDO.setUsername(userDO.getUsername());
//        withdrawDO.setCurrencyId(memberWalletDO.getCurrencyId());
//        withdrawDO.setCurrencyName(currency.getName());
//        withdrawDO.setWithdrawAmount(createReqVO.getAmount());
//        withdrawDO.setWalletId(memberWalletDO.getId());
//        withdrawDO.setWalletName(memberWalletDO.getName());
//        withdrawDO.setWalletType(memberWalletDO.getType());
//        withdrawDO.setWalletTypeName(memberWalletDO.getTypeName());
//        withdrawDO.setWalletAccount(memberWalletDO.getAccount());
//        withdrawDO.setBankAddress(memberWalletDO.getBankAddress());
//        withdrawDO.setBankBranch(memberWalletDO.getBankBranch());
//        withdrawDO.setMemberRemark(createReqVO.getRemark());
//        withdrawDO.setOrderSystemStatus(MemberWithdrawStatusEnum.WAIT_HANDLE.getValue());
//        withdrawDO.setOrderMemberStatus(MemberWithdrawStatusEnum.WAIT_HANDLE.getValue());
//        withdrawDO.setFrozenId(memberFrozenDO.getId());
//        withdrawDO.setFeeAmount(feeAmount);
//        Collection<CurrencyRateDTO> rateList = currencyRateApi.getTenantCurrencyRateList(userDO.getTenantId());
//        Optional<CurrencyRateDTO> rate = rateList.stream().filter(r -> r.getQuoteCurrency().equals(memberWalletDO.getCurrencyCode())).findFirst();
//        if (rate.isPresent()) {
//            withdrawDO.setCurrencyRate(rate.get().getRate());
//            withdrawDO.setCurrencyAmount(withdrawDO.getCurrencyRate().multiply(withdrawDO.getWithdrawAmount()));
//        }
//        withdrawMapper.insert(withdrawDO);
//
//        //记录账变
//        BigDecimal beforeBalance = userDO.getBalance();
//        BigDecimal afterbalance = userDO.getBalance().subtract(createReqVO.getAmount());
//        MemberTransactionsDO transactionsDO = new MemberTransactionsDO();
//        transactionsDO.setUserId(userDO.getId());
//        transactionsDO.setUsername(userDO.getUsername());
//        transactionsDO.setType(MemberTransactionsTypeEnum.WITHDRAW.getValue());
//        transactionsDO.setAmount(createReqVO.getAmount());
//        transactionsDO.setCurrencyId(withdrawDO.getCurrencyId());
//        transactionsDO.setCurrencyName(withdrawDO.getCurrencyName());
//        transactionsDO.setCurrencySymbol(currency.getSymbol());
//        transactionsDO.setBizOrderNo(withdrawDO.getOrderNo());
//        transactionsDO.setRemark(MessageConstants.WITHDRAW_APPLY);
//        transactionsDO.setBeforeBalance(beforeBalance);
//        transactionsDO.setAfterBalance(afterbalance);
//        transactionsDO.setCurrencyRate(withdrawDO.getCurrencyRate());
//        transactionsDO.setAfterBalance(withdrawDO.getCurrencyAmount());
//        memberTransactionsMapper.insert(transactionsDO);
//
//        //修改用户余额
//        memberUserMapper.updateBalanceDecr(userDO, withdrawDO.getWithdrawAmount(), true);
//
//        // 返回
//        return withdrawDO.getId();
//    }
//
//    @Override
//    @Slave
//    public PageResult<MemberWithdrawDO> getWithdrawPage(Long userId, AppMemberWithdrawPageReqVO pageReqVO) {
//        return withdrawMapper.selectPage(userId, pageReqVO);
//    }
//
//    @Override
//    @Master
//    public void deleteWithdraw(Long id) {
//        // 校验存在
//        //validateWithdrawExists(id);
//        // 删除
//        withdrawMapper.deleteById(id);
//    }
//
//    private MemberWithdrawDO validateWithdrawExists(Long id) {
//        MemberWithdrawDO memberWithdrawDO = withdrawMapper.selectById(id);
//        if (memberWithdrawDO == null) {
//            throw exception(USER_WITHDRAW_NOT_EXISTS);
//        }
//        return memberWithdrawDO;
//    }
//
//    @Override
//    @Slave
//    public MemberWithdrawDO getWithdraw(Long id) {
//        return withdrawMapper.selectById(id);
//    }
//
//    @Override
//    @Slave
//    public PageResult<MemberWithdrawDO> getWithdrawPage(MemberWithdrawPageReqVO pageReqVO) {
//        return withdrawMapper.selectPage(pageReqVO);
//    }
//
//    @Override
//    public void updateWithdraw(MemberWithdrawSaveReqVO reqVO) {
//        MemberWithdrawDO memberWithdrawDO = validateWithdrawExists(reqVO.getId());
//        MemberWithdrawDO updateObj = BeanUtils.toBean(reqVO, MemberWithdrawDO.class);
//        withdrawMapper.updateById(updateObj);
//    }
//
//    @Override
//    @Master
//    @DSTransactional(rollbackFor = Exception.class)
//    public void handleWithdraw(String handler, MemberFundsRecordWithdrawHandleReqVO reqVO) {
//        //验证充值记录存在
//        MemberWithdrawDO withdrawDO = withdrawMapper.selectByIdForUpdate(reqVO.getId());
//        if (withdrawDO == null) {
//            throw exception(USER_WITHDRAW_NOT_EXISTS);
//        }
//        if (!Objects.equals(withdrawDO.getOrderSystemStatus(), MemberWithdrawStatusEnum.WAIT_HANDLE.getValue())) {
//            throw exception(USER_WITHDRAW_HAS_HANDLE);
//        }
//        //验证用户存在
//        MemberUserDO userDO = memberUserMapper.selectByIdForUpdate(withdrawDO.getUserId());
//        if (userDO == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//
//        //保存处理结果
//        withdrawDO.setCurrencyRate(reqVO.getCurrencyRate());
//        withdrawDO.setCurrencyAmount(reqVO.getCurrencyRate().multiply(withdrawDO.getWithdrawAmount()));
////        withdrawDO.setDeductionAmount(reqVO.getDeductionAmount());
////        withdrawDO.setTransferAmount(reqVO.getTransferAmount());
//        withdrawDO.setOrderMemberStatus(reqVO.getMemberStatus());
//        withdrawDO.setOrderSystemStatus(reqVO.getSystemStatus());
//        withdrawDO.setSystemRemark(reqVO.getRemark());
//        withdrawDO.setHandler(handler);
//        withdrawDO.setHandleTime(DateUtils.getUnixTimestampNow());
//        withdrawMapper.updateById(withdrawDO);
//        //更新账变的汇率
//        LambdaUpdateWrapper<MemberTransactionsDO> memberTransactionsUpdateWrapper = new LambdaUpdateWrapper<>();
//        memberTransactionsUpdateWrapper.eq(MemberTransactionsDO::getUserId, withdrawDO.getUserId()).eq(MemberTransactionsDO::getBizOrderNo, withdrawDO.getOrderNo())
//                .set(MemberTransactionsDO::getCurrencyRate, withdrawDO.getCurrencyRate()).set(MemberTransactionsDO::getCurrencyAmount, withdrawDO.getCurrencyAmount());
//        memberTransactionsMapper.update(memberTransactionsUpdateWrapper);
//
//
//        MemberWithdrawStatusEnum systemStatus = MemberWithdrawStatusEnum.get(reqVO.getSystemStatus());
//        switch (systemStatus) {
//            case SUCCESS -> {
//                //审核通过释放冻结金额
//                memberUserMapper.updateFrozenBalanceDecr(userDO, withdrawDO.getWithdrawAmount(), true);
//                //删除冻结明细
//                memberFrozenMapper.deleteById(withdrawDO.getFrozenId());
//            }
//            case BACK -> {
//                CurrencyBaseRespDTO currency = currencyApi.getCurrency(withdrawDO.getCurrencyId());
//                //记录账变
//                BigDecimal beforeBalance = userDO.getBalance();
//                BigDecimal afterbalance = userDO.getBalance().add(withdrawDO.getWithdrawAmount());
//                MemberTransactionsDO transactionsDO = new MemberTransactionsDO();
//                transactionsDO.setUserId(userDO.getId());
//                transactionsDO.setUsername(userDO.getUsername());
//                transactionsDO.setType(MemberTransactionsTypeEnum.WITHDRAW.getValue());
//                transactionsDO.setAmount(withdrawDO.getWithdrawAmount());
//                transactionsDO.setCurrencyId(withdrawDO.getCurrencyId());
//                transactionsDO.setCurrencyName(withdrawDO.getCurrencyName());
//                transactionsDO.setCurrencySymbol(currency.getSymbol());
//                transactionsDO.setBizOrderNo(withdrawDO.getOrderNo());
//                transactionsDO.setRemark(MessageConstants.WITHDRAW_BACK);
//                transactionsDO.setBeforeBalance(beforeBalance);
//                transactionsDO.setAfterBalance(afterbalance);
//
//                memberTransactionsMapper.insert(transactionsDO);
//                //退回余额
//                memberUserMapper.updateBalanceIncr(userDO, withdrawDO.getWithdrawAmount(), true, false);
//
//                //删除冻结明细
//                memberFrozenMapper.deleteById(withdrawDO.getFrozenId());
//            }
//            case PENDING -> {
//            }
//        }
//    }
//
//    private boolean isPasswordMatch(String rawPassword, String encodedPassword) {
//        return passwordEncoder.matches(rawPassword, encodedPassword);
//    }
//}