package com.rf.exchange.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;

@Schema(description = "管理后台 - 用户修改积分 Request VO")
@Data
@ToString(callSuper = true)
public class MemberUserUpdatePasswordReqVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @Positive(message = "用户id不能为空")
    private Long id;

    @Schema(description = "变动金额，正数为增加，负数为减少", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotEmpty(message = "新密码不能为空")
    //@Length(min = 6, max = 16, message = "密码长度为 8-16 位")
    private String password;

}
