package com.rf.exchange.module.member.controller.admin.favoritetradepair.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员收藏交易对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberFavoriteTradePairRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31501")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9896")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14518")
    @ExcelProperty("交易对id")
    private Long tradePairId;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("交易对名称")
    private String tradePairName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}