package com.rf.exchange.module.member.controller.app.transactions.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rf.exchange.module.member.enums.transactions.MemberTransactionsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员账变 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppMemberTransactionsRespVO {


    @Schema(description = "发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约")
    private Integer type;

    @Schema(description = "发生金额")
    private BigDecimal amount;

    @Schema(description = "法币金额")
    private BigDecimal currencyAmount;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    public String getTypeStr(){
        if(type==null)return "";
        return MemberTransactionsTypeEnum.get(type).getLabel();
    }
    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencyName;
    @Schema(description = "货币汇率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal currencyRate;
    @Schema(description = "货币符号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencySymbol;
}