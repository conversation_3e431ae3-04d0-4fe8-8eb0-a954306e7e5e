package com.rf.exchange.module.member.controller.admin.spotorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员现货订单记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberSpotOrderRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9041")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21031")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29240")
    @ExcelProperty("交易对id")
    private Long tradePairId;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("交易对名称")
    private String tradePairName;

    @Schema(description = "委托数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("委托数量")
    private BigDecimal orderVolume;

    @Schema(description = "委托价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "22114")
    @ExcelProperty("委托价格")
    private BigDecimal orderPrice;

    @Schema(description = "成交价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "4128")
    @ExcelProperty("成交价格")
    private BigDecimal executionPrice;

    @Schema(description = "手续费金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手续费金额")
    private BigDecimal feeAmount;

    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇")
    private Integer assetType;

    @Schema(description = "订单状态 0:进行中 1:已完成 2:失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("订单状态 0:进行中 1:已完成 2:失败")
    private Integer orderStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}