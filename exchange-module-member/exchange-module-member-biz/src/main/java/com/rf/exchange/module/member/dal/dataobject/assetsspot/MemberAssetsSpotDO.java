package com.rf.exchange.module.member.dal.dataobject.assetsspot;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员现货资产 DO
 *
 * <AUTHOR>
 */
@TableName("member_assets_spot")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberAssetsSpotDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 交易对id
     */
    private Long tradePairId;
    /**
     * 交易对名称
     */
    private String tradePairName;
    /**
     * 现货持仓量
     */
    private BigDecimal volume;
    /**
     * 可用余额(总余额为占用余额+可用余额)
     */
    private BigDecimal balance;
    /**
     * 占用余额
     */
    private BigDecimal balanceLocked;
    /**
     * 成交均价 (用于计算收益)
     */
    private BigDecimal executionAveragePrice;

    private Long agentId;
    private String agentName;
}