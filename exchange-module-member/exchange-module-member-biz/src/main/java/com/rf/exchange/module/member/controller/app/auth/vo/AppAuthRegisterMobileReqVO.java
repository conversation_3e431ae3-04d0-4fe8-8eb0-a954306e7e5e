//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.Mobile;
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.NotEmpty;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//@Schema(description = "用户 APP - 手机注册 Request VO")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthRegisterMobileReqVO extends AppAuthRegisterVO {
//
//    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    @NotEmpty(message = "手机号码不能为空")
//    @Mobile
//    private String mobile;
//
//    @Schema(description = "验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    @NotEmpty(message = "验证码不能为空")
//    private String verifyCode;
//}
