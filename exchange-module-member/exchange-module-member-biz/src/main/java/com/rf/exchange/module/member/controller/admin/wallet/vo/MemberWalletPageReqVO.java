package com.rf.exchange.module.member.controller.admin.wallet.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员钱包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberWalletPageReqVO extends PageParam {

    @Schema(description = "会员id", example = "21677")
    private Long userId;

    @Schema(description = "会员账号", example = "芋艿")
    private String username;

    @Schema(description = "类型，虚拟，银行", example = "1")
    private Integer type;

    @Schema(description = "类型名称，如银行名，或者链名", example = "王五")
    private String typeName;

    @Schema(description = "名称，如户名", example = "赵六")
    private String name;

    @Schema(description = "账号，如银行卡号，或者钱包地址", example = "13114")
    private String account;

    @Schema(description = "银行地址")
    private String bankAddress;

    @Schema(description = "支行")
    private String bankBranch;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "地区id", example = "20996")
    private Long areaId;

    @Schema(description = "地区名称", example = "王五")
    private String areaName;

    @Schema(description = "货币id", example = "23938")
    private Long currencyId;

    @Schema(description = "货币名称", example = "李四")
    private String currencyName;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}