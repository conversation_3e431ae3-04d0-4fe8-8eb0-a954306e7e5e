package com.rf.exchange.module.member.controller.app.favoritetradepair.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
public class AppMemberFavoriteTradePairSaveRemoveReqVO implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    @Schema(description = "交易对code", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{TRADE_PAIR_NOT_EMPTY}")
    private String code;

}
