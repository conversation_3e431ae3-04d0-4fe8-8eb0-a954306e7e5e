package com.rf.exchange.module.member.service.assetsspot;

import java.util.*;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.member.controller.admin.assetsspot.vo.MemberAssetsSpotPageReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationPageReqVO;
import com.rf.exchange.module.member.dal.dataobject.assetsspot.MemberAssetsSpotDO;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;

/**
 * 会员现货资产 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberAssetsSpotService {

    /**
     * 获得会员现货资产
     *
     * @param userId 用户id
     * @return 会员现货资产列表
     */
    List<MemberAssetsSpotDO> getAssetsSpotByUserId(Long userId);

    /**
     * 获取现货资产详细
     * @param id
     * @return 会员现货
     */
    MemberAssetsSpotDO getAssetsSpot(Long id);


    /**
     * 获取现货资产分页
     *
     * @param pageReqVO 分页查询
     * @return 会员现货资产分页分页
     */
    PageResult<MemberAssetsSpotDO> getAssetsSpotPage(MemberAssetsSpotPageReqVO pageReqVO);
}