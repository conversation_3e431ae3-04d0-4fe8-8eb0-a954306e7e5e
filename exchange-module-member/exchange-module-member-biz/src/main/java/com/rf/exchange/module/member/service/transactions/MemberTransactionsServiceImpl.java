package com.rf.exchange.module.member.service.transactions;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.dynamic.datasource.tx.DsPropagation;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.api.balance.dto.MemberTransactionSaveReqVO;
import com.rf.exchange.module.member.controller.admin.transactions.vo.MemberTransactionsPageReqVO;
import com.rf.exchange.module.member.controller.app.transactions.vo.AppMemberTransactionsPageReqVO;
import com.rf.exchange.module.member.dal.dataobject.transactions.MemberTransactionsDO;
import com.rf.exchange.module.member.dal.mysql.transactions.MemberTransactionsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 会员账变 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberTransactionsServiceImpl implements MemberTransactionsService {

    @Resource
    private MemberTransactionsMapper transactionsMapper;


    @Override
    @Slave
    public PageResult<MemberTransactionsDO> getTransactionsPage(MemberTransactionsPageReqVO pageReqVO) {
        return transactionsMapper.selectPage(pageReqVO);
    }

    @Override
    @Slave
    public PageResult<MemberTransactionsDO> getTransactionsPage(Long userId, AppMemberTransactionsPageReqVO pageReqVO) {
        return transactionsMapper.selectPage(userId,pageReqVO);
    }

    @Override
    @Slave
    public PageResult<MemberTransactionsDO> getTransactionsPageExcludeRechargeWithdraw(Long userId, AppMemberTransactionsPageReqVO pageReqVO) {
        return transactionsMapper.selectPageExcludeRechargeWithdraw(userId, pageReqVO);
    }

    @Override
    @Master
    @DSTransactional(propagation = DsPropagation.MANDATORY)
    public MemberTransactionsDO saveTransaction(MemberTransactionSaveReqVO requestVO) {
        MemberTransactionsDO transactionsDO = new MemberTransactionsDO();
        transactionsDO.setTenantId(requestVO.getTenantId());
        transactionsDO.setUserId(requestVO.getUserId());
        transactionsDO.setUsername(requestVO.getUsername());
        //TODO 代理未设置
//        transactionsDO.setAgentId();
//        transactionsDO.setAgentName();

        transactionsDO.setType(requestVO.getTransactionType().getValue());
        transactionsDO.setChangeAmount(requestVO.getAmount());
        transactionsDO.setCurrencyCode(requestVO.getAmountCurrency());
        transactionsDO.setBeforeBalance(requestVO.getBeforeBalance());
        transactionsDO.setAfterBalance(requestVO.getAfterBalance());
        transactionsDO.setBizOrderNo(requestVO.getBizOrderNo());
        transactionsDO.setRemark(requestVO.getRemark());
        if (StrUtil.isNotEmpty(requestVO.getCreator())) {
            transactionsDO.setCreator(requestVO.getCreator());
        }
        if (StrUtil.isNotEmpty(requestVO.getUpdater())) {
            transactionsDO.setUpdater(requestVO.getUpdater());
        }
        transactionsMapper.insert(transactionsDO);
        return transactionsDO;
    }

    @Override
    public List<MemberTransactionsDO> getLastTransactionByRange(Long startTime, Long endTime) {
        return transactionsMapper.getLastTransactionByRange(startTime,endTime);
    }

    @Override
    public List<MemberTransactionsDO> getTransactionsByRelationOrderNo(String orderNo) {
        final LambdaQueryWrapperX<MemberTransactionsDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(MemberTransactionsDO::getBizOrderNo, orderNo);
        wrapper.orderByDesc(MemberTransactionsDO::getId);
        return transactionsMapper.selectList(wrapper);
    }
}