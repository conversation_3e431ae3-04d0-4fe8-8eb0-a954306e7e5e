package com.rf.exchange.module.member.service.fundsrecord;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordRespVO;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordSaveReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRechargeCreateReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordWithdrawCreateReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordWithdrawWithWalletCreateReqVO;
import com.rf.exchange.module.member.dal.dataobject.fundsrecord.MemberFundsRecordDO;

import java.util.List;

/**
 * 会员资金记录，如充值提现 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberFundsRecordService {

    /**
     * 创建充值资金记录
     *
     * @param userId      用户id
     * @param createReqVO 创建信息
     */
    Long createFundsRecordRecharge(Long userId, AppMemberFundsRecordRechargeCreateReqVO createReqVO);

    /**
     * 创建提现资金记录
     *
     * @param userId      用户id
     * @param createReqVO 用户
     */
    void createFundsRecordWithdraw(Long userId, AppMemberFundsRecordWithdrawCreateReqVO createReqVO);

    /**
     * 创建提现带钱包信息
     * @param userId
     * @param createReqVO
     */
    void createFundsRecordWithdraw(Long userId, AppMemberFundsRecordWithdrawWithWalletCreateReqVO createReqVO);

    /**
     * 更新会员资金记录，如充值提现
     *
     * @param updateReqVO 更新信息
     */
    void updateFundsRecord(MemberFundsRecordSaveReqVO updateReqVO);

    /**
     * 删除会员资金记录，如充值提现
     *
     * @param id 编号
     * @return 请求结果
     */
    CommonResult<Boolean> deleteFundsRecord(Long id);

    /**
     * 获得会员资金记录，如充值提现
     *
     * @param id 编号
     * @return 会员资金记录，如充值提现
     */
    MemberFundsRecordDO getFundsRecord(Long id);

    /**
     * 获得会员资金记录，如充值提现分页
     *
     * @param pageReqVO 分页查询
     * @return 会员资金记录，如充值提现分页
     */
    PageResult<MemberFundsRecordRespVO> getFundsRecordPage(MemberFundsRecordPageReqVO pageReqVO);

    /**
     * 处理充值
     *
     * @param handler 处理人
     * @param reqVO   请求参数
     */
    void handleRecharge(String handler, MemberFundsRecordHandleReqVO reqVO);

    /**
     * 处理提现
     *
     * @param handler 处理人
     * @param reqVO   请求参数
     */
    void handleWithdraw(String handler, MemberFundsRecordHandleReqVO reqVO);

    /**
     * 会员获取
     *
     * @param userId 用户id
     * @return 会员资金记录分页
     */
    PageResult<MemberFundsRecordDO> getFundsRecordPageByUserId(Long userId, AppMemberFundsRecordPageReqVO reqVO);

    /**
     * 获取时间范围内的充值提现记录
     * @param startTime
     * @param endTime
     * @return
     */
    List<MemberFundsRecordDO> getMemberFundsRecordByRange(Long startTime,Long endTime);


    List<MemberFundsRecordDO> getMemberFundsRecordByRange(List<Long> agentId,Long startTime,Long endTime);
}