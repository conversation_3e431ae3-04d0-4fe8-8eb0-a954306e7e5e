package com.rf.exchange.module.member.service.assetsspot;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rf.exchange.module.member.controller.admin.assetsspot.vo.*;
import com.rf.exchange.module.member.dal.dataobject.assetsspot.MemberAssetsSpotDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.member.dal.mysql.assetsspot.MemberAssetsSpotMapper;

import java.util.List;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员现货资产 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAssetsSpotServiceImpl implements MemberAssetsSpotService {

    @Resource
    private MemberAssetsSpotMapper assetsSpotMapper;

    @Override
    @Slave
    public List<MemberAssetsSpotDO> getAssetsSpotByUserId(Long userId) {
        return assetsSpotMapper.selectByUserId(userId);
    }

    @Override
    @Slave
    public MemberAssetsSpotDO getAssetsSpot(Long id) {
        return null;
    }

    @Override
    @Slave
    public PageResult<MemberAssetsSpotDO> getAssetsSpotPage(MemberAssetsSpotPageReqVO pageReqVO) {
        return assetsSpotMapper.selectPage(pageReqVO);
    }
}