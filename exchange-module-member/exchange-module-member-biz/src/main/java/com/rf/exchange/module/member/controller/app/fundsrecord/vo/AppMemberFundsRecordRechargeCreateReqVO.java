package com.rf.exchange.module.member.controller.app.fundsrecord.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordPayMethodEnum;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 充值请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberFundsRecordRechargeCreateReqVO {
    //    @Schema(description = "钱包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @Positive(message = "钱包未找到")
//    private Long walletId;

    @Schema(description = "币种代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{CURRENCY_NOT_EMPTY}")
    private String currencyCode;

    @Schema(description = "美金金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.5")
    @Positive(message = "{RECHARGE_AMOUNT_ERROR}")
    private String amount;

    @Schema(description = "法币金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{ARG_VALUE_ERROR}")
    private String legalAmount;

    @Schema(description = "汇率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{ARG_VALUE_ERROR}")
    private String currencyRate;

    @Schema(description = "支付方式,1法币，2虚拟币", requiredMode = Schema.RequiredMode.REQUIRED, example = "1法币，2虚拟币")
    @InEnum(value = MemberFundsRecordPayMethodEnum.class,message = "{PAY_METHOD_ERROR}")
    private Integer payMethod;

    @Schema(description = "地区ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.5")
    @Positive(message = "{AREA_NOT_EMPTY}")
    private Integer areaId;

    @Hidden
    @Schema(description = "是否自动处理，用于后台的修改余额生成充值单")
    private boolean autoHandle;

    @Hidden
    @Schema(description = "自动处理备注，用于后台的修改余额生成充值单")
    private String autoHandleRemark;

    @Hidden
    @Schema(description = "自动处理处理人，用于后台的修改余额生成充值单")
    private String autoHandler;

//    @Schema(description = "资金密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
//    @NotEmpty(message = "资金密码不能为空")
//    private String fundsPassword;
}
