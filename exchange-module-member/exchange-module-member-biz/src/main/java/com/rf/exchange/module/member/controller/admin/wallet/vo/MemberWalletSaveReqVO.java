package com.rf.exchange.module.member.controller.admin.wallet.vo;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 钱包创建")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberWalletSaveReqVO {

    @Schema(description = "钱包id,如果是新增可以放空或者0", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;
    @Schema(description = "钱包类型，银行1，虚拟币2", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(MemberWalletTypeEnum.class)
    private Integer type;
    @Schema(description = "钱包名称，如果是银行卡就是户名，虚拟钱包就是钱包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty
    private String name;
    @Schema(description = "钱包账户，如果是银行卡就是卡号，虚拟钱包就是地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********  0x16546541651")
    @NotEmpty
    private String account;
    @Schema(description = "账户类型名称，如果是银行卡就是银行名，虚拟钱包就是链名", requiredMode = Schema.RequiredMode.REQUIRED, example = "儿童银行  BTC")
    @NotEmpty
    private String typeName;
    @Schema(description = "银行地址，非必填", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx城市xxx县")
    private String bankAddress;
    @Schema(description = "支行地址，非必填", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx城市xxx县")
    private String bankBranch;
    @Schema(description = "地区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer areaId;
    @Schema(description = "币种代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "USD")
    private String currencyCode;
    @Schema(description = "状态，0正常，1禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "USD")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

}
