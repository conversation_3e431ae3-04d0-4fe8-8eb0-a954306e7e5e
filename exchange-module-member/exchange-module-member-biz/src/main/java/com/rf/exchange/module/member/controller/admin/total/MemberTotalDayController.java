package com.rf.exchange.module.member.controller.admin.total;

import com.rf.exchange.module.member.dal.dataobject.total.MemberTotalDayDO;
import com.rf.exchange.module.member.service.total.MemberTotalDayService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.member.controller.admin.total.vo.*;

@Tag(name = "管理后台 - 会员日统计数据")
@RestController
@RequestMapping("/member/total-day")
@Validated
public class MemberTotalDayController {

    @Resource
    private MemberTotalDayService dayTotalService;


    @GetMapping("/page")
    @Operation(summary = "获得会员日统计数据分页")
    @PreAuthorize("@ss.hasPermission('member:day-total:query')")
    public CommonResult<PageResult<MemberTotalDayRespVO>> getDayTotalPage(@Valid MemberTotalDayPageReqVO pageReqVO) {
        PageResult<MemberTotalDayDO> pageResult = dayTotalService.getDayTotalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberTotalDayRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员日统计数据 Excel")
    @PreAuthorize("@ss.hasPermission('member:day-total:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDayTotalExcel(@Valid MemberTotalDayPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberTotalDayDO> list = dayTotalService.getDayTotalPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员日统计数据.xls", "数据", MemberTotalDayRespVO.class,
                        BeanUtils.toBean(list, MemberTotalDayRespVO.class));
    }

}