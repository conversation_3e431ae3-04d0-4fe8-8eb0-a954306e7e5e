//package com.rf.exchange.module.member.service.withdraw;
//
//import com.rf.exchange.module.member.controller.admin.withdraw.vo.MemberWithdrawPageReqVO;
//import com.rf.exchange.module.member.controller.admin.withdraw.vo.MemberWithdrawSaveReqVO;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawCreateReqVO;
//import com.rf.exchange.module.member.controller.app.withdraw.vo.AppMemberWithdrawPageReqVO;
//import com.rf.exchange.module.member.dal.dataobject.withdraw.MemberWithdrawDO;
//import com.rf.exchange.framework.common.pojo.PageResult;
//
///**
// * 会员提现 Service 接口
// *
// * <AUTHOR>
// */
//public interface MemberWithdrawService {
//
//    /**
//     * 创建会员提现
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createWithdraw(Long userId, AppMemberWithdrawCreateReqVO createReqVO);
//
//    /**
//     * 获得会员提现分页
//     *
//     * @param pageReqVO 分页查询
//     * @return 会员提现分页
//     */
//    PageResult<MemberWithdrawDO> getWithdrawPage(Long userId,AppMemberWithdrawPageReqVO pageReqVO);
//
//    /**
//     * 删除会员提现
//     *
//     * @param id 编号
//     */
//    void deleteWithdraw(Long id);
//
//    /**
//     * 获得会员提现
//     *
//     * @param id 编号
//     * @return 会员提现
//     */
//    MemberWithdrawDO getWithdraw(Long id);
//
//    /**
//     * 获得会员提现分页
//     *
//     * @param pageReqVO 分页查询
//     * @return 会员提现分页
//     */
//    PageResult<MemberWithdrawDO> getWithdrawPage(MemberWithdrawPageReqVO pageReqVO);
//
//    /**
//     * 修改提现信息
//     * @param reqVO
//     */
//    void updateWithdraw(MemberWithdrawSaveReqVO reqVO);
//
//    /**
//     * 处理提现
//     * @param reqVO
//     */
//    void handleWithdraw(String handler, MemberFundsRecordWithdrawHandleReqVO reqVO);
//}