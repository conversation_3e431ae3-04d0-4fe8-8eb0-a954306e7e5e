package com.rf.exchange.module.member.service.certification;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationHandleReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationManualSaveReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationPageReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationSaveReqVO;
import com.rf.exchange.module.member.controller.app.certification.vo.AppMemberCertificationSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;

import jakarta.validation.Valid;

/**
 * 会员身份认证 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberCertificationService {

    /**
     * 【会员】保存身份认证
     *
     * @param saveCertificationReqVO 身份信息
     */
    void saveUserCertification(Long userId, AppMemberCertificationSaveReqVO saveCertificationReqVO);

    /**
     * 删除会员身份认证
     *
     * @param id 编号
     */
    void deleteCertification(Long id);

    /**
     * 获得会员身份认证
     *
     * @param id 编号
     * @return 会员身份认证
     */
    MemberCertificationDO getCertification(Long id);

    /**
     * 通过用户id获取身份认证
     *
     * @param userId
     * @return
     */
    MemberCertificationDO getCertificationByUserId(Long userId);

    /**
     * 获得会员身份认证分页
     *
     * @param pageReqVO 分页查询
     * @return 会员身份认证分页
     */
    PageResult<MemberCertificationDO> getCertificationPage(MemberCertificationPageReqVO pageReqVO);

    /**
     * 处理身份认证
     *
     * @param loginUser
     * @param handleReqVO
     */
    void handle(String loginUser, MemberCertificationHandleReqVO handleReqVO);

    /**
     * 更新会员身份认证
     *
     * @param updateReqVO 更新信息
     */
    void updateCertification(MemberCertificationSaveReqVO updateReqVO);

    /**
     * 管理后台手动
     *
     * @param reqVO         请求信息
     * @param loginUserId   后台登录用户id
     * @param loginUsername 后台登录用户名
     */
    void manualAddCertification(MemberCertificationManualSaveReqVO reqVO, Long loginUserId, String loginUsername);

    /**
     * 创建会员身份认证
     * 
     * @param reqVO 请求信息
     * @param loginUserId 后台登录用户id
     * @param loginUsername 后台登录用户名
     */
    void createCertification(@Valid MemberCertificationManualSaveReqVO reqVO, Long loginUserId, String loginUsername);
}