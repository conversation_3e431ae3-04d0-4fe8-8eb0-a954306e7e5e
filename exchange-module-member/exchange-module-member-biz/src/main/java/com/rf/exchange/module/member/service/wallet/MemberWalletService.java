package com.rf.exchange.module.member.service.wallet;

import com.rf.exchange.module.member.controller.app.wallet.vo.AppMemberWalletSaveReqVO;
import com.rf.exchange.module.member.controller.admin.wallet.vo.*;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import com.rf.exchange.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 会员钱包 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberWalletService {

    /**
     * 创建会员钱包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long saveWallet(Long userId,AppMemberWalletSaveReqVO createReqVO);

    /**
     * 修改钱包
     * @param saveReqVO
     */
    void updateWallet(MemberWalletSaveReqVO saveReqVO);

    /**
     * 删除会员钱包
     *
     * @param id 编号
     */
    void deleteWallet(Long id);

    /**
     * 获得会员钱包
     *
     * @param id 编号
     * @return 会员钱包
     */
    MemberWalletDO getWallet(Long id);

    /**
     * 获取会员钱包明细
     * @param userId
     * @param id
     * @return
     */
    MemberWalletDO getWallet(Long userId,Long id);

    /**
     * 删除会员钱包
     * @param userId
     * @param id
     */
    void deleteWallet(Long userId, Long id);

    /**
     * 获得会员钱包分页
     *
     * @param pageReqVO 分页查询
     * @return 会员钱包分页
     */
    PageResult<MemberWalletDO> getWalletPage(MemberWalletPageReqVO pageReqVO);

    /**
     * 获取钱包列表
     * @param userId 用户id
     * @return
     */
    List<MemberWalletDO> getWalletList(Long userId);

}