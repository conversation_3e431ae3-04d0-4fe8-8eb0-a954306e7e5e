package com.rf.exchange.module.member.service.config;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.controller.admin.user.vo.MemberUserConfigSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserConfigDO;
import com.rf.exchange.module.member.dal.mysql.user.MemberUserConfigMapper;
import com.rf.exchange.module.member.dal.redis.MemberUserConfigRedisDAO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MemberUserConfigServiceImpl implements MemberUserConfigService {

    @Resource
    private MemberUserConfigMapper memberUserConfigMapper;
    @Resource
    private MemberUserConfigRedisDAO configRedisDAO;

    @Override
    @Master
    @DSTransactional
    public void updateMemberUserConfig(MemberUserConfigSaveReqVO reqVO) {
        long exists = memberUserConfigMapper.selectCount(new LambdaQueryWrapperX<MemberUserConfigDO>().eq(MemberUserConfigDO::getMemberId, reqVO.getMemberId()));
        MemberUserConfigDO updateObj = BeanUtils.toBean(reqVO, MemberUserConfigDO.class);
        if (exists > 0) {
            memberUserConfigMapper.updateById(updateObj);
        } else {
            memberUserConfigMapper.insert(updateObj);
        }
        configRedisDAO.set(updateObj);
    }

    @Override
    @Slave
    public MemberUserConfigDO getConfigCachedByUserId(Long userId) {
        MemberUserConfigDO configDO = configRedisDAO.get(userId);
        if (configDO != null) {
            return configDO;
        }
        configDO = memberUserConfigMapper.selectById(userId);
        configRedisDAO.set(configDO);
        return configDO;
    }

    @Override
    @Slave
    public Map<Long, MemberUserConfigDO> getConfigListByUserIdList(Collection<Long> userIdList) {
        LambdaQueryWrapperX<MemberUserConfigDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(MemberUserConfigDO::getMemberId, userIdList);
        List<MemberUserConfigDO> list = memberUserConfigMapper.selectList(queryWrapperX);
        return list.parallelStream().collect(Collectors.toMap(MemberUserConfigDO::getMemberId, Function.identity()));
    }
}
