package com.rf.exchange.module.member.dal.dataobject.frozen;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 会员冻结明细 DO
 *
 * <AUTHOR>
 */
@TableName("member_frozen")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberFrozenDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;
    /**
     * 冻结原因
     */
    private String frozenReason;
    /**
     * 关联单号
     */
    private String bizOrderNo;

    private Long agentId;
    private String agentName;
}