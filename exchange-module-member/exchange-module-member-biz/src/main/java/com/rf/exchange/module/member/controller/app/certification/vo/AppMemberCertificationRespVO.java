package com.rf.exchange.module.member.controller.app.certification.vo;

import com.rf.exchange.framework.common.validation.ImageUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 认证详情")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberCertificationRespVO {
    @Schema(description = "地区IUD")
    private Integer areaId;
    @Schema(description = "地区")
    private String areaName;
    @Schema(description = "真名")
    private String realName;
    @Schema(description = "性别")
    private Integer sex;
    @Schema(description = "生日，unix时间戳")
    private Long birthday;
    @Schema(description = "认证类型，1身份证，2护照")
    private Integer credentialsType;
    @Schema(description = "证件号")
    private String credentialsCode;
    @Schema(description = "证件正面")
    @ImageUrl
    private String credentialsFront;
    @Schema(description = "证件反面")
    @ImageUrl
    private String credentialsBack;
    @Schema(description = "认证状态,0未提交，1审核中，2成功，3失败")
    private Integer status;
}
