package com.rf.exchange.module.member.controller.app.user.vo;

import com.rf.exchange.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

@Schema(description = "用户 APP - 修改手机 Request VO")
@Data
public class AppMemberUserUpdateMobileReqVO {

    @Schema(description = "手机验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "{AUTH_CODE_NOT_EMPTY}")
    private String code;

    @Schema(description = "手机号",requiredMode = Schema.RequiredMode.REQUIRED, example = "15823654487")
    @Mobile(message = "{MOBILE_FORMATTER_ERROR}")
    private String mobile;

    @Schema(description = "原手机验证码", example = "1024")
    private String oldCode;
}
