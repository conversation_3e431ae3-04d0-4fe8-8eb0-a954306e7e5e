package com.rf.exchange.module.member.controller.admin.certification.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员身份认证分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberCertificationPageReqVO extends PageParam {

    @Schema(description = "用户", example = "18826")
    private Long userId;

    @Schema(description = "账号", example = "李四")
    private String username;

    @Schema(description = "地区", example = "2514")
    private Long areaId;

    @Schema(description = "地区", example = "王五")
    private String areaName;

    @Schema(description = "真名", example = "芋艿")
    private String realName;

    @Schema(description = "性别")
    private Short sex;

    @Schema(description = "生日")
    private Long birthday;

    @Schema(description = "证件类型", example = "1")
    private Short credentialsType;

    @Schema(description = "证件号码")
    private String credentialsCode;

    @Schema(description = "证件正面")
    private String credentialsFront;

    @Schema(description = "证件反面")
    private String credentialsBack;

    @Schema(description = "状态", example = "1")
    private Short status;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] handleTime;

    @Schema(description = "处理备注", example = "你说的对")
    private String handleRemark;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}