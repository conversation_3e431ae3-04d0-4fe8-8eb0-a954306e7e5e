package com.rf.exchange.module.member.controller.app.user.vo;

import com.rf.exchange.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "用户 APP - 修改手机 Request VO")
@Data
public class AppMemberUserUpdateMailReqVO {

    @Schema(description = "邮箱验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "{AUTH_CODE_NOT_EMPTY}")
    private String code;

    @Schema(description = "手机号",requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @Email(message = "{MAIL_FORMATTER_ERROR}")
    private String mail;
}
