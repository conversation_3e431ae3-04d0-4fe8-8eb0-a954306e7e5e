package com.rf.exchange.module.member.controller.admin.wallet.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员钱包 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberWalletRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10269")
    @ExcelProperty("id")
    private Long id;
    @Schema(description = "钱包类型,1银行卡，2加密币", requiredMode = Schema.RequiredMode.REQUIRED, example = "10269")
    @InEnum(MemberWalletTypeEnum.class)
    private Integer type;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21677")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "类型名称，如银行名，或者链名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("类型名称，如银行名，或者链名")
    private String typeName;

    @Schema(description = "名称，如户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("名称，如户名")
    private String name;

    @Schema(description = "账号，如银行卡号，或者钱包地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "13114")
    @ExcelProperty("账号，如银行卡号，或者钱包地址")
    private String account;

    @Schema(description = "银行地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行地址")
    private String bankAddress;

    @Schema(description = "支行", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支行")
    private String bankBranch;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "地区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20996")
    @ExcelProperty("地区id")
    private Long areaId;

    @Schema(description = "地区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("地区名称")
    private String areaName;

    @Schema(description = "货币id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23938")
    @ExcelProperty("货币id")
    private Long currencyId;

    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("货币名称")
    private String currencyName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}