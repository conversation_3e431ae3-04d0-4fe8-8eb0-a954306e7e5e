package com.rf.exchange.module.member.controller.admin.spotorder.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员现货订单记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberSpotOrderPageReqVO extends PageParam {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "会员id", example = "21031")
    private Long userId;

    @Schema(description = "会员账号", example = "王五")
    private String username;

    @Schema(description = "交易对id", example = "29240")
    private Long tradePairId;

    @Schema(description = "交易对名称", example = "王五")
    private String tradePairName;

    @Schema(description = "委托数量")
    private BigDecimal orderVolume;

    @Schema(description = "委托价格", example = "22114")
    private BigDecimal orderPrice;

    @Schema(description = "成交价格", example = "4128")
    private BigDecimal executionPrice;

    @Schema(description = "手续费金额")
    private BigDecimal feeAmount;

    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", example = "1")
    private Integer assetType;

    @Schema(description = "订单状态 0:进行中 1:已完成 2:失败", example = "1")
    private Integer orderStatus;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}