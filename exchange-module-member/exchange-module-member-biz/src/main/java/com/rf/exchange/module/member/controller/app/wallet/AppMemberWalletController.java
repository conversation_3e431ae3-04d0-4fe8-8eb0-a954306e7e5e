package com.rf.exchange.module.member.controller.app.wallet;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.wallet.vo.AppMemberWalletRespVO;
import com.rf.exchange.module.member.controller.app.wallet.vo.AppMemberWalletSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.wallet.MemberWalletDO;
import com.rf.exchange.module.member.service.wallet.MemberWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.error;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 用户钱包")
@RestController
@RequestMapping("/member/wallet")
@Validated
@Slf4j
public class AppMemberWalletController {
    @Resource
    private MemberWalletService memberWalletService;

    @GetMapping("/page")
    @Operation(summary = "会员钱包列表")
    @PreAuthenticated
    public CommonResult<List<AppMemberWalletRespVO>> getWalletList() {
        List<MemberWalletDO> list = memberWalletService.getWalletList(WebFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(list, AppMemberWalletRespVO.class));
    }

    @GetMapping("get")
    @Operation(summary = "会员钱包详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppMemberWalletRespVO> getWallet(@RequestParam("id") Long id) {
        MemberWalletDO memberWalletDO = memberWalletService.getWallet(WebFrameworkUtils.getLoginUserId(), id);
        return success(BeanUtils.toBean(memberWalletDO, AppMemberWalletRespVO.class));
    }

    @PostMapping("/save")
    @Operation(summary = "会员保存钱包信息,创建/修改")
    @PreAuthenticated
    public CommonResult<Boolean> saveWallet(@RequestBody @Valid AppMemberWalletSaveReqVO reqVO) {
        memberWalletService.saveWallet(WebFrameworkUtils.getLoginUserId(), reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/remove")
    @Operation(summary = "移除会员钱包")
    @PreAuthenticated
    public CommonResult<Boolean> removeWallet(@RequestParam("id")Long id){
        memberWalletService.deleteWallet(WebFrameworkUtils.getLoginUserId(), id);
        return CommonResult.success(true);
    }
}
