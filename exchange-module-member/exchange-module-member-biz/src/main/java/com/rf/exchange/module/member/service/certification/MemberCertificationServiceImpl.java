package com.rf.exchange.module.member.service.certification;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationHandleReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationManualSaveReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationPageReqVO;
import com.rf.exchange.module.member.controller.admin.certification.vo.MemberCertificationSaveReqVO;
import com.rf.exchange.module.member.controller.app.certification.vo.AppMemberCertificationSaveReqVO;
import com.rf.exchange.module.member.dal.dataobject.certification.MemberCertificationDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.dal.mysql.certification.MemberCertificationMapper;
import com.rf.exchange.module.member.dal.mysql.user.MemberUserMapper;
import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import com.rf.exchange.module.member.mq.producer.user.MemberUserMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.NOT_IMPLEMENTED;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员身份认证 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberCertificationServiceImpl implements MemberCertificationService {

    @Resource
    private MemberCertificationMapper memberCertificationMapper;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private MemberUserMQProducer memberUserMQProducer;

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public void saveUserCertification(Long userId, AppMemberCertificationSaveReqVO saveCertificationReqVO) {
        //校验用户存在
        MemberUserDO user = memberUserMapper.selectById(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        //修改用户表的认证状态
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userId).set(MemberUserDO::getCertificationStatus, MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType());
        memberUserMapper.update(updateWrapper);

        //判断之前是否有提交过身份信息
        MemberCertificationDO memberCertificationDO = memberCertificationMapper.selectByMemberId(userId);
        if (memberCertificationDO == null) {
            Area area = AreaUtils.getArea(saveCertificationReqVO.getAreaId());
            memberCertificationDO = new MemberCertificationDO();
            memberCertificationDO.setUserId(user.getId());
            memberCertificationDO.setUsername(user.getUsername());
            memberCertificationDO.setAgentId(user.getAgentId());
            memberCertificationDO.setAgentName(user.getAgentName());
            memberCertificationDO.setAreaId(area.getId());
            memberCertificationDO.setAreaName(area.getName());
            memberCertificationDO.setRealName(saveCertificationReqVO.getRealName());
            memberCertificationDO.setCredentialsType(saveCertificationReqVO.getCredentialsType());
            memberCertificationDO.setCredentialsCode(saveCertificationReqVO.getCredentialsCode());
            memberCertificationDO.setCredentialsFront(StrUtil.emptyIfNull(saveCertificationReqVO.getCredentialsFront()));
            memberCertificationDO.setCredentialsBack(StrUtil.emptyIfNull(saveCertificationReqVO.getCredentialsBack()));
            memberCertificationDO.setStatus(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getValue());
            memberCertificationMapper.insert(memberCertificationDO);
        } else {
            MemberCertificationStatusEnum certificationStatusEnum = MemberCertificationStatusEnum.get(memberCertificationDO.getStatus());
            switch (certificationStatusEnum) {
                case USER_CERTIFIED_STATUS_SUCCESS -> throw exception(USER_CERTIFICATION_STATUS_SUCCESS);
                case USER_CERTIFIED_STATUS_HANDLING -> throw exception(USER_CERTIFICATION_STATUS_HANDLING);
                case USER_CERTIFIED_STATUS_FAIL -> {
                    Area area = AreaUtils.getArea(saveCertificationReqVO.getAreaId());
                    memberCertificationDO.setAreaId(area.getId());
                    memberCertificationDO.setAreaName(area.getName());
                    memberCertificationDO.setRealName(saveCertificationReqVO.getRealName());
                    memberCertificationDO.setCredentialsType(saveCertificationReqVO.getCredentialsType());
                    memberCertificationDO.setCredentialsCode(saveCertificationReqVO.getCredentialsCode());
                    memberCertificationDO.setCredentialsFront(saveCertificationReqVO.getCredentialsFront());
                    memberCertificationDO.setCredentialsBack(saveCertificationReqVO.getCredentialsBack());
                    memberCertificationDO.setStatus(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getValue());
                    memberCertificationMapper.updateById(memberCertificationDO);
                }
            }
        }
        if (user.getDemo()) {
            handle("试玩", MemberCertificationHandleReqVO.builder().remark("自动审核").status(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType()).id(memberCertificationDO.getId()).build());
        } else {
            // 发送RocketMQ消息
            memberUserMQProducer.sendUserCertificateMessage(user.getTenantId(), userId);
        }
    }

    @Override
    @Master
    public void deleteCertification(Long id) {
        // 校验存在
        //validateCertificationExists(id);
        // 删除
        memberCertificationMapper.deleteById(id);
    }

    private void validateCertificationExists(Long id) {
        if (memberCertificationMapper.selectById(id) == null) {
            throw exception(USER_CERTIFICATION_NOT_EXISTS);
        }
    }

    @Override
    @Slave
    public MemberCertificationDO getCertification(Long id) {
        return memberCertificationMapper.selectById(id);
    }

    @Override
    @Slave
    public MemberCertificationDO getCertificationByUserId(Long userId) {
        LambdaQueryWrapperX<MemberCertificationDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(MemberCertificationDO::getUserId, userId).last("limit 1");
        MemberCertificationDO memberCertificationDO = memberCertificationMapper.selectOne(queryWrapperX);
        return memberCertificationDO;
    }


    @Override
    public void updateCertification(MemberCertificationSaveReqVO updateReqVO) {
        // 校验存在
        validateCertificationExists(updateReqVO.getId());
        // 更新
        MemberCertificationDO updateObj = BeanUtils.toBean(updateReqVO, MemberCertificationDO.class);
        memberCertificationMapper.updateById(updateObj);
    }


    @Override
    public void manualAddCertification(MemberCertificationManualSaveReqVO reqVO, Long loginUserId, String loginUsername) {
        long userId = reqVO.getUserId();
        //校验用户存在
        MemberUserDO user = memberUserMapper.selectById(reqVO.getUserId());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        //判断之前是否有提交过身份信息
        MemberCertificationDO existsCertDO = memberCertificationMapper.selectByMemberId(userId);
        if (existsCertDO == null) {
            saveUserCertificationRecord(reqVO, loginUsername, user);
        } else {
            existsCertDO.setAreaId(user.getAreaId());
            existsCertDO.setAreaName(user.getAreaName());
            existsCertDO.setRealName(reqVO.getRealName());
            existsCertDO.setCredentialsType(reqVO.getCredentialsType());
            existsCertDO.setCredentialsCode(reqVO.getCredentialsCode());
            existsCertDO.setCredentialsFront(reqVO.getCredentialsFront());
            existsCertDO.setCredentialsBack(reqVO.getCredentialsBack());
            existsCertDO.setStatus(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getValue());
            existsCertDO.setHandler(loginUsername);
            existsCertDO.setHandleRemark(StrUtil.emptyIfNull(reqVO.getHandleRemark()));
            memberCertificationMapper.updateById(existsCertDO);
        }

        //修改用户表的认证状态
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, userId)
                .set(MemberUserDO::getName, reqVO.getRealName())
                .set(MemberUserDO::getCertificationStatus, MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType());
        memberUserMapper.update(updateWrapper);
    }

    @Override
    public void createCertification(MemberCertificationManualSaveReqVO reqVO, Long loginUserId, String loginUsername) {
        long userId = reqVO.getUserId();
        //校验用户存在
        MemberUserDO user = memberUserMapper.selectById(reqVO.getUserId());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        //判断之前是否有提交过身份信息
        MemberCertificationDO existsCertDO = memberCertificationMapper.selectByMemberId(userId);
        if (existsCertDO == null) {
            saveUserCertificationRecord(reqVO, loginUsername, user);
        } else {
            if (existsCertDO.getStatus().equals(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getType())) {
                throw exception(USER_CERTIFICATION_STATUS_SUCCESS);
            } else if (existsCertDO.getStatus().equals(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_FAIL.getType())) {
                throw exception(USER_CERTIFICATION_VERIFY_FAILURE);
            } else if (existsCertDO.getStatus().equals(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_HANDLING.getType())) {
                throw exception(USER_CERTIFICATION_STATUS_HANDLING);
            } else {
                throw exception(NOT_IMPLEMENTED);
            }
        }
    }

    @Master
    @DSTransactional
    public void saveUserCertificationRecord(MemberCertificationManualSaveReqVO reqVO, String loginUsername, MemberUserDO user) {
        MemberCertificationDO existsCertDO;
        existsCertDO = new MemberCertificationDO();
        existsCertDO.setUserId(user.getId());
        existsCertDO.setUsername(user.getUsername());
        existsCertDO.setAgentId(user.getAgentId());
        existsCertDO.setAgentName(user.getAgentName());
        existsCertDO.setAreaId(user.getAreaId());
        existsCertDO.setAreaName(user.getAreaName());
        existsCertDO.setRealName(reqVO.getRealName());
        existsCertDO.setCredentialsType(reqVO.getCredentialsType());
        existsCertDO.setCredentialsCode(reqVO.getCredentialsCode());
        existsCertDO.setCredentialsFront(StrUtil.emptyIfNull(reqVO.getCredentialsFront()));
        existsCertDO.setCredentialsBack(StrUtil.emptyIfNull(reqVO.getCredentialsBack()));
        existsCertDO.setStatus(MemberCertificationStatusEnum.USER_CERTIFIED_STATUS_SUCCESS.getValue());
        existsCertDO.setHandler(loginUsername);
        existsCertDO.setHandleRemark(StrUtil.emptyIfNull(reqVO.getHandleRemark()));
        existsCertDO.setTenantId(user.getTenantId());
        memberCertificationMapper.insert(existsCertDO);
    }

    @Override
    @Slave
    public PageResult<MemberCertificationDO> getCertificationPage(MemberCertificationPageReqVO pageReqVO) {
        return memberCertificationMapper.selectPage(pageReqVO);
    }

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public void handle(String loginUser, MemberCertificationHandleReqVO handleReqVO) {
        MemberCertificationDO memberCertificationDO = memberCertificationMapper.selectById(handleReqVO.getId());
        if (memberCertificationDO == null) {
            throw exception(USER_CERTIFICATION_NOT_EXISTS);
        }
//        if (memberCertificationDO.getStatus() != MemberCertificationStatusEnum.WAIT_HANDLE.getValue()) {
//            throw exception(USER_CERTIFICATION_BEEN_HANDLE);
//        }
        memberCertificationDO.setHandler(loginUser);
        memberCertificationDO.setHandleTime(DateUtils.getUnixTimestampNow());
        memberCertificationDO.setHandleRemark(handleReqVO.getRemark());
        memberCertificationDO.setUpdater(loginUser);
        memberCertificationDO.setStatus(handleReqVO.getStatus());
        memberCertificationMapper.updateById(memberCertificationDO);

        //修改会员表认证状态
        LambdaUpdateWrapper<MemberUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberUserDO::getId, memberCertificationDO.getUserId())
                .set(MemberUserDO::getName, memberCertificationDO.getRealName())
                .set(MemberUserDO::getCertificationStatus, handleReqVO.getStatus());
        memberUserMapper.update(updateWrapper);

    }
}