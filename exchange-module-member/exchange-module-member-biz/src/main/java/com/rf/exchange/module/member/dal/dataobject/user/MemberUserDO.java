package com.rf.exchange.module.member.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.enums.TerminalEnum;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import com.rf.exchange.module.system.enums.common.SexEnum;
import lombok.*;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.math.BigDecimal;

/**
 * 会员用户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "member_user", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserDO extends TenantBaseDO {

    // ========== 账号信息 ==========
    /**
     * 用户ID
     */
    @TableId
    private Long id;
    /**
     * 会员名
     */
    private String username;
    /**
     * 登录账号
     */
    private String account;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 加密后的密码
     * <p>
     * 因为目前使用 {@link BCryptPasswordEncoder} 加密器，所以无需自己处理 salt 盐
     */
    private String password;
    /**
     * 所属的代理id
     */
    private Long agentId;

    /**
     * 所属代理
     */
    private String agentName;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 注册 IP
     */
    private String registerIp;
    /**
     * 注册终端 枚举 {@link TerminalEnum}
     */
    private Integer registerTerminal;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private long loginDate;

    // ========== 基础信息 ==========

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 真实名字
     */
    private String name;
    /**
     * 性别
     * <p>
     * 枚举 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 出生日期
     */
    private long birthday;
    /**
     * 所在地
     * <p>
     * 关联 {@link Area#getId()} 字段
     */
    private Integer areaId;
    /**
     * 所在地
     */
    private String areaName;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 余额
     */
    private BigDecimal usdtBalance;
    /**
     * 冻结金额
     */
    private BigDecimal usdtFrozenBalance;
    /**
     * 用户总充值
     */
    private BigDecimal recharge;
    /**
     * 用户总提款
     */
    private BigDecimal withdraw;
    /**
     * 默认币种
     */
    private long currencyId;
    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 会员身份信息认证状态
     */
    private Integer certificationStatus;
    /**
     * 资金密码
     */
    private String fundPassword;

    private Long levelId;
    private String levelName;

    private Boolean demo;

    /**
     * 信用分
     */
    private Integer creditScore;

    /**
     * 密码错误次数
     */
    private Integer pwdWrongCount;
    /**
     * 禁止提现
     * true: 禁止
     */
    private Boolean disableWithdraw;
    /**
     * 禁止限时合约下注
     * true: 禁止
     */
    private Boolean disableTimeContract;
    /**
     * 禁止合约下单
     * true: 禁止
     */
    private Boolean disableContract;
}
