package com.rf.exchange.module.member.controller.admin.assetsspot.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员现货资产分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberAssetsSpotPageReqVO extends PageParam {

    @Schema(description = "会员id", example = "26726")
    private Long userId;

    @Schema(description = "会员账号", example = "李四")
    private String username;

    @Schema(description = "交易对id", example = "228")
    private Long tradePairId;

    @Schema(description = "交易对名称", example = "张三")
    private String tradePairName;

    @Schema(description = "现货持仓量")
    private BigDecimal volume;

    @Schema(description = "可用余额(总余额为占用余额+可用余额)")
    private BigDecimal balance;

    @Schema(description = "占用余额")
    private BigDecimal balanceLocked;

    @Schema(description = "成交均价 (用于计算收益)", example = "29502")
    private BigDecimal executionAveragePrice;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}