package com.rf.exchange.module.member.dal.dataobject.fundsrecord;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * 会员资金记录，如充值提现 DO
 *
 * <AUTHOR>
 */
@TableName("member_funds_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberFundsRecordDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 类型：1充值，2提现
     */
    private Integer opType;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 币种金额
     */
    private BigDecimal currencyAmount;
    /**
     * 币种汇率
     */
    private BigDecimal currencyRate;
    /**
     * usdt金额
     */
    private BigDecimal usdtAmount;
    /**
     * 提现手续费
     */
    private BigDecimal feeAmount;
    /**
     * 支付方式
     */
    private Integer payMethod;
    /**
     * 会员订单状态
     * {@link MemberFundsRecordStatusEnum}
     */
    private Integer orderMemberStatus;
    /**
     * 系统订单状态
     * {@link MemberFundsRecordStatusEnum}
     */
    private Integer orderSystemStatus;
    /**
     * 会员备注
     */
    private String memberRemark;
    /**
     * 系统备注
     */
    private String systemRemark;
    /**
     * 冻结的id
     */
    private Long frozenId;
    /**
     * 钱包id
     */
    private Long walletId;
    /**
     * 钱包类型，加密货币，银行
     */
    private Integer walletType;
    /**
     * 钱包名，链名，银行名
     */
    private String walletTypeName;
    /**
     * 银行户名
     */
    private String walletName;
    /**
     * 银行账号/钱包哈希地址
     */
    private String walletAccount;
    /**
     * 银行地址
     */
    private String walletBankAddress;
    /**
     * 银行支行
     */
    private String walletBankBranch;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理时间
     */
    private Long handleTime;

    private Long agentId;
    private String agentName;

    private Integer areaId;
    private String area;
}