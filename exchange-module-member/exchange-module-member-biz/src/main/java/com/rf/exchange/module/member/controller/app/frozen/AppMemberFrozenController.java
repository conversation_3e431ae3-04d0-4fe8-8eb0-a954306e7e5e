package com.rf.exchange.module.member.controller.app.frozen;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.security.core.annotations.PreAuthenticated;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.app.frozen.vo.AppMemberFrozenRespVO;
import com.rf.exchange.module.member.dal.dataobject.frozen.MemberFrozenDO;
import com.rf.exchange.module.member.service.frozen.MemberFrozenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
@Tag(name = "用户 APP - 冻结金额")
@RestController
@RequestMapping("/member/frozen")
@Validated
@Slf4j
public class AppMemberFrozenController {
    @Resource
    private MemberFrozenService memberFrozenService;
    @GetMapping("/page")
    @Operation(summary = "获得会员账变分页")
    @PreAuthenticated
    public CommonResult<List<AppMemberFrozenRespVO>> getTransactionsPage() {
        List<MemberFrozenDO> pageResult = memberFrozenService.getListByUserId(WebFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(pageResult, AppMemberFrozenRespVO.class));
    }
}
