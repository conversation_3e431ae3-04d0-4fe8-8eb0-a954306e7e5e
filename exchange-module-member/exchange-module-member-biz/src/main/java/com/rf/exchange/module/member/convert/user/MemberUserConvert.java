package com.rf.exchange.module.member.convert.user;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.ip.core.utils.AreaUtils;
import com.rf.exchange.module.member.api.user.dto.MemberUserConfigRespDTO;
import com.rf.exchange.module.member.api.user.dto.MemberUserRespDTO;
import com.rf.exchange.module.member.controller.admin.user.vo.MemberUserRespVO;
import com.rf.exchange.module.member.controller.admin.user.vo.MemberUserUpdateReqVO;
import com.rf.exchange.module.member.controller.app.user.vo.AppMemberUserInfoRespVO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserConfigDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;


//@Mapper(uses = {AddressConvert.class})
@Mapper
public interface MemberUserConvert {

    MemberUserConvert INSTANCE = Mappers.getMapper(MemberUserConvert.class);

    AppMemberUserInfoRespVO convert(MemberUserDO bean);

//    @Mapping(source = "level", target = "level")
//    @Mapping(source = "bean.experience", target = "experience")
//    AppMemberUserInfoRespVO convert(MemberUserDO bean, MemberLevelDO level);

    MemberUserRespDTO convert2(MemberUserDO bean);

    MemberUserDO convertR2(MemberUserRespDTO bean);

    List<MemberUserRespDTO> convertList2(List<MemberUserDO> list);

    MemberUserDO convert(MemberUserUpdateReqVO bean);

    PageResult<MemberUserRespVO> convertPage(PageResult<MemberUserDO> page);

    @Mapping(source = "areaId", target = "areaName", qualifiedByName = "convertAreaIdToAreaName")
    MemberUserRespVO convert3(MemberUserDO bean);

    MemberUserConfigRespDTO convert4(MemberUserConfigDO bean);

    Map<Long, MemberUserConfigRespDTO> convertMap(Map<Long, MemberUserConfigDO> beanMap);

//    default PageResult<MemberUserRespVO> convertPage(PageResult<MemberUserDO> pageResult) {
//        PageResult<MemberUserRespVO> result = convertPage(pageResult);
//        // 处理关联数据
//        Map<Long, String> tagMap = convertMap(tags, MemberTagDO::getId, MemberTagDO::getName);
//        Map<Long, String> levelMap = convertMap(levels, MemberLevelDO::getId, MemberLevelDO::getName);
//        Map<Long, String> groupMap = convertMap(groups, MemberGroupDO::getId, MemberGroupDO::getName);
//        // 填充关联数据
//        result.getList().forEach(user -> {
//            user.setTagNames(convertList(user.getTagIds(), tagMap::get));
//            user.setLevelName(levelMap.get(user.getLevelId()));
//            user.setGroupName(groupMap.get(user.getGroupId()));
//        });
//        return result;
//    }

    @Named("convertAreaIdToAreaName")
    default String convertAreaIdToAreaName(Integer areaId) {
        return AreaUtils.format(areaId);
    }
}
