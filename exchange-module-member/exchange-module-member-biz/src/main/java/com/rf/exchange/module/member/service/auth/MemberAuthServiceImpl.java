package com.rf.exchange.module.member.service.auth;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.enums.UserTypeEnum;
import com.rf.exchange.framework.common.util.monitor.TracerUtils;
import com.rf.exchange.framework.common.util.servlet.ServletUtils;
import com.rf.exchange.framework.ip.core.Area;
import com.rf.exchange.framework.ip.core.utils.IPUtils;
import com.rf.exchange.module.member.controller.app.auth.vo.*;
import com.rf.exchange.module.member.convert.auth.AuthConvert;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.enums.user.UserPwdWrongType;
import com.rf.exchange.module.member.service.user.MemberUserService;
import com.rf.exchange.module.system.api.logger.LoginLogApi;
import com.rf.exchange.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.rf.exchange.module.system.api.mail.MailCodeApi;
import com.rf.exchange.module.system.api.mail.dto.MailCodeSendReqDTO;
import com.rf.exchange.module.system.api.oauth2.OAuth2TokenApi;
import com.rf.exchange.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import com.rf.exchange.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import com.rf.exchange.module.system.api.sms.SmsCodeApi;
import com.rf.exchange.module.system.enums.logger.LoginLogTypeEnum;
import com.rf.exchange.module.system.enums.logger.LoginResultEnum;
import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
import com.rf.exchange.module.system.enums.oauth2.OAuth2ClientConstants;
import com.rf.exchange.module.system.enums.sms.SmsSceneEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.VALUE_ERROR;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.util.servlet.ServletUtils.getClientIP;
import static com.rf.exchange.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAuthServiceImpl implements MemberAuthService {

    private static final Pattern IS_ALL_NUMBER = Pattern.compile("[0-9+-]+");
    private static final Pattern DOMAIN_PATTERN = Pattern.compile("([a-zA-Z0-9-]+)\\.([a-zA-Z0-9-]+\\.[a-zA-Z]{2,})$");

    @Resource
    private MemberUserService userService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private MailCodeApi mailCodeApi;
    @Resource
    private LoginLogApi loginLogApi;
    @Resource
    private OAuth2TokenApi oauth2TokenApi;

    @Override
    public void register(AppAuthRegisterReqVO reqVO) {
//        final String account = reqVO.getAccount().trim();
//        Area area= AreaUtils.getArea(reqVO.getAreaId());
//        userService.createUserWithAccount(area,reqVO.getIcode(), account, reqVO.getPassword(), getClientIP(), getTerminal());
        userService.register(reqVO);
    }

//    @Override
//    public void mailRegister(AppAuthRegisterEmailReqVO reqVO) {
//        final String email = reqVO.getEmail().trim();
//        Area area= AreaUtils.getArea(reqVO.getAreaId());
//        userService.createUserWithEmail(area,reqVO.getIcode(),email, reqVO.getPassword(), getClientIP(), getTerminal());
//    }
//
//    @Override
//    public void mobileRegister(AppAuthRegisterMobileReqVO reqVO) {
//        final String mobile = reqVO.getMobile().replaceAll("\\s+", "");
//        Area area= AreaUtils.getArea(reqVO.getAreaId());
//        userService.createUserWithMobile(area,reqVO.getIcode(),mobile, reqVO.getPassword(), getClientIP(), getTerminal());
//    }

    @Override
    public void logout(String token) {
        // 删除访问令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.removeAccessToken(token);
        if (accessTokenRespDTO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenRespDTO.getUserId());
    }

    @Override
    @DSTransactional
    public AppAuthLoginRespVO login(AppAuthLoginReqVO reqVO) {
        String userIp = getClientIP();
        Area area = IPUtils.getArea(userIp);
        MemberUserDO user;
        if (PatternPool.EMAIL.matcher(reqVO.getAccount()).matches()) {
            user = innerPasswordLogin(reqVO.getAccount(), reqVO.getPassword(), LoginLogTypeEnum.LOGIN_EMAIL);
        } else {
            // 如果是全字母则只可能是账号登录
            if (Validator.isMobile(reqVO.getAccount())) {
                user = innerPasswordLogin(reqVO.getAccount(), reqVO.getPassword(), LoginLogTypeEnum.LOGIN_MOBILE);
            } else {
                // 使用手机 + 密码，进行登录
                user = innerPasswordLogin(reqVO.getAccount(), reqVO.getPassword(), LoginLogTypeEnum.LOGIN_USERNAME);
            }
        }
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, reqVO.getAccount(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    @Override
    public void sendSmsCode(Long userId, AppSmsCodeSendReqVO reqVO) {
        // 情况 1：如果是修改手机场景，需要校验新手机号是否已经注册，说明不能使用该手机了
        SmsSceneEnum scene = SmsSceneEnum.getCodeByScene(reqVO.getScene());
        switch (scene) {
            case MEMBER_REGISTER: {
                MemberUserDO user = userService.getUser(reqVO.getMobile());
                if (user != null) {
                    throw exception(USER_MOBILE_USED);
                }
                break;
            }
            case MEMBER_LOGIN:
            case MEMBER_RESET_PASSWORD: {
                MemberUserDO user = userService.getUser(reqVO.getMobile());
                if (user == null) {
                    throw exception(USER_MOBILE_NOT_EXISTS);
                }
                break;
            }
            case MEMBER_UPDATE_PASSWORD:
            case MEMBER_UPDATE_MOBILE: {
                MemberUserDO user = userService.getUser(userId);
                if (user == null) {
                    throw exception(USER_NOT_EXISTS);
                }
                break;
            }
            case ADMIN_MEMBER_LOGIN: {
                break;
            }
        }
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user != null && !Objects.equals(user.getId(), userId)) {
//                throw exception(AUTH_MOBILE_USED);
//            }
//        }
//        // 情况 2：如果是重置密码场景，需要校验手机号是存在的
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_RESET_PASSWORD.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user == null) {
//                throw exception(USER_MOBILE_NOT_EXISTS);
//            }
//        }
//        // 情况 3：如果是修改密码场景，需要查询手机号，无需前端传递
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_PASSWORD.getScene())) {
//            MemberUserDO user = userService.getUser(userId);
//            // FIXME: 后续 member user 手机非强绑定，这块需要做下调整；
//            if (user == null) {
//                throw exception(USER_MOBILE_NOT_EXISTS);
//            }
//            reqVO.setMobile(user.getMobile());
//        }
        // 执行发送
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }

    @Override
    public void sendMailCode(Long userId, AppMailCodeSendReqVO reqVO, String lang, Long tenantId) {
        MailSceneEnum scene = MailSceneEnum.getCodeByScene(reqVO.getScene());
        MemberUserDO user = null;
        switch (scene) {
            case MEMBER_REGISTER: {
                user = userService.getUserByEmail(reqVO.getEmail());
                if (user != null) {
                    throw exception(USER_EMAIL_USED);
                }
                break;
            }
            case MEMBER_LOGIN:
            case MEMBER_RESET_PASSWORD: {
                user = userService.getUserByEmail(reqVO.getEmail());
                if (user == null) {
                    throw exception(USER_EMAIL_NOT_EXISTS);
                }
            }
            case MEMBER_UPDATE_PASSWORD:
            case MEMBER_UPDATE_EMAIL: {
                if (null == userId) {
                    throw exception(VALUE_ERROR);
                }
                user = userService.getUser(userId);
                if (user == null) {
                    throw exception(USER_NOT_EXISTS);
                }
            }
        }

        MailCodeSendReqDTO mailSendReqDTO = AuthConvert.INSTANCE.convert(reqVO);
        mailSendReqDTO.setScene(scene.getScene());
        mailSendReqDTO.setEmail(reqVO.getEmail());
        if (null != user) {
            mailSendReqDTO.setToUserId(user.getId());
            mailSendReqDTO.setToUsername(user.getUsername());
        }
        mailSendReqDTO.setCreateIp(getClientIP());
        mailSendReqDTO.setTenantId(tenantId);
        mailSendReqDTO.setToUserType(UserTypeEnum.MEMBER.getValue());
        if (StrUtil.isEmpty(lang)) {
            mailSendReqDTO.setLang("en");
        } else {
            mailSendReqDTO.setLang(lang);
        }

        if (StrUtil.isNotEmpty(reqVO.getDomain())) {
            String domain = reqVO.getDomain();
            try {
                // 处理domain
                final URI uri = new URI(reqVO.getDomain());
                final Matcher matcher = DOMAIN_PATTERN.matcher(uri.getHost());
                if (matcher.find()) {
                    domain = matcher.group(2);
                }
            } catch (URISyntaxException e) {
                log.error("发送邮件时 请求参数domain处理异常:[{}] domain:[{}]", e.getMessage(), domain);
            }
            mailSendReqDTO.setDomain(domain);
        }
        mailCodeApi.sendMailCode(mailSendReqDTO);
    }

    //    @Override
//    public AppAuthLoginRespVO mobileLogin(AppAuthMobileLoginReqVO reqVO) {
//        // 使用手机 + 密码，进行登录。
//        MemberUserDO user = innerPasswordLogin(reqVO.getMobile(), reqVO.getPassword(), LoginLogTypeEnum.LOGIN_MOBILE);
//        // 创建 Token 令牌，记录登录日志
//        return createTokenAfterLoginSuccess(user, reqVO.getMobile(), LoginLogTypeEnum.LOGIN_MOBILE);
//    }
//
//    @Override
//    public AppAuthLoginRespVO emailLogin(AppAuthEmailLoginReqVO reqVO) {
//        // 使用邮件 + 密码, 进行登录
//        MemberUserDO user = innerPasswordLogin(reqVO.getEmail(), reqVO.getPassword(), LoginLogTypeEnum.LOGIN_EMAIL);
//        return createTokenAfterLoginSuccess(user, reqVO.getEmail(), LoginLogTypeEnum.LOGIN_EMAIL);
//    }
//
//    @Override
//    @DSTransactional
//    public AppAuthLoginRespVO smsCodeLogin(AppAuthSmsLoginReqVO reqVO) {
//        // 校验验证码
//        String userIp = getClientIP();
//        smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.MEMBER_LOGIN.getScene(), userIp));
//        Area area=AreaUtils.getArea(reqVO.getAreaId());
//        // 获得获得注册用户, 不存在则注册用户
//        MemberUserDO user = userService.createUserWithMobileWithoutPasswordIfAbsent(area,reqVO.getIcode(),reqVO.getMobile(), userIp, getTerminal());
//        if (user == null) {
//            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
//        }
//        // 创建 Token 令牌，记录登录日志
//        return createTokenAfterLoginSuccess(user, reqVO.getMobile(), LoginLogTypeEnum.LOGIN_SMS);
//    }

    /**
     * 登录成功之后创建token
     *
     * @param user    用户对象
     * @param account 登录账号
     * @param logType 日志类型
     * @return 登录信息
     */
    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberUserDO user, String account, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(user.getId(), account, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                .setUserId(user.getId()).setUserType(getUserType().getValue())
                .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT));
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenRespDTO);
    }

    private MemberUserDO innerPasswordLogin(String account, String password, LoginLogTypeEnum logTypeEnum) {
        // 校验账号是否存在
        MemberUserDO user = userService.getUser(account);
//        if (LoginLogTypeEnum.LOGIN_USERNAME.equals(logTypeEnum)) {
//            user = userService.getUserByAccount(account);
//        } else if (LoginLogTypeEnum.LOGIN_MOBILE.equals(logTypeEnum)) {
//            user = userService.getUserByMobile(account);
//        } else if (LoginLogTypeEnum.LOGIN_EMAIL.equals(logTypeEnum)) {
//            user = userService.getUserByEmail(account);
//        }
        if (user == null) {
            createLoginLog(null, account, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), account, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            // 统计用户的密码错误次数
            userService.incrPwdWrongCount(user.getTenantId(), user.getId(), UserPwdWrongType.LOGIN_PWD);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), account, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    private void createLoginLog(Long userId, String account, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(account);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, getClientIP());
        }
    }

//    @Override
//    public void sendSmsCode(Long userId, AppAuthSmsSendReqVO reqVO) {
//        // 情况 1：如果是修改手机场景，需要校验新手机号是否已经注册，说明不能使用该手机了
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user != null && !Objects.equals(user.getId(), userId)) {
//                throw exception(AUTH_MOBILE_USED);
//            }
//        }
//        // 情况 2：如果是重置密码场景，需要校验手机号是存在的
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_RESET_PASSWORD.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user == null) {
//                throw exception(USER_MOBILE_NOT_EXISTS);
//            }
//        }
//        // 情况 3：如果是修改密码场景，需要查询手机号，无需前端传递
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_PASSWORD.getScene())) {
//            MemberUserDO user = userService.getUser(userId);
//            // FIXME: 后续 member user 手机非强绑定，这块需要做下调整；
//            if (user == null) {
//                throw exception(USER_MOBILE_NOT_EXISTS);
//            }
//            reqVO.setMobile(user.getMobile());
//        }
//        // 执行发送
//        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
//    }
//
//    @Override
//    public void validateSmsCode(Long userId, AppAuthSmsValidateReqVO reqVO) {
//        smsCodeApi.validateSmsCode(AuthConvert.INSTANCE.convert(reqVO));
//    }
//
//    @Override
//    public void sendMailCode(Long userId, AppAuthEmailSendReqVO reqVO) {
//        // 发送邮箱验证码
//        mailCodeApi.sendMailCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(ServletUtils.getClientIP()));
//    }
//
//    @Override
//    public void validateMailCode(Long userId, AppAuthEmailValidateReqVO reqVO) {
//        // 验证邮箱验证码
//        mailCodeApi.validateMailCode(AuthConvert.INSTANCE.convert(reqVO));
//    }

    @Override
    public AppAuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenRespDTO accessTokenDO = oauth2TokenApi.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    private void createLogoutLog(Long userId) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(LoginLogTypeEnum.LOGOUT_SELF.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(getMobile(userId));
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogApi.createLoginLog(reqDTO);
    }

    private String getMobile(Long userId) {
        if (userId == null) {
            return null;
        }
        MemberUserDO user = userService.getUser(userId);
        return user != null ? user.getMobile() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }

}
