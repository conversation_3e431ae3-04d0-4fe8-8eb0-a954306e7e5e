package com.rf.exchange.module.member.controller.app.withdraw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 提现申请")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberWithdrawCreateReqVO {
    @Schema(description = "钱包ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @Positive
    private Long walletId;
    @Schema(description = "提现金额",requiredMode = Schema.RequiredMode.REQUIRED,example = "1000")
    @Positive
    private BigDecimal amount;
    @Schema(description = "用户提现备注",requiredMode = Schema.RequiredMode.NOT_REQUIRED,example = "")
    private String remark;
    @Schema(description = "资金密码",requiredMode = Schema.RequiredMode.REQUIRED,example = "123456789")
    private String fundPassword;
}
