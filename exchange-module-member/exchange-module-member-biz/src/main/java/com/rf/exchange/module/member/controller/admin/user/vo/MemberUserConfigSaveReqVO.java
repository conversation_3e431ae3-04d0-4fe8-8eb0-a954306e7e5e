package com.rf.exchange.module.member.controller.admin.user.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.config.MemberConfigProfitTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 会员新增/修改 Request VO")
@Data
public class MemberUserConfigSaveReqVO {

    @Schema(description = "memberId", requiredMode = Schema.RequiredMode.REQUIRED, example = "6838")
    @Positive(message = "会员id不能为空")
    private Long memberId;

    @Schema(description = "盈利类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "31128")
    @NotNull(message = "盈利类型不能为空")
    @InEnum(MemberConfigProfitTypeEnum.class)
    private Integer profitType;

    @Schema(description = "随机盈利的赢率", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private BigDecimal randomRate;

}