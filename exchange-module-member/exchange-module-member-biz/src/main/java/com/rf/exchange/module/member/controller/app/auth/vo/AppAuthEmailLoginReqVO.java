//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.Email;
//import jakarta.validation.constraints.NotEmpty;
//import jakarta.validation.constraints.Pattern;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.validator.constraints.Length;
//
///**
// * <AUTHOR>
// * @since 2024-06-06
// */
//@Schema(description = "用户 APP - 邮箱 + 密码登录 Request VO")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class AppAuthEmailLoginReqVO {
//
//    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
//    @NotEmpty(message = "邮箱不能为空")
//    @Email
//    private String email;
//
//    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "Password")
//    @NotEmpty(message = "密码不能为空")
//    @Length(min = 8, max = 16, message = "密码长度为 8-16 位")
//    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "密码格式为数字以及字母")
//    private String password;
//}
