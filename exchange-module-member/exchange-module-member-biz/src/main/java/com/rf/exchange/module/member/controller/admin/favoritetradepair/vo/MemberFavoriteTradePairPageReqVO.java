package com.rf.exchange.module.member.controller.admin.favoritetradepair.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员收藏交易对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberFavoriteTradePairPageReqVO extends PageParam {

    @Schema(description = "会员id", example = "9896")
    private Long userId;

    @Schema(description = "会员账号", example = "芋艿")
    private String username;

    @Schema(description = "交易对Code", example = "14518")
    private String tradePairCode;

    @Schema(description = "交易对名称", example = "赵六")
    private String tradePairName;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}