package com.rf.exchange.module.member.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;

@Schema(description = "用户 APP - 账号 + 密码登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppAuthLoginReqVO {

    @Schema(description = "登录账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "邮箱/账号/手机号码")
    @NotEmpty(message = "{ACCOUNT_NOT_EMPTY}")
    private String account;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "Password")
    @NotEmpty(message = "{PASSWORD_NOT_EMPTY}")
    //@Length(min = 6, max = 16, message = "{PASSWORD_NOT_LENGTH_6_16}")
    //@Pattern(regexp = "^[A-Za-z0-9]+$",message = "{PASSWORD_FORMATTER_ERROR}")
    private String password;


    //@Schema(description = "地区id(可以使用system/area/get-id-by-ip获取) ", requiredMode = Schema.RequiredMode.REQUIRED, example = "Password")
    //@NotEmpty(message = "地区id不能为空")
    //private Integer areaId;
}
