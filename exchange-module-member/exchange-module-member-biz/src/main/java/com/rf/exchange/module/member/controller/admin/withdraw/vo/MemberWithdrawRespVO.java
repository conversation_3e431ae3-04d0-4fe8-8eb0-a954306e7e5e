package com.rf.exchange.module.member.controller.admin.withdraw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员提现 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberWithdrawRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1648")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16296")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "货币id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11101")
    @ExcelProperty("货币id")
    private Long currencyId;

    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("货币名称")
    private String currencyName;

    @Schema(description = "币种汇率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种汇率")
    private BigDecimal currencyRate;

    @Schema(description = "币种金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("币种金额")
    private BigDecimal currencyAmount;

    @Schema(description = "提现金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("提现金额")
    private BigDecimal withdrawAmount;

//    @Schema(description = "操作金额", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("操作金额")
//    private BigDecimal deductionAmount;
//
//    @Schema(description = "转账金额", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("转账金额")
//    private BigDecimal transferAmount;

    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "607")
    @ExcelProperty("钱包id")
    private Long walletId;

    @Schema(description = "钱包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("钱包类型")
    private Integer walletType;

    @Schema(description = "钱包类型名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("钱包类型名")
    private String walletTypeName;

    @Schema(description = "钱包名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("钱包名")
    private String walletName;

    @Schema(description = "钱包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "27785")
    @ExcelProperty("钱包类型")
    private Integer walletAccount;

    @Schema(description = "银行地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("银行地址")
    private String bankAddress;

    @Schema(description = "分支银行", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分支银行")
    private String bankBranch;

    @Schema(description = "会员备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("会员备注")
    private String memberRemark;

    @Schema(description = "系统备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("系统备注")
    private String systemRemark;

    @Schema(description = "订单会员状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("订单会员状态")
    private Integer orderMemberStatus;

    @Schema(description = "订单系统状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("订单系统状态")
    private Integer orderSystemStatus;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理人")
    private String handler;

    @Schema(description = "处理时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("处理时间")
    private Long handleTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}