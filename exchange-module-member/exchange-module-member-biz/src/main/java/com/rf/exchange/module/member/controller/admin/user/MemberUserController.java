package com.rf.exchange.module.member.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.IdReqVO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.common.util.servlet.ServletUtils;
import com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils;
import com.rf.exchange.framework.tenant.core.controller.TenantBaseController;
import com.rf.exchange.framework.web.core.util.WebFrameworkUtils;
import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFrozenUpdateReqVO;
import com.rf.exchange.module.member.controller.admin.user.vo.*;
import com.rf.exchange.module.member.controller.app.auth.vo.AppAuthRegisterReqVO;
import com.rf.exchange.module.member.convert.user.MemberUserConvert;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserConfigDO;
import com.rf.exchange.module.member.dal.dataobject.user.MemberUserDO;
import com.rf.exchange.module.member.enums.user.AuthAccountTypeEnum;
import com.rf.exchange.module.member.service.auth.MemberAuthService;
import com.rf.exchange.module.member.service.config.MemberUserConfigService;
import com.rf.exchange.module.member.service.user.MemberUserService;
import com.rf.exchange.module.system.api.agent.AgentApi;
import com.rf.exchange.module.system.api.agent.dto.AgentBaseRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - APP会员用户")
@RestController
@RequestMapping("/member/user")
@Validated
public class MemberUserController extends TenantBaseController {

    @Resource
    private MemberUserService memberUserService;
    @Resource
    private MemberUserConfigService memberUserConfigService;
    @Resource
    private AgentApi agentApi;
    @Resource
    private MemberAuthService memberAuthService;

    @PostMapping("/create")
    @Operation(summary = "创建会员")
    @PreAuthorize("@ss.hasPermission('member:user:create')")
    public CommonResult<Boolean> createUser(@Valid @RequestBody MemberUserCreateReqVO reqVO) {
        AppAuthRegisterReqVO regVo = AppAuthRegisterReqVO.builder().registerIp(ServletUtils.getClientIP())
                .terminal(WebFrameworkUtils.getTerminal())
                .accountType(AuthAccountTypeEnum.ACCOUNT.getType())
                .username(reqVO.getUsername())
                .password(reqVO.getPassword())
                .agentCode(reqVO.getAgentCode())
                .nickname(reqVO.getNickname())
                .areaId(reqVO.getAreaId()).demo(true).build();
        memberAuthService.register(regVo);
        return success(Boolean.TRUE);
    }

    @PostMapping("/update")
    @Operation(summary = "更新会员用户")
    @PreAuthorize("@ss.hasPermission('member:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody MemberUserUpdateReqVO updateReqVO) {
        setupTenantId();
        AgentBaseRespDTO agent = agentApi.getAgent(updateReqVO.getAgentId());
        updateReqVO.setAgentName(agent.getName());
        memberUserService.updateUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员")
    @Parameter(name = "id", description = "会员id", required = true)
    @PreAuthorize("@ss.hasPermission('member:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        setupTenantId();
        memberUserService.deleteUser(id);
        return success(true);
    }

    @PostMapping("/update-balance")
    @Operation(summary = "更新会员用户余额")
    @PreAuthorize("@ss.hasPermission('member:user:update-balance')")
    public CommonResult<Boolean> updateUserBalance(@Valid @RequestBody MemberUserUpdateBalanceReqVO reqVO) {
        setupTenantId();
        memberUserService.updateUserBalance(WebFrameworkUtils.getLoginNickname(), reqVO);
        return success(true);
    }

    @PostMapping("/update-frozen")
    @Operation(summary = "更新会员用户冻结金额")
    @PreAuthorize("@ss.hasPermission('member:user:update-frozen')")
    public CommonResult<Boolean> updateUserFrozen(@Valid @RequestBody MemberFrozenUpdateReqVO reqVO) {
        setupTenantId();
        memberUserService.updateFrozenFundsRecord(reqVO);
        return success(true);
    }

    @PostMapping("/update-password")
    @Operation(summary = "强制修改会员登陆密码")
    @PreAuthorize("@ss.hasPermission('member:user:update-password')")
    public CommonResult<Boolean> updatePassword(@Valid @RequestBody MemberUserUpdatePasswordReqVO reqVO) {
        setupTenantId();
        memberUserService.setUserPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @PostMapping("/update-fund-password")
    @Operation(summary = "强制修改会员资金密码")
    @PreAuthorize("@ss.hasPermission('member:user:update-fund-password')")
    public CommonResult<Boolean> updateFundPassword(@Valid @RequestBody MemberUserUpdatePasswordReqVO reqVO) {
        setupTenantId();
        memberUserService.setFundPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @GetMapping("/available-update-agent")
    @Operation(summary = "获取修改会员代理的可用代理")
    @Parameter(name = "userId", description = "会员id", required = true)
    @PreAuthorize("@ss.hasAnyPermissions('member:user:update-user-agent')")
    public CommonResult<List<AgentBaseRespDTO>> availableUpdateAgent(@RequestParam("userId") Long userId) {
        setupTenantId();
        final MemberUserDO user = memberUserService.checkUserExists(userId);
        return success(agentApi.getUserMoveAvailableAgents(user.getAgentId()));
    }

    @PostMapping("/update-agent")
    @Operation(summary = "修改会员所属代理")
    @PreAuthorize("@ss.hasPermission('member:user:update-user-agent')")
    public CommonResult<Boolean> updateUserAgent(@Valid @RequestBody MemberUserUpdateAgentReqVO reqVO) {
        setupTenantId();
        memberUserService.updateUserParentAgent(SecurityFrameworkUtils.getLoginUser(), reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<MemberUserRespVO> getUser(@RequestParam("id") Long id) {
        setupTenantId();
        return success(memberUserService.getUserAndConfig(id));
    }

    @PostMapping("/page")
    @Operation(summary = "获得会员用户分页")
    @PreAuthorize("@ss.hasPermission('member:user:query')")
    public CommonResult<PageResult<MemberUserRespVO>> getUserPage(@Valid @RequestBody MemberUserPageReqVO pageVO) {
        setupTenantId();
        PageResult<MemberUserDO> pageResult = memberUserService.getUserPage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        PageResult<MemberUserRespVO> result = MemberUserConvert.INSTANCE.convertPage(pageResult);
        //取配置表
        List<Long> userIdList = pageResult.getList().parallelStream().map(MemberUserDO::getId).toList();
        Map<Long, MemberUserConfigDO> userConfigMap = memberUserConfigService.getConfigListByUserIdList(userIdList);

        // key: 代理id，value:关系树
        Map<Long, TreeMap<Long, AgentBaseRespDTO>> tmpLocalAgentTreeMap = new HashMap<>();
        //取代理的上级
        Set<Long> agentIds = result.getList().stream().map(MemberUserBaseVO::getAgentId).collect(Collectors.toSet());
        for (Long agentId : agentIds) {
            if (!tmpLocalAgentTreeMap.containsKey(agentId)) {
                final TreeMap<Long, AgentBaseRespDTO> ancestorMapOfAgent = agentApi.getAncestorMapOfAgent(agentId);
                tmpLocalAgentTreeMap.put(agentId, ancestorMapOfAgent);
            }
        }

        //合并数据，配置及代理上级
        result.getList().parallelStream().forEach(userResp -> {
            MemberUserConfigDO configDO = userConfigMap.get(userResp.getId());
            if (configDO != null) {
                userResp.setProfitType(configDO.getProfitType());
                userResp.setRandomRate(configDO.getRandomRate());
            }
            final TreeMap<Long, AgentBaseRespDTO> agentTreeMap = tmpLocalAgentTreeMap.get(userResp.getAgentId());
            if (null != agentTreeMap) {
                List<MemberUserRespVO.AgentNode> nodeList = new ArrayList<>();
                for (Map.Entry<Long, AgentBaseRespDTO> entry : agentTreeMap.entrySet()) {
                    AgentBaseRespDTO agent = entry.getValue();
                    final MemberUserRespVO.AgentNode agentNode = BeanUtils.toBean(agent, MemberUserRespVO.AgentNode.class);
                    nodeList.add(agentNode);
                }
                userResp.setAgentNodeList(nodeList);
            }
        });
        return success(result);
    }

    @PostMapping("/set-config")
    @Operation(summary = "修改会员配置")
    @PreAuthorize("@ss.hasPermission('member:user:set-config')")
    public CommonResult<Boolean> setConfig(@Valid @RequestBody MemberUserConfigSaveReqVO reqVO) {
        setupTenantId();
        memberUserConfigService.updateMemberUserConfig(reqVO);
        return success(true);
    }

    @PostMapping("/reset-pwd-wrong-count")
    @Operation(summary = "重置会员的密码输入错误次数")
    @PreAuthorize("@ss.hasPermission('member:user:reset-pwd-wrong-count')")
    public CommonResult<Boolean> resetUserPwdWrongCount(@Valid @RequestBody IdReqVO idReqVO) {
        setupTenantId();
        memberUserService.resetPasswordWrongCount(idReqVO.getId());
        return success(true);
    }

}
