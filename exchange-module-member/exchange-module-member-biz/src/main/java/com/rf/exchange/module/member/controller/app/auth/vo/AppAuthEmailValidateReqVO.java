//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import com.rf.exchange.framework.common.validation.InEnum;
//import com.rf.exchange.module.system.enums.mail.MailSceneEnum;
//import io.swagger.v3.oas.annotations.media.Schema;
//import jakarta.validation.constraints.Email;
//import jakarta.validation.constraints.NotEmpty;
//import jakarta.validation.constraints.NotNull;
//import jakarta.validation.constraints.Pattern;
//import lombok.Data;
//import lombok.experimental.Accessors;
//import org.hibernate.validator.constraints.Length;
//
//@Schema(description = "用户 APP - 校验邮箱验证码 Request VO")
//@Data
//@Accessors(chain = true)
//public class AppAuthEmailValidateReqVO {
//
//    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
//    @Email
//    private String email;
//
//    @Schema(description = "发送场景,对应 MailSceneEnum 枚举", example = "1")
//    @NotNull(message = "发送场景不能为空")
//    @InEnum(MailSceneEnum.class)
//    private Integer scene;
//
//    @Schema(description = "手机验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
//    @NotEmpty(message = "手机验证码不能为空")
//    @Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
//    @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
//    private String code;
//}
