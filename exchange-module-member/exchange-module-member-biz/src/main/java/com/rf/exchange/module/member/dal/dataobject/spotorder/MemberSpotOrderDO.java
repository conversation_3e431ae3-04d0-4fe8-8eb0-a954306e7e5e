package com.rf.exchange.module.member.dal.dataobject.spotorder;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员现货订单记录 DO
 *
 * <AUTHOR>
 */
@TableName("member_spot_order")
@KeySequence("member_spot_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberSpotOrderDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员账号
     */
    private String username;
    /**
     * 交易对id
     */
    private Long tradePairId;
    /**
     * 交易对名称
     */
    private String tradePairName;
    /**
     * 委托数量
     */
    private BigDecimal orderVolume;
    /**
     * 委托价格
     */
    private BigDecimal orderPrice;
    /**
     * 成交价格
     */
    private BigDecimal executionPrice;
    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;
    /**
     * 资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇
     */
    private Integer assetType;
    /**
     * 订单状态 0:进行中 1:已完成 2:失败
     */
    private Integer orderStatus;

    private Long agentId;
    private String agentName;
}