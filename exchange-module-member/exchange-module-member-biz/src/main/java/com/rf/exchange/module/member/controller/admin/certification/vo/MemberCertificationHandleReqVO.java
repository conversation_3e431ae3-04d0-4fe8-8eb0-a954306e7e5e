package com.rf.exchange.module.member.controller.admin.certification.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.certification.MemberCertificationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 会员身份认证处理")
@Data
@Builder
@ToString(callSuper = true)
public class MemberCertificationHandleReqVO {
    @Schema(description = "认证编号", example = "1")
    @Min(1)
    private Long id;
    @Schema(description = "认证状态，2成功，3失败", example = "2")
    @InEnum(MemberCertificationStatusEnum.class)
    private Integer status;

    @Schema(description = "认证备注，非必填", example = "照片不清晰")
    private String remark;
}
