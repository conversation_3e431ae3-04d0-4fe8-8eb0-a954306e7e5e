package com.rf.exchange.module.member.service.recharge;

import com.rf.exchange.module.member.controller.admin.fundsrecord.vo.MemberFundsRecordHandleReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordRechargeCreateReqVO;
import com.rf.exchange.module.member.controller.app.fundsrecord.vo.AppMemberFundsRecordPageReqVO;
import com.rf.exchange.module.member.controller.admin.recharge.vo.*;
import com.rf.exchange.module.member.dal.dataobject.recharge.MemberRechargeDO;
import com.rf.exchange.framework.common.pojo.PageResult;

/**
 * 会员充值 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberRechargeService {

    /**
     * 创建会员充值
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRecharge(Long userId, AppMemberFundsRecordRechargeCreateReqVO createReqVO);


    /**
     * 删除会员充值
     *
     * @param id 编号
     */
    void deleteRecharge(Long id);

    /**
     * 获得会员充值
     *
     * @param id 编号
     * @return 会员充值
     */
    MemberRechargeDO getRecharge(Long id);

    /**
     * 获得会员充值分页
     *
     * @param pageReqVO 分页查询
     * @return 会员充值分页
     */
    PageResult<MemberRechargeDO> getRechargePage(MemberRechargePageReqVO pageReqVO);

    /**
     * APP获得会员充值分页
     *
     * @param pageReqVO 分页查询
     * @return 会员充值分页
     */
    PageResult<MemberRechargeDO> getRechargePage(Long userId, AppMemberFundsRecordPageReqVO pageReqVO);

    /**
     * 处理会员充值请求
     * @param reqVO
     */
    void handleRecharge(String handler, MemberFundsRecordHandleReqVO reqVO);
}