package com.rf.exchange.module.member.controller.admin.assetsspot.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 会员现货资产 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberAssetsSpotRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19704")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26726")
    @ExcelProperty("会员id")
    private Long userId;

    @Schema(description = "会员账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("会员账号")
    private String username;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "228")
    @ExcelProperty("交易对id")
    private Long tradePairId;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("交易对名称")
    private String tradePairName;

    @Schema(description = "现货持仓量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("现货持仓量")
    private BigDecimal volume;

    @Schema(description = "可用余额(总余额为占用余额+可用余额)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("可用余额(总余额为占用余额+可用余额)")
    private BigDecimal balance;

    @Schema(description = "占用余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("占用余额")
    private BigDecimal balanceLocked;

    @Schema(description = "成交均价 (用于计算收益)", requiredMode = Schema.RequiredMode.REQUIRED, example = "29502")
    @ExcelProperty("成交均价 (用于计算收益)")
    private BigDecimal executionAveragePrice;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}