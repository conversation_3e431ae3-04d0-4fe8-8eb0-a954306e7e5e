package com.rf.exchange.module.member.dal.redis;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.rf.exchange.module.member.dal.redis.RedisKeyConstants.MEMBER_CONTRACT_UNREALIZED_PROFIT_LOSS;

/**
 * 用户合约收益的浮动盈亏的redis缓存
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Repository
public class MemberContractProfitLossRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存用户的合约持仓中的浮动盈亏
     *
     * @param userId     用户id
     * @param positionNo 持仓编号
     * @param profitLoss 浮动盈亏金额
     */
    void setUnrealizedProfitLoss(Long userId, String positionNo, BigDecimal profitLoss) {
        redisTemplate.opsForHash().put(formatKey(userId), positionNo, profitLoss);
    }

    /**
     * 获取用户所有持仓中的合约的浮动盈亏
     *
     * @param userId 用户id
     * @return 持仓的浮动盈亏
     */
    Map<String, BigDecimal> getUnrealizedProfitLoss(Long userId) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(formatKey(userId));
        if (CollectionUtil.isEmpty(entries)) {
            return Collections.emptyMap();
        }
        Map<String, BigDecimal> resultMap = new HashMap<>(entries.size());
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            resultMap.put(entry.getKey().toString(), new BigDecimal((String) entry.getValue()));
        }
        return resultMap;
    }

    private String formatKey(Long userId) {
        return MEMBER_CONTRACT_UNREALIZED_PROFIT_LOSS + ":" + userId;
    }
}
