//package com.rf.exchange.module.member.controller.app.auth.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
///**
// * 注册请求基类
// *
// * <AUTHOR>
// * @since 2024-06-06
// */
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class AppAuthRegisterVO {
//
//    @Schema(description = "邀请码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    private String icode;
//
//    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    private String password;
//
//    @Schema(description = "国家id", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
//    private int areaId;
//}
