package com.rf.exchange.module.member.service.spotorder;

import java.util.*;
import jakarta.validation.*;
import com.rf.exchange.module.member.controller.admin.spotorder.vo.*;
import com.rf.exchange.module.member.dal.dataobject.spotorder.MemberSpotOrderDO;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.PageParam;

/**
 * 会员现货订单记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberSpotOrderService {

//    /**
//     * 创建会员现货订单记录
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createSpotOrder( MemberSpotOrderSaveReqVO createReqVO);
//
//    /**
//     * 更新会员现货订单记录
//     *
//     * @param updateReqVO 更新信息
//     */
//    void updateSpotOrder( MemberSpotOrderSaveReqVO updateReqVO);

    /**
     * 删除会员现货订单记录
     *
     * @param id 编号
     */
    void deleteSpotOrder(Long id);

    /**
     * 获得会员现货订单记录
     *
     * @param id 编号
     * @return 会员现货订单记录
     */
    MemberSpotOrderDO getSpotOrder(Long id);

    /**
     * 获得会员现货订单记录分页
     *
     * @param pageReqVO 分页查询
     * @return 会员现货订单记录分页
     */
    PageResult<MemberSpotOrderDO> getSpotOrderPage(MemberSpotOrderPageReqVO pageReqVO);

}