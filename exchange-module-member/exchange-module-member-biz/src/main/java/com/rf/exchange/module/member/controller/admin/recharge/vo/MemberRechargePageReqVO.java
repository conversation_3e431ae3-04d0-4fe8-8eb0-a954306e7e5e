package com.rf.exchange.module.member.controller.admin.recharge.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员充值分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberRechargePageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "会员id", example = "13660")
    private Long userId;

    @Schema(description = "会员账号", example = "芋艿")
    private String username;

    @Schema(description = "币种", example = "11461")
    private Long currencyId;

    @Schema(description = "币种名称", example = "芋艿")
    private String currencyName;

    @Schema(description = "法币金额")
    private BigDecimal currencyAmount;

    @Schema(description = "到账法币汇率")
    private BigDecimal currencyRate;

    @Schema(description = "充值金额")
    private BigDecimal rechargeAmount;

    @Schema(description = "到账金额")
    private BigDecimal arrivalAmount;

    @Schema(description = "实付金额")
    private BigDecimal payAmount;

    @Schema(description = "支付方式，虚拟/银行")
    private Integer payMethod;

    @Schema(description = "订单用户状态", example = "1")
    private Integer orderMemberStatus;

    @Schema(description = "订单系统状态", example = "2")
    private Integer orderSystemStatus;

    @Schema(description = "会员备注", example = "随便")
    private String memberRemark;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] handleTime;

    @Schema(description = "系统备注", example = "你说的对")
    private String systemRemark;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}