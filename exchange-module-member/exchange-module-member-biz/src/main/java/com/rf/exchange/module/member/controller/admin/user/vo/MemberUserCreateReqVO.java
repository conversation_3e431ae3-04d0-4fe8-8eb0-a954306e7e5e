package com.rf.exchange.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 创建用户")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MemberUserCreateReqVO {
    @Schema(description = "登陆账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "lisa")
    @NotNull(message = "登陆账号不能为空")
    private String username;

    @Schema(description = "登陆密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789")
    @NotNull(message = "登陆密码不能为空")
    private String password;

    @Schema(description = "代理邀请码", requiredMode = Schema.RequiredMode.REQUIRED, example = "system")
    @NotNull(message = "代理邀请码不能为空")
    private String agentCode;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "lisa")
    @NotNull(message = "昵称不能为空")
    private String nickname;

    @Schema(description = "地区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16")
    @Positive(message = "地区id不能为空")
    private Integer areaId;

}
