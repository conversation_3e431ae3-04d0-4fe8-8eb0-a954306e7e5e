package com.rf.exchange.module.member.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import lombok.*;

import java.math.BigDecimal;

@TableName(value = "member_user_config", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserConfigDO  extends BaseNoDeleteDO {
    @TableId
    private Long memberId;
    /**
     * 盈利类型
     */
    private Integer profitType;
    /**
     * 随机盈利的赢率
     */
    private BigDecimal randomRate;

}
