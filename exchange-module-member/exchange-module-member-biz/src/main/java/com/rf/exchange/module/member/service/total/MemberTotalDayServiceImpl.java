package com.rf.exchange.module.member.service.total;

import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.query.QueryWrapperX;
import com.rf.exchange.module.member.dal.dataobject.total.MemberTotalDayDO;
import com.rf.exchange.module.member.dal.mysql.total.MemberTotalDayMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.rf.exchange.module.member.controller.admin.total.vo.*;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;


/**
 * 会员日统计数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberTotalDayServiceImpl implements MemberTotalDayService {

    @Resource
    private MemberTotalDayMapper dayTotalMapper;

    @Override
    @Master
    public Long createDayTotal(MemberTotalDaySaveReqVO createReqVO) {
        //删除原来的统计
        LambdaQueryWrapperX<MemberTotalDayDO> queryWrapperX=new LambdaQueryWrapperX<>();
        queryWrapperX.eq(MemberTotalDayDO::getUserId,createReqVO.getUserId()).eq(MemberTotalDayDO::getTotalTime,createReqVO.getTotalTime());
        dayTotalMapper.delete(queryWrapperX);
        // 插入
        MemberTotalDayDO dayTotal = BeanUtils.toBean(createReqVO, MemberTotalDayDO.class);
        dayTotalMapper.insert(dayTotal);
        // 返回
        return dayTotal.getId();
    }


    @Override
    @Slave
    public PageResult<MemberTotalDayDO> getDayTotalPage(MemberTotalDayPageReqVO pageReqVO) {
        return dayTotalMapper.selectPage(pageReqVO);
    }

}