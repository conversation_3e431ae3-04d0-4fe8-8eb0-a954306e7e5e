package com.rf.exchange.module.member.controller.app.fundsrecord.vo;

import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordPayMethodEnum;
import com.rf.exchange.module.member.enums.fundsrecord.MemberFundsRecordStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 提现列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberFundsRecordRespVO {
    private Long id;
    private String orderNo;
    private Integer opType;
    private String currencyCode;
    private String memberRemark;
    private BigDecimal usdtAmount;
    private BigDecimal currencyAmount;
    private Integer orderMemberStatus;
    private Long createTime;

    /**
     * 钱包类型，加密货币，银行
     */
    private Integer walletType;
    /**
     * 钱包名，链名，银行名
     */
    private String walletTypeName;
    /**
     * 银行户名
     */
    private String walletName;
    /**
     * 银行账号/钱包哈希地址
     */
    private String walletAccount;
    /**
     * 银行地址
     */
    private String walletBankAddress;
    /**
     * 银行支行
     */
    private String walletBankBranch;

    public String getOrderMemberStatusStr() {
        if (orderMemberStatus == null) return "";
        return MemberFundsRecordStatusEnum.getI18n(orderMemberStatus);
    }
}
