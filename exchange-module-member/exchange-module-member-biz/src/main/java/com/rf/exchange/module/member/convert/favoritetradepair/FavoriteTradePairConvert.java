package com.rf.exchange.module.member.convert.favoritetradepair;

import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.member.controller.app.favoritetradepair.vo.AppMemberFavoriteTradePairRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
@Mapper
public interface FavoriteTradePairConvert {

    FavoriteTradePairConvert INSTANCE = Mappers.getMapper(FavoriteTradePairConvert.class);

    AppMemberFavoriteTradePairRespVO convert(TradePairRespDTO dto);

    List<AppMemberFavoriteTradePairRespVO> convertList(List<TradePairRespDTO> list);
}
