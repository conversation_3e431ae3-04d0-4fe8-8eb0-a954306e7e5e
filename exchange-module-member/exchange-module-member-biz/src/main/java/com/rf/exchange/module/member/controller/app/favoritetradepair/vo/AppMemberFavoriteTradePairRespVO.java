package com.rf.exchange.module.member.controller.app.favoritetradepair.vo;

import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
@Schema(description = "用户 APP - 收藏/取消交易对")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberFavoriteTradePairRespVO implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private Long id;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private String name;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private String code;

    @Schema(description = "实时价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private String currentPrice = "";

    @Schema(description = "标记价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private String markPrice = "0.00";

    @Schema(description = "涨跌幅百分比", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private String percentage = "0.00";

    /**
     * {@link TradeTypeEnum}
     */
    @Schema(description = "交易类型 0:现货 1:合约期货 2:杠杆保证金", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer tradeType;

    /**
     * {@link TradeAssetTypeEnum}
     */
    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer assetType;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "30210")
    private Integer status;
}
