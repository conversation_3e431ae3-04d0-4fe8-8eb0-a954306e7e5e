package com.rf.exchange.module.member.controller.app.wallet.vo;

import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 钱包列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberWalletRespVO {

    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private Long id;
    @Schema(description = "钱包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private Integer type;

    @Schema(description = "钱包类型字符串", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    public String getTypeStr() {
        return MemberWalletTypeEnum.get(type).name();
    }

    @Schema(description = "类型名称，如银行名，或者链名", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    public String typeName;
    @Schema(description = "名称，如银行卡户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String name;
    @Schema(description = "账号，如银行卡号，或者钱包地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String account;
    @Schema(description = "银行地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String bankAddress;
    @Schema(description = "支行地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String bankBranch;
    @Schema(description = "地区ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private Long areaId;
    @Schema(description = "地区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String areaName;
    @Schema(description = "货币代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String currencyCode;
    @Schema(description = "货币名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private String currencyName;
}
