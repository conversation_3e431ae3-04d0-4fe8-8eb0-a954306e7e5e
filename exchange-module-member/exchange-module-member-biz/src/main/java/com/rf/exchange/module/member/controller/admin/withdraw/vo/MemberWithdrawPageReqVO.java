package com.rf.exchange.module.member.controller.admin.withdraw.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员提现分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberWithdrawPageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "会员id", example = "16296")
    private Long userId;

    @Schema(description = "会员账号", example = "赵六")
    private String username;

    @Schema(description = "货币id", example = "11101")
    private Long currencyId;

    @Schema(description = "货币名称", example = "李四")
    private String currencyName;

    @Schema(description = "币种汇率")
    private BigDecimal currencyRate;

    @Schema(description = "币种金额")
    private BigDecimal currencyAmount;

    @Schema(description = "提现金额")
    private BigDecimal withdrawAmount;

    @Schema(description = "操作金额")
    private BigDecimal deductionAmount;

    @Schema(description = "转账金额")
    private BigDecimal transferAmount;

    @Schema(description = "钱包id", example = "607")
    private Long walletId;

    @Schema(description = "钱包类型", example = "2")
    private Integer walletType;

    @Schema(description = "钱包类型名", example = "王五")
    private String walletTypeName;

    @Schema(description = "钱包名", example = "李四")
    private String walletName;

    @Schema(description = "钱包类型", example = "27785")
    private Integer walletAccount;

    @Schema(description = "银行地址")
    private String bankAddress;

    @Schema(description = "分支银行")
    private String bankBranch;

    @Schema(description = "会员备注", example = "随便")
    private String memberRemark;

    @Schema(description = "系统备注", example = "你猜")
    private String systemRemark;

    @Schema(description = "订单会员状态", example = "2")
    private Integer orderMemberStatus;

    @Schema(description = "订单系统状态", example = "2")
    private Integer orderSystemStatus;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理时间", example = "")
    //// @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] handleTime;

    @Schema(description = "创建时间")
    //@JsonSerialize(using = TimestampLocalDateTimeSerializer.class)
    //@JsonDeserialize(using = TimestampLocalDateTimeDeserializer.class)
    private Long[] createTime;

}