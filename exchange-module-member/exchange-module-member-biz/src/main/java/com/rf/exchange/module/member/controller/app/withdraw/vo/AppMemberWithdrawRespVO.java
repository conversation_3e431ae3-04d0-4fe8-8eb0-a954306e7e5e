package com.rf.exchange.module.member.controller.app.withdraw.vo;

import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 提现列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppMemberWithdrawRespVO {
    private Long id;
    private Integer type;
    private String getTypeStr(){
        return MemberWalletTypeEnum.get(type).name();
    }
    public String typeName;
    private String name;
    private String account;
    private String bankAddress;
    private String bankBranch;
    private String areaName;
    private String currencyName;
}
