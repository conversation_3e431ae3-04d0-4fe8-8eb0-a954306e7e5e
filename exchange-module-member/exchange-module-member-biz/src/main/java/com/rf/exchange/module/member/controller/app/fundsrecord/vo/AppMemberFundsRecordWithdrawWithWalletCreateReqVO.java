package com.rf.exchange.module.member.controller.app.fundsrecord.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.member.enums.wallet.MemberWalletTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class AppMemberFundsRecordWithdrawWithWalletCreateReqVO extends AppMemberFundsRecordWithdrawCreateReqVO{

    @Schema(description = "钱包类型，银行1，虚拟币2", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer walletType;
    @Schema(description = "钱包名称，如果是银行卡就是户名，虚拟钱包就是钱包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String name;
    @Schema(description = "钱包账户，如果是银行卡就是卡号，虚拟钱包就是地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********  0x16546541651")
    private String account;
    @Schema(description = "账户类型名称，如果是银行卡就是银行名，虚拟钱包就是链名", requiredMode = Schema.RequiredMode.REQUIRED, example = "儿童银行  BTC")
    private String typeName;
    @Schema(description = "银行地址，非必填", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx城市xxx县")
    private String bankAddress;
    @Schema(description = "支行地址，非必填", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx城市xxx县")
    private String bankBranch;
    @Schema(description = "地区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer areaId;
    @Schema(description = "币种代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "USD")
    private String currencyCode;
}
