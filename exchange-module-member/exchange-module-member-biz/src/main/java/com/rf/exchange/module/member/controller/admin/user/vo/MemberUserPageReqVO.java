package com.rf.exchange.module.member.controller.admin.user.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 会员用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "")
    private String username;

    @Schema(description = "代理Id", example = "")
    private Long agentId;

    @Schema(description = "代理", example = "")
    private String agentName;

    @Schema(description = "手机号", example = "15601691300")
    private String mobile;

    @Schema(description = "用户昵称", example = "李四")
    private String nickname;

    @Schema(description = "最后登录时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] loginDate;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

    @Schema(description = "会员标签编号列表", example = "[1, 2]")
    private List<Long> tagIds;

    @Schema(description = "会员等级编号", example = "1")
    private Long levelId;

    @Schema(description = "用户分组编号", example = "1")
    private Long groupId;

    @Schema(description = "是否试玩用户 true:试玩 false:非试玩")
    private Boolean demo;
}
