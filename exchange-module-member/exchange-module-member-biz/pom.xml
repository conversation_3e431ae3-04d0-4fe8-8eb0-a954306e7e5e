<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.rf.dev</groupId>
        <artifactId>exchange-module-member</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>exchange-module-member-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        会员 模块 API，暴露给其它模块调用
    </description>

    <dependencies>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-candle-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-exc-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-module-trade-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-common</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- 消息队列 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Web 防护 -->
        <dependency>
            <groupId>com.rf.dev</groupId>
            <artifactId>exchange-spring-boot-starter-protection</artifactId>
        </dependency>
    </dependencies>

</project>
