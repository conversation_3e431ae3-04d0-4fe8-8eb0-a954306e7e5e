<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.module.exc.dal.mysql.tradepairtenant.TradePairTenantMapper">

    <select id="selectTradePairTenantPage" resultType="java.util.Map">
        SELECT
        t.id as id,
        t.tenant_id as tenantId,
        t.status as status,
        t.sort as sort,
        t.hot as hot,
        t.is_default as isDefault,
        t.creator as creator,
        t.updater as updater,
        t.create_time as createTime,
        t.update_time as updateTime,
        tp.id as tradePairId,
        tp.name as name,
        tp.code as code,
        tp.base_asset as baseAsset,
        tp.quote_asset as quoteAsset,
        tp.trade_type as tradeType,
        tp.asset_type as assetType,
        tp.status as tradePairStatus,
        tp.source as source,
        tp.scale as scale,
        tp.mark_price as markPrice,
        tp.percentage as percentage
        FROM
        exchange_trade_pair_tenant t
        JOIN
        exchange_trade_pair tp ON t.trade_pair_id = tp.id
        <where>
            <if test="reqVO.tenantId != null">
                t.tenant_id = #{reqVO.tenantId}
            </if>
            <if test="reqVO.tradePairId != null">
                AND t.trade_pair_id = #{reqVO.tradePairId}
            </if>
            <if test="reqVO.code != null and reqVO.code != ''">
                AND tp.code = #{reqVO.code}
            </if>
            <if test="reqVO.baseAsset != null and reqVO.baseAsset != ''">
                AND tp.base_asset = #{reqVO.baseAsset}
            </if>
            <if test="reqVO.quotaAsset != null and reqVO.quotaAsset != ''">
                AND tp.quota_asset = #{reqVO.quotaAsset}
            </if>
            <if test="reqVO.status != null">
                AND t.status = #{reqVO.status}
            </if>
            <if test="reqVO.createTime != null and reqVO.createTime.length > 0">
                AND t.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
            </if>
        </where>
        ORDER BY t.sort
    </select>

</mapper>
