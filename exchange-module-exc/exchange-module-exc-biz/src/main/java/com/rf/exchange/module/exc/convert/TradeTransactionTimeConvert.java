package com.rf.exchange.module.exc.convert;

import com.rf.exchange.module.exc.api.tradetransactionhour.dto.TradeTransactionTimeDTO;
import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-03
 */
@Mapper
public interface TradeTransactionTimeConvert {

    TradeTransactionTimeConvert INSTANCE = Mappers.getMapper(TradeTransactionTimeConvert.class);

    TradeTransactionTimeDTO convert(TradeTransactionTimeDO hourDO);

    List<TradeTransactionTimeDTO> convertList(List<TradeTransactionTimeDO> hourDOList);
}
