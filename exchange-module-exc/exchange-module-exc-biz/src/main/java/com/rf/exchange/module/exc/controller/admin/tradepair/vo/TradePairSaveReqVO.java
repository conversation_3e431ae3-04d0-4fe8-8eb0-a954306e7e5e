package com.rf.exchange.module.exc.controller.admin.tradepair.vo;

import com.rf.exchange.framework.common.validation.CharOnly;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 系统交易对新增/修改 Request VO")
@Data
public class TradePairSaveReqVO implements Serializable {
    @Serial
    private final static long serialVersionUID = 1L;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long id;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "交易对名称不能为空")
    @Size(max = 64, message = "交易对名称不能超过64个字符")
    private String name;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易对代码不能为空")
    @Size(max = 20, message = "交易对代码不能超过10个字符")
    private String code;

    @Schema(description = "基础资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "基础资产不能为空")
    @Size(max = 10, message = "基础资产不能超过10个字符")
    @CharOnly
    private String baseAsset;

    @Schema(description = "报价资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "报价资产不能为空")
    @Size(max = 10, message = "报价资产不能超过10个字符")
    @CharOnly
    private String quoteAsset;

    @Schema(description = "交易类型 0:现货 1:合约期货 2:杠杆保证金", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "交易类型 0:现货 1:合约期货 2:杠杆保证金不能为空")
    private Integer tradeType;

    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇不能为空")
    private Integer assetType;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "三方数据源 0:polygon 1:alltick", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "三方数据源 0:polygon 1:alltick不能为空")
    private Integer source;

    @Schema(description = "是否自发币", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isCustom = false;

    @Schema(description = "是否完全复制参考币", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isCopy = false;

    @Schema(description = "参考交易对")
    private String referenceCode;

    @Schema(description = "K线开始时间")
    private Long candleStartTime;

    @Schema(description = "发行价格")
    private BigDecimal issuedPrice;

    @Schema(description = "每分钟成交额基数")
    private BigDecimal oneMinuteTurnover;
}