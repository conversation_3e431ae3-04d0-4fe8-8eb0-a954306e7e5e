package com.rf.exchange.module.exc.dal.mysql.tradepairtenant;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.dataobject.tradepairtenant.TradePairTenantDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 租户交易对 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradePairTenantMapper extends BaseMapperX<TradePairTenantDO> {

    default TradePairTenantDO selectDefaultTradePair(Long tenantId) {
        return selectOne(new LambdaQueryWrapperX<TradePairTenantDO>()
                .eqIfPresent(TradePairTenantDO::getTenantId, tenantId)
                .eq(TradePairTenantDO::getIsDefault, true));
    }

    IPage<Map<String, Object>> selectTradePairTenantPage(Page<?> page, @Param("reqVO") TradePairTenantPageReqVO reqVO);

    List<TradePairDO> selectTradePairTenantPage(@Param("reqVO") TradePairTenantPageReqVO reqVO);

    default List<TradePairTenantDO> selectTradePairByTenant(Long tenantId, int limit) {
        LambdaQueryWrapperX<TradePairTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(TradePairTenantDO::getTenantId, tenantId);
        wrapper.eq(TradePairTenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        if (limit > 0) {
            wrapper.last("limit " + limit);
        }
        return selectList(wrapper);
    }

    default TradePairTenantDO selectByTradePairId(Long tenantId, Long tradePairId) {
        LambdaQueryWrapperX<TradePairTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eqIfPresent(TradePairTenantDO::getTenantId, tenantId);
        wrapper.eqIfPresent(TradePairTenantDO::getTradePairId, tradePairId);
        return selectOne(wrapper);
    }
}