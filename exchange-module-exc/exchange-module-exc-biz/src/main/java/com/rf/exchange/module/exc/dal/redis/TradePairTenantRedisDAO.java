package com.rf.exchange.module.exc.dal.redis;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.util.collection.CollectionUtils;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.exc.dal.dataobject.tradepairtenant.TradePairTenantDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.rf.exchange.module.exc.dal.redis.RedisKeyConstants.TRADE_PAIR_TENANT;

/**
 * 租户交易对id的缓存
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public class TradePairTenantRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取指定tenantId的交易对缓存
     *
     * @param tenantId 租户id
     * @return 交易对列表
     */
    public List<TradePairTenantDO> get(Long tenantId) {
        String redisKey = formatKey(tenantId);
        return JsonUtils.parseArray(stringRedisTemplate.opsForValue().get(redisKey), TradePairTenantDO.class);
    }

    /**
     * 获取指定tenantId的交易对缓存
     *
     * @param list     交易对列表
     * @param tenantId 租户id
     */
    public void set(List<TradePairTenantDO> list, Long tenantId) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        String redisKey = formatKey(tenantId);
        String jsonString = JsonUtils.toJsonString(list);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString);
    }

    /**
     * 删除指定tenantId的交易对列表缓存
     *
     * @param tenantId 租户id
     */
    public void delete(Long tenantId) {
        stringRedisTemplate.delete(formatKey(tenantId));
    }

    /**
     * 批量删除租户的交易对列表缓存
     *
     * @param idList 租户id列表
     */
    public void deleteList(List<Long> idList) {
        List<String> redisKeys = CollectionUtils.convertList(idList, TradePairTenantRedisDAO::formatKey);
        stringRedisTemplate.delete(redisKeys);
    }

    private static String formatKey(Long tenantId) {
        return TRADE_PAIR_TENANT + ":" + tenantId;
    }
}
