package com.rf.exchange.module.exc.dal.dataobject.tradepair;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;

import java.math.BigDecimal;

@Deprecated
@TableName("exchange_trade_pair_control")
@Data
public class TradePairControlDO extends TenantBaseDO {
    @TableId
    private Long id;
    /**
     * 交易对id
     */
    private Long tradePairId;
    /**
     * 交易对编码
     */
    private String tradePairCode;
    /**
     * 控制开始时间
     */
    private Long startTime;
    /**
     * 控制结束时间
     */
    private Long endTime;

    /**
     * 控制结束时间价格
     */
    private BigDecimal endPrice;
    /**
     * 生成状态
     */
    private int status;
    /**
     * 每分钟成交额
     */
    private BigDecimal oneMinuteTurnover;

    private BigDecimal highPrice;
    private BigDecimal lowPrice;
}
