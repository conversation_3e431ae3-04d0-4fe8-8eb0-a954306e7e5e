package com.rf.exchange.module.exc.controller.admin.tradetypetenant;

import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.rf.exchange.framework.common.pojo.PageParam;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

import com.rf.exchange.framework.excel.core.util.ExcelUtils;

import com.rf.exchange.framework.apilog.core.annotation.ApiAccessLog;
import static com.rf.exchange.framework.apilog.core.enums.OperateTypeEnum.*;

import com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo.*;
import com.rf.exchange.module.exc.service.tradetypetenant.TradeAssetTypeTenantService;

@Tag(name = "管理后台 - 租户交易对类型配置")
@RestController
@RequestMapping("/exchange/trade-asset-type-tenant")
@Validated
public class TradeAssetTypeTenantController {

    @Resource
    private TradeAssetTypeTenantService tradeAssetTypeTenantService;

    @PostMapping("/create")
    @Operation(summary = "创建租户交易对类型配置")
    @PreAuthorize("@ss.hasPermission('exchange:trade-asset-type-tenant:create')")
    public CommonResult<Long> createTradeAssetTypeTenant(@Valid @RequestBody TradeAssetTypeTenantSaveReqVO createReqVO) {
        return success(tradeAssetTypeTenantService.createTradeAssetTypeTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户交易对类型配置")
    @PreAuthorize("@ss.hasPermission('exchange:trade-asset-type-tenant:update')")
    public CommonResult<Boolean> updateTradeAssetTypeTenant(@Valid @RequestBody TradeAssetTypeTenantSaveReqVO updateReqVO) {
        tradeAssetTypeTenantService.updateTradeAssetTypeTenant(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户交易对类型配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('exchange:trade-asset-type-tenant:delete')")
    public CommonResult<Boolean> deleteTradeAssetTypeTenant(@RequestParam("id") Long id) {
        tradeAssetTypeTenantService.deleteTradeAssetTypeTenant(id);
        return success(true);
    }

    @PostMapping("/page")
    @Operation(summary = "获得租户交易对类型配置分页")
    @PreAuthorize("@ss.hasPermission('exchange:trade-asset-type-tenant:query')")
    public CommonResult<PageResult<TradeAssetTypeTenantRespVO>> getTradeAssetTypeTenantPage(@Valid @RequestBody TradeAssetTypeTenantPageReqVO pageReqVO) {
        PageResult<TradeAssetTypeTenantDO> pageResult = tradeAssetTypeTenantService.getTradeAssetTypeTenantPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradeAssetTypeTenantRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户交易对类型配置 Excel")
    @PreAuthorize("@ss.hasPermission('exchange:trade-asset-type-tenant:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTradeAssetTypeTenantExcel(@Valid TradeAssetTypeTenantPageReqVO pageReqVO,
                                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TradeAssetTypeTenantDO> list = tradeAssetTypeTenantService.getTradeAssetTypeTenantPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户交易对类型配置.xls", "数据", TradeAssetTypeTenantRespVO.class,
                        BeanUtils.toBean(list, TradeAssetTypeTenantRespVO.class));
    }

}