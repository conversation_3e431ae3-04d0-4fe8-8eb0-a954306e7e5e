package com.rf.exchange.module.exc.api.tradepair;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.convert.TradePairConvert;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.redis.RedisKeyConstants;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import com.rf.exchange.module.exc.dal.redis.TradePairRedisDAO;
import com.rf.exchange.module.exc.service.tradepair.TradePairService;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.TRADE_PAIR_NOT_EXISTS;

/**
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
@Validated
public class TradePairApiImpl implements TradePairApi {

    @Resource
    private TradePairService tradePairService;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;
    @Resource
    private TradePairRedisDAO tradePairRedisDAO;

    @Override
    public TradePairRespDTO validateTradePairExists(Long id) {
        TradePairDO tradePairDO = tradePairService.validateTradePairExists(id);
        return TradePairConvert.INSTANCE.convert2(tradePairDO);
    }

    @Override
    public TradePairRespDTO validateTradePairExists(String code) {
        TradePairDO tradePairDO = tradePairService.validateTradePairExistsByCode(code);
        return TradePairConvert.INSTANCE.convert2(tradePairDO);
    }

    @Override
    public TradePairRespDTO getTradePair(Long id) {
        TradePairDO tradePair = tradePairService.getTradePair(id, CommonStatusEnum.ENABLE.getStatus());
        return BeanUtils.toBean(tradePair, TradePairRespDTO.class);
    }

    @Override
    public List<TradePairRespDTO> getTradePairEnableCached() {
        List<TradePairDO> tradePairDOList = tradePairService.getListEnableCached();
        return TradePairConvert.INSTANCE.convertList2(tradePairDOList);
    }

    @Override
    public List<TradePairRespDTO> getTradePairAll() {
        List<TradePairDO> tradePairDOList = tradePairService.getAll();
        return TradePairConvert.INSTANCE.convertList2(tradePairDOList);
    }

    @Override
    public List<TradePairRespDTO> getTradePairListCached() {
        List<TradePairDO> listCached = tradePairService.getListCached();
        return TradePairConvert.INSTANCE.convertList2(listCached);
    }

    @Override
    public TradePairRespDTO getTradePairCachedByCode(String code) {
        List<TradePairDO> listCached = tradePairService.getListCached();
        Optional<TradePairDO> first = listCached.stream().filter(tradePairDO -> tradePairDO.getCode().equals(code)).findFirst();
        if (first.isEmpty()) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        return first.map(TradePairConvert.INSTANCE::convert2).orElse(null);
    }

    @Override
    public List<TradePairRespDTO> getTradePairEnableListCached() {
        List<TradePairDO> listEnableCached = tradePairService.getListEnableCached();
        return TradePairConvert.INSTANCE.convertList2(listEnableCached);
    }

    @Override
    public Map<String, TradePairRespDTO> getTradePairMapCached() {
        List<TradePairRespDTO> listCached = getTradePairListCached();
        return listCached.stream().collect(Collectors.toMap(TradePairRespDTO::getCode, tradePairRespDTO -> tradePairRespDTO));
    }

    @Override
    public List<TradePairRespDTO> getTradePairListByIds(Set<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return List.of();
        }
        List<TradePairDO> allTradePairList = tradePairService.getListByIds(ids, CommonStatusEnum.ENABLE.getStatus());
        List<TradePairDO> list = allTradePairList.stream().filter(tradePairDO -> ids.contains(tradePairDO.getId())).collect(Collectors.toList());
        return TradePairConvert.INSTANCE.convertList2(list);
    }

    @Override
    public String getNameByCode(String code) {
        return tradePairService.getNameByCode(code);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.CUSTOM_TRADE_PAIR_LIST)
    public List<TradePairRespDTO> getCustomTradePair() {
        List<TradePairDO> list = tradePairService.getCustomAndCopyTradePair();
        final List<TradePairDO> result = list.stream().filter(TradePairDO::getIsCustom).collect(Collectors.toList());
        return BeanUtils.toBean(result, TradePairRespDTO.class);
    }

    @Override
    public List<TradePairRespDTO> getNot24HourTradePairs() {
        final List<TradePairDO> tradePairEnabledList = tradePairService.getListEnableCached();
        if (CollectionUtil.isEmpty(tradePairEnabledList)) {
            return List.of();
        }
        List<TradePairRespDTO> resultList = new ArrayList<>(tradePairEnabledList.size());
        for (TradePairDO tradePairEnabled : tradePairEnabledList) {
            if (!tradePairEnabled.getIs24Hour()) {
                resultList.add(TradePairConvert.INSTANCE.convert2(tradePairEnabled));
            }
        }
        return resultList;
    }

    @Override
    public List<TradePairRespDTO> getCopyTradeOfReference(String referenceCode) {
        final List<TradePairDO> allCopyTrades = tradePairRedisDAO.getAllCopyTradesOf(referenceCode);
        return BeanUtils.toBean(allCopyTrades, TradePairRespDTO.class);
    }
}
