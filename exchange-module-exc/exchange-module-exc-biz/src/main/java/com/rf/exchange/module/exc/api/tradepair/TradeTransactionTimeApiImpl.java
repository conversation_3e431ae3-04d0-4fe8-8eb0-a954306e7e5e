package com.rf.exchange.module.exc.api.tradepair;

import com.rf.exchange.module.exc.api.tradetransactionhour.TradeTransactionTimeApi;
import com.rf.exchange.module.exc.api.tradetransactionhour.dto.TradeTransactionTimeDTO;
import com.rf.exchange.module.exc.convert.TradeTransactionTimeConvert;
import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;
import com.rf.exchange.module.exc.service.tradetransactionhour.TradeTransactionTimeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-03
 */
@Service
@Validated
public class TradeTransactionTimeApiImpl implements TradeTransactionTimeApi {

    @Resource
    private TradeTransactionTimeService transactionTimeService;

    @Override
    public List<TradeTransactionTimeDTO> tradeTransactionTimes() {
        final List<TradeTransactionTimeDO> hours = transactionTimeService.getTransactionTimes();
        return TradeTransactionTimeConvert.INSTANCE.convertList(hours);
    }

    @Override
    public TradeTransactionTimeDTO getTradeTransactionTimeById(Long id) {
        return TradeTransactionTimeConvert.INSTANCE.convert(transactionTimeService.getTransactionTime(id));
    }
}
