package com.rf.exchange.module.exc.api.tradepair;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.convert.TradePairConvert;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.service.tradepairtenant.TradePairTenantService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */
@Service
@Validated
public class TradePairTenantApiImpl implements TradePairTenantApi {

    @Resource
    private TradePairTenantService tradePairTenantService;

    @Override
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        tradePairTenantService.copyDataFromTenant(sourceTenantId, targetTenantId);
    }

    @Override
    public TradePairRespDTO getDefaultTradePair(Long tenantId) {
        TradePairDO defaultTradePair = tradePairTenantService.getDefaultTradePair(tenantId);
        return TradePairConvert.INSTANCE.convert2(defaultTradePair);
    }

    @Override
    public TradePairRespDTO validateTradePairExists(Long tenantId, Long tradePairId) {
        TradePairDO tradePairDO = tradePairTenantService.validateTradePairExists(tenantId, tradePairId);
        return TradePairConvert.INSTANCE.convert2(tradePairDO);
    }

    @Override
    public TradePairRespDTO validateTradePairExists(Long tenantId, String tradePairCode) {
        TradePairDO tradePairDO = tradePairTenantService.validateTradePairExists(tenantId, tradePairCode);
        return TradePairConvert.INSTANCE.convert2(tradePairDO);
    }

    @Override
    public List<TradePairRespDTO> getTradePairListByCodes(Long tenantId, Set<String> codeSet) {
        List<TradePairDO> tradPairList = tradePairTenantService.getListCachedByTenantTradeCodes(tenantId, codeSet);
        return TradePairConvert.INSTANCE.convertList2(tradPairList);
    }

    @Override
    public List<TradePairRespDTO> getListCachedByType(Long tenantId, Integer tradeType, Integer assetType) {
        // 直接从 TradePairDO 获取数据，而不是通过 TradePairRespVO
        List<TradePairDO> tradePairDOList = tradePairTenantService.getListCachedByTenantId(tenantId);
        if (CollectionUtil.isEmpty(tradePairDOList)) {
            return Collections.emptyList();
        }

        // 应用过滤条件
        List<TradePairDO> filteredList = tradePairDOList.stream()
                .filter(tradePairDO -> (assetType == null || Objects.equals(tradePairDO.getAssetType(), assetType))
                        && (tradeType == null || Objects.equals(tradePairDO.getTradeType(), tradeType)))
                .collect(Collectors.toList());

        // 转换为 TradePairRespDTO
        return TradePairConvert.INSTANCE.convertList2(filteredList);
    }

}
