package com.rf.exchange.module.exc.controller.app.tradepair.vo;

import com.rf.exchange.framework.common.validation.InEnum;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-06-21
 */

@Schema(description = "用户 APP - 获取交易对")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppTradePairReqVO implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    /**
     * {@link TradeTypeEnum}
     */
    @Schema(description = "交易类型 0现货 1期货合约 2杠杆保证金 3限时 不传获取全部", example = "1")
    private Integer tradeType;

    /**
     * {@link TradeAssetTypeEnum}
     */
    @Schema(description = "资产类型 0加密货币 1股票 2大宗商品 3外汇", example = "1")
    @InEnum(value = TradeAssetTypeEnum.class,message = "{ASSET_Type_ERROR}")
    private Integer assetType;
}
