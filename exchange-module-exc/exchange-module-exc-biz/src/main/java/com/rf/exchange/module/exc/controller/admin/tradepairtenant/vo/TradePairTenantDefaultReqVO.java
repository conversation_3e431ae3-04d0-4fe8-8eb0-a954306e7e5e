package com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-16
 */
@Schema(description = "管理后台 - 租户默认交易对修改 Request VO")
@Data
public class TradePairTenantDefaultReqVO {
    @Schema(description = "租户交易对编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "是否为默认交易对 true:是 false:否", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "isDefault 不能为空")
    private Boolean isDefault;
}
