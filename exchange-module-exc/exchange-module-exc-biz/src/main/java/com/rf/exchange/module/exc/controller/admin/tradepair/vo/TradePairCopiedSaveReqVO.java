package com.rf.exchange.module.exc.controller.admin.tradepair.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@Schema(description = "管理后台 - 复制币的新增/修改 Request VO")
public class TradePairCopiedSaveReqVO implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long id;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "交易对名称不能为空")
    @Size(max = 64, message = "交易对名称不能超过64个字符")
    private String name;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易对代码不能为空")
    @Size(max = 20, message = "交易对代码不能超过10个字符")
    private String code;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "参考交易对")
    private String referenceCode;
}
