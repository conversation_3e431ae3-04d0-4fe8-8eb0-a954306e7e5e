package com.rf.exchange.module.exc.dal.redis;

/**
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface RedisKeyConstants {

    String PRIVATE_KEY_PREFIX = "exch:";

    /**
     * 交易对的缓存
     * <p>
     * KEY 格式: trade_pair:all
     * VALUE 数据格式：String 模版信息 *
     */
    String TRADE_PAIR = PRIVATE_KEY_PREFIX + "trade_pair";

    /**
     * 租户的交易对的缓存
     * <p>
     * KEY 格式: trade_pair_tenant:{id}
     * VALUE 数据格式：String 模版信息 *
     */
    String TRADE_PAIR_TENANT = PRIVATE_KEY_PREFIX + "trade_pair_tenant";

    /**
     * 租户的热门交易对
     */
    String HOT_TRADE_PAIR_TENANT = PRIVATE_KEY_PREFIX + "hot_trade_pair#30m";


    String CUSTOM_TRADE_PAIR_LIST = PRIVATE_KEY_PREFIX + "custom_trade_pair";


    String CUSTOM_TRADE_PAIR_CONTROL = PRIVATE_KEY_PREFIX + "custom_trade_pair_control:";

    /**
     * 复制币的引用交易对
     */
    String TRADE_PAIR_COPY_OF_REFERENCE = PRIVATE_KEY_PREFIX + "copy_of_reference";

    /**
     * 交易对的交易时间
     * key: 主键编号
     * value: 交易时间信息
     */
    String TRADE_PAIR_TRANSACTION_TIME = PRIVATE_KEY_PREFIX + "transaction_time";
}
