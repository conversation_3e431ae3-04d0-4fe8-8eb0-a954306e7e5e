package com.rf.exchange.module.exc.controller.admin.tradepair.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 系统交易对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradePairRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1670")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("交易对名称")
    private String name;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String code;

    @Schema(description = "基础资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("基础资产")
    private String baseAsset;

    @Schema(description = "报价资产", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("报价资产")
    private String quoteAsset;

    @Schema(description = "标记价格 (非实时价格)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelIgnore
    private String markPrice = "0.00"; // 默认为0.00

    @Schema(description = "涨跌幅百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelIgnore
    private String percentage = "0.00"; // 默认为0.00

    @Schema(description = "当前价格", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currentPrice = "0";

    @Schema(description = "小数点位数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer scale = 2;

    @Schema(description = "交易量", requiredMode = Schema.RequiredMode.REQUIRED)
    private String volume = "0";

    /**
     * {@link TradeTypeEnum}
     */
    @Schema(description = "交易类型 0:现货 1:合约期货 2:杠杆保证金", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("交易类型 0:现货 1:合约期货 2:杠杆保证金")
    private Integer tradeType;

    /**
     * {@link TradeAssetTypeEnum}
     */
    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇")
    private Integer assetType;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("开启状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "数据同步状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty("数据同步状态（0正常 1停用）")
    private Integer syncStatus;

    @Schema(description = "三方数据源 0:polygon 1:alltick", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方数据源 0:polygon 1:alltick")
    private Integer source;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "是否熱門", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("熱門")
    private boolean hot;

    @Schema(description = "是否默认", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "isDefault")
    private boolean isDefault;

    @Schema(description = "是否自发币", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "isCustom")
    private Boolean isCustom;

    @Schema(description = "是否复制币", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "isCopy")
    private Boolean isCopy;


    @Schema(description = "k线开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long candleStartTime;

    @Schema(description = "参考交易对", requiredMode = Schema.RequiredMode.REQUIRED)
    private String referenceCode;

    @Schema(description = "发行价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal issuedPrice;

    @Schema(description = "分钟成交额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal oneMinuteTurnover;

    @Schema(description = "交易对图标")
    private String icon;
}