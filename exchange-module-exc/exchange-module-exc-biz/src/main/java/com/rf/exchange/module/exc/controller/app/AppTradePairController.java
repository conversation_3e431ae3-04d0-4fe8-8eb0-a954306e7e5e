package com.rf.exchange.module.exc.controller.app;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.ratelimiter.core.annotation.RateLimiter;
import com.rf.exchange.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.controller.app.tradepair.vo.AppTradePairReqVO;
import com.rf.exchange.module.exc.controller.app.tradepair.vo.AppTradePairSearchReqVO;
import com.rf.exchange.module.exc.service.tradepairtenant.TradePairTenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Tag(name = "用户APP - 交易对")
@RestController
@RequestMapping("/exchange/trade-pair")
@Validated
public class AppTradePairController {

    @Resource
    private TradePairTenantService tradePairTenantService;

    @GetMapping("/hot")
    @Operation(summary = "获取热门交易对")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<List<TradePairRespVO>> hotTradePair() {
        return success(tradePairTenantService.getHotTradePair(getTenantId()));
    }

    @GetMapping("/all")
    @Operation(summary = "获取所有可用的交易对")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, count = 60, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<List<TradePairRespVO>> allTradePair(@Valid AppTradePairReqVO reqVO) {
        return success(tradePairTenantService.getListCachedByType(getTenantId(), reqVO.getTradeType(), reqVO.getAssetType()));
    }

    @PostMapping("/search")
    @Operation(summary = "搜索交易对")
    @PermitAll
    @RateLimiter(timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<List<TradePairRespVO>> searchTradePair(@Valid @RequestBody AppTradePairSearchReqVO reqVO) {
        return success(tradePairTenantService.appSearchTradePair(reqVO, getTenantId()));
    }
}
