package com.rf.exchange.module.exc.service.tradepair;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.UpdateStatusReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Set;

/**
 * 系统交易对 Service 接口
 *
 * <AUTHOR>
 */
public interface TradePairService {

    /**
     * 校验交易对是否存在
     *
     * @param id 交易对id
     * @return 交易对
     */
    TradePairDO validateTradePairExists(Long id);

    /**
     * 校验交易对是否存在
     *
     * @param code 交易对代码
     * @return 交易对
     */
    TradePairDO validateTradePairExistsByCode(String code);


    /**
     * 获取缓存的所有交易对
     * （包括停用的交易对）
     *
     * @return 交易对列表
     */
    List<TradePairDO> getListCached();

    /**
     * 获取系统所有交易对
     * （不包括停用的交易对）
     *
     * @return 交易对列表
     */
    List<TradePairDO> getListEnableCached();

    /**
     * 获取所有系统交易对
     * （包括停用的交易对）
     *
     * @return 交易对列表
     */
    List<TradePairDO> getAll();

    /**
     * 获取所有的复制币交易对
     * @return 交易对列表
     */
    List<TradePairDO> getAllCopyTradePairs();

    /**
     * 搜索系统交易对
     *
     * @return 交易对列表
     */
    List<TradePairDO> search(String keyword);

    /**
     * 获取系统指定id集合的交易对
     *
     * @param idSet  交易对id集合
     * @param status null查全部状态
     * @return 交易对列表
     */
    List<TradePairDO> getListByIds(Set<Long> idSet, Integer status);

    /**
     * 获取系统指定code集合的交易对
     *
     * @param codeSet 交易对code集合
     * @param status  状态
     * @return 交易对列表
     */
    List<TradePairDO> getListByCodes(Set<String> codeSet, Integer status);

    /**
     * 创建系统交易对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTradePair(@Valid TradePairSaveReqVO createReqVO);

    /**
     * 更新系统交易对
     *
     * @param updateReqVO 更新信息
     */
    void updateTradePair(@Valid TradePairSaveReqVO updateReqVO);

    /**
     * 更新交易对状态
     *
     * @param reqVO 更新信息
     */
    void updateTradePairStatus(@Valid UpdateStatusReqVO reqVO);

    /**
     * 更新交易对的数据同步状态
     *
     * @param reqVO 更新信息
     */
    void updateTradePairSyncStatus(@Valid UpdateStatusReqVO reqVO);

    /**
     * 删除系统交易对
     *
     * @param id 编号
     */
    void deleteTradePair(Long id);

    /**
     * 获得系统交易对信息
     *
     * @param id     编号
     * @param status null查所有状态
     * @return 系统交易对
     */
    TradePairDO getTradePair(Long id, Integer status);

    /**
     * 通过code获取交易对信息
     *
     * @param code   交易对代码
     * @param status null查所有状态
     * @return 交易对信息
     */
    TradePairDO getTradePairByCode(String code, Integer status);

    /**
     * 获得系统交易对分页
     *
     * @param pageReqVO 分页查询
     * @return 系统交易对分页
     */
    PageResult<TradePairDO> getTradePairPage(TradePairPageReqVO pageReqVO);

    /**
     * 通过编码获取名称
     *
     * @param code 交易对代码
     * @return 交易对名称
     */
    String getNameByCode(String code);

    /**
     * 获取要生成未来k线的自发币或复制币
     *
     * @return 自发币或者复制币
     */
    List<TradePairDO> getCustomAndCopyTradePair();

}