package com.rf.exchange.module.exc.framework.web.config;

import com.rf.exchange.framework.swagger.config.ExchangeSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024-06-23
 */
@Configuration(proxyBeanMethods = false)
public class ExcWebConfiguration {

    /**
     * pay 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi excGroupedOpenApi() {
        return ExchangeSwaggerAutoConfiguration.buildGroupedOpenApi("exchange", "交易所");
    }

}
