package com.rf.exchange.module.exc.dal.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rf.exchange.module.exc.dal.redis.RedisKeyConstants.TRADE_PAIR_TRANSACTION_TIME;

/**
 * 交易对的交易时间的redis 缓存
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Repository
public class TradeTransactionTimeRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public void updateAll(List<TradeTransactionTimeDO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Map<String, String> map = new HashMap<>(list.size());
        for (TradeTransactionTimeDO hourDO : list) {
            String json = JsonUtils.toJsonString(hourDO);
            map.put(hourDO.getId().toString(), json);
        }
        redisTemplate.opsForHash().putAll(TRADE_PAIR_TRANSACTION_TIME, map);
    }

    public TradeTransactionTimeDO get(long id) {
        final String obj = (String) redisTemplate.opsForHash().get(TRADE_PAIR_TRANSACTION_TIME, String.valueOf(id));
        if (StrUtil.isNotEmpty(obj)) {
            return JsonUtils.parseObject(obj, TradeTransactionTimeDO.class);
        }
        return null;
    }

    /**
     * 获取所有的交易时间缓存
     *
     * @return 交易时间缓存列表
     */
    public List<TradeTransactionTimeDO> getAll() {
        final Map<Object, Object> entries = redisTemplate.opsForHash().entries(TRADE_PAIR_TRANSACTION_TIME);
        if (CollUtil.isEmpty(entries)) {
            return List.of();
        }
        List<TradeTransactionTimeDO> list = new ArrayList<>(entries.size());
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            final TradeTransactionTimeDO hourDO = JsonUtils.parseObject(entry.getValue().toString(), TradeTransactionTimeDO.class);
            list.add(hourDO);
        }
        return list;
    }

    public void update(TradeTransactionTimeDO hourDO) {
        final String json = JsonUtils.toJsonString(hourDO);
        redisTemplate.opsForHash().put(TRADE_PAIR_TRANSACTION_TIME, hourDO.getId().toString(), json);
    }

    public void deleteAll() {
        redisTemplate.opsForHash().delete(TRADE_PAIR_TRANSACTION_TIME);
    }
}
