package com.rf.exchange.module.exc.controller.app.tradepair.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-07-27
 */
@Schema(description = "用户 APP - 搜索交易对")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppTradePairSearchReqVO {

    @Schema(description = "搜索关键字", example = "1")
    @NotEmpty(message = "{KEY_NOT_EMPTY}")
    private String keyword;
}
