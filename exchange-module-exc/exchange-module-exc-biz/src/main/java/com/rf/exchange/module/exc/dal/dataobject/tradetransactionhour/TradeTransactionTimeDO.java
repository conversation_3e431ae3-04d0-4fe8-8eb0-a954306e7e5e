package com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour;

import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import lombok.*;

import java.io.Serial;
import java.time.LocalTime;

/**
 * 交易对的交易时间
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@TableName("exchange_trade_transaction_time")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeTransactionTimeDO extends BaseNoDeleteDO {

    @Serial
    private final static long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 时令
     * {@link }
     */
    private String season;
    /**
     * 周几开市
     */
    private String dayOfWeekOpen;
    /**
     * 开市时间
     */
    private LocalTime marketOpen;
    /**
     * 周几休市
     */
    private String dayOfWeekClose;
    /**
     * 休市时间
     */
    private LocalTime marketClose;
    /**
     * 是否有日内时间
     */
    private Boolean hasDailyBreak;
    /**
     * 日内开市时间
     */
    private LocalTime dailyBreakStart;
    /**
     * 日内休市时间
     */
    private LocalTime dailyBreakEnd;
    /**
     * 时区
     */
    private String timezone;
}
