package com.rf.exchange.module.exc.api.tradepair;

import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairControlRespDTO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import com.rf.exchange.module.exc.service.tradepaircontrol.TradePairControlService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TradePairControlApiImpl implements TradePairControlApi {
    @Resource
    private TradePairControlService tradePairControlService;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;

    @Override
    public List<TradePairControlRespDTO> getTradePairControlToGen() {
        List<TradePairControlDO> list = tradePairControlService.getTradePairControlToGen();
        return BeanUtils.toBean(list, TradePairControlRespDTO.class);
    }

    @Override
    public TradePairControlRespDTO get(long id) {
        TradePairControlDO tradePairControlDO = tradePairControlService.get(id);
        return BeanUtils.toBean(tradePairControlDO, TradePairControlRespDTO.class);
    }

    @Override
    public void setTradePairControlFinish(long id) {
        tradePairControlService.setFinish(id);
    }

    @Override
    public TradePairControlRespDTO getCacheByCode(String code) {
        TradePairControlDO tradePairControlDO = tradePairControlRedisDAO.getTradePairControl(code);
        if (tradePairControlDO == null) return null;
        return BeanUtils.toBean(tradePairControlDO, TradePairControlRespDTO.class);
    }
}
