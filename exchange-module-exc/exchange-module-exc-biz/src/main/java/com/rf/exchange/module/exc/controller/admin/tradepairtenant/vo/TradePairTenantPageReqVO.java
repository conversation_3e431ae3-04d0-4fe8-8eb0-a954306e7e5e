package com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租户交易对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradePairTenantPageReqVO extends PageParam {

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "交易对编号", example = "15")
    private Long tradePairId;

    @Schema(description = "交易对代码", example = "")
    private String code;

    @Schema(description = "基础资产", example = "")
    private String baseAsset;

    @Schema(description = "报价资产", example = "")
    private String quotaAsset;

    @Schema(description = "开启状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}