package com.rf.exchange.module.exc.convert;

import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;
import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Mapper
public interface TradeAssetTypeConvert {

    TradeAssetTypeConvert INSTANCE = Mappers.getMapper(TradeAssetTypeConvert.class);

    TenantTradeAssetTypeRespDTO convert(TradeAssetTypeTenantDO tradeType);

    List<TenantTradeAssetTypeRespDTO> convertList(List<TradeAssetTypeTenantDO> tradeAssetTypeTenantDOList);
}
