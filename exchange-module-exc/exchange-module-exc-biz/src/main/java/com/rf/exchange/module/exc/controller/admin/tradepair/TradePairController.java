package com.rf.exchange.module.exc.controller.admin.tradepair;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.UpdateStatusReqVO;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.*;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.redis.TradePairRedisDAO;
import com.rf.exchange.module.exc.service.tradepair.TradePairService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants.*;
import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 系统交易对")
@RestController
@RequestMapping("/exchange/trade-pair")
@Validated
public class TradePairController {

    @Resource
    private TradePairService tradePairService;
    @Resource
    private TradePairRedisDAO tradPairRedisDAO;

    @PostMapping("/create")
    @Operation(summary = "创建系统交易对")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:create')")
    public CommonResult<Long> createTradePair(@Valid @RequestBody TradePairSaveReqVO createReqVO) {
        if (createReqVO.getIsCustom()) {
            if (!StringUtils.hasText(createReqVO.getReferenceCode())) {
                throw exception(CUSTOM_TRADE_PAIR_NEED_REFERENCE_TRADE_PAIR);
            }
            String tradePairDO = tradePairService.getNameByCode(createReqVO.getReferenceCode());
            if (!StringUtils.hasText(tradePairDO)) {
                throw exception(CUSTOM_REFERENCE_NOT_EXISTS);
            }
            // 非复制币则必须要
            if (!createReqVO.getIsCopy() && createReqVO.getIssuedPrice() == null) {
                throw exception(CUSTOM_TRADE_PAIR_NEED_ISSUED_PRICE);
            }
            if (!createReqVO.getIsCopy() && createReqVO.getOneMinuteTurnover() == null) {
                throw exception(CUSTOM_TRADE_PAIR_NEED_ONE_MINUTE_TURNOVER);
            }
        }
        Long id = tradePairService.createTradePair(createReqVO);
        clearCustomTradePair();
        return success(id);
    }

    @PostMapping("/create-copy")
    @Operation(summary = "创建复制币")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:create')")
    public CommonResult<Long> createTradePair(@Valid @RequestBody TradePairCopiedSaveReqVO createReqVO) {
        if (!StringUtils.hasText(createReqVO.getReferenceCode())) {
            throw exception(CUSTOM_TRADE_PAIR_NEED_REFERENCE_TRADE_PAIR);
        }
        String tradePairDO = tradePairService.getNameByCode(createReqVO.getReferenceCode());
        if (!StringUtils.hasText(tradePairDO)) {
            throw exception(CUSTOM_REFERENCE_NOT_EXISTS);
        }
        final TradePairDO referTradePair = tradePairService.getTradePairByCode(createReqVO.getReferenceCode(), CommonStatusEnum.ENABLE.getStatus());
        final TradePairSaveReqVO saveReqVO = BeanUtils.toBean(referTradePair, TradePairSaveReqVO.class);
        saveReqVO.setCode(createReqVO.getCode());
        saveReqVO.setReferenceCode(referTradePair.getReferenceCode());
        Long id = tradePairService.createTradePair(saveReqVO);
        // 清除引用交易对的缓存
        clearCopyTradePair(createReqVO.getReferenceCode());
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统交易对")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:update')")
    public CommonResult<Boolean> updateTradePair(@Valid @RequestBody TradePairSaveReqVO updateReqVO) {
        tradePairService.updateTradePair(updateReqVO);
        clearCustomTradePair();
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新系统交易对状态")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:update')")
    public CommonResult<Boolean> updateTradePairStatus(@Valid @RequestBody UpdateStatusReqVO updateReqVO) {
        tradePairService.updateTradePairStatus(updateReqVO);
        clearCustomTradePair();
        return success(true);
    }

    @PutMapping("/update-sync-status")
    @Operation(summary = "更新系统交易对的数据同步状态")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:update')")
    public CommonResult<Boolean> updateTradePairSyncStatus(@Valid @RequestBody UpdateStatusReqVO updateReqVO) {
        tradePairService.updateTradePairSyncStatus(updateReqVO);
        clearCustomTradePair();
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统交易对")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:delete')")
    public CommonResult<Boolean> deleteTradePair(@RequestParam("id") Long id) {
        tradePairService.deleteTradePair(id);
        clearCustomTradePair();
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统交易对")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:query')")
    public CommonResult<TradePairRespVO> getTradePair(@RequestParam("id") Long id) {
        TradePairDO tradePair = tradePairService.getTradePair(id, null);
        return success(BeanUtils.toBean(tradePair, TradePairRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统交易对分页")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair:query')")
    public CommonResult<PageResult<TradePairRespVO>> getTradePairPage(@Valid TradePairPageReqVO pageReqVO) {
        PageResult<TradePairDO> pageResult = tradePairService.getTradePairPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradePairRespVO.class));
    }

    @GetMapping(value = "/list-all-simple")
    @Operation(summary = "获取系统交易对全列表", description = "只包含名称和编码的所有列表")
    public CommonResult<List<TradePairSimpleRespVO>> getSimpleTradePairList() {
        List<TradePairDO> list = tradePairService.getListEnableCached();
        return success(BeanUtils.toBean(list, TradePairSimpleRespVO.class));
    }

    public void clearCustomTradePair() {
        tradPairRedisDAO.clearCustomTradePair();
    }

    public void clearCopyTradePair(String referCode) {
        tradPairRedisDAO.clearCopyTradePair(referCode);
    }
}