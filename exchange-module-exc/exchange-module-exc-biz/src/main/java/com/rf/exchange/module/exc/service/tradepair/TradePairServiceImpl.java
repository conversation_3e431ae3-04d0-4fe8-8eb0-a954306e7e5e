package com.rf.exchange.module.exc.service.tradepair;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.pojo.UpdateStatusReqVO;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.mysql.tradepair.TradePairMapper;
import com.rf.exchange.module.exc.dal.redis.TradePairRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.TRADE_PAIR_EXISTS;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.TRADE_PAIR_NOT_EXISTS;

/**
 * 系统交易对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradePairServiceImpl implements TradePairService {

    @Resource
    private TradePairMapper tradePairMapper;

    @Resource
    private TradePairRedisDAO tradePairRedisDAO;

    @Resource
    @Lazy
    private CandleDataApi candleDataApi;

    @Override
    @Slave
    public TradePairDO validateTradePairExists(Long id) {
        TradePairDO tradePair = getTradePair(id, null);
        if (tradePair == null) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        return tradePair;
    }

    @Override
    @Slave
    public TradePairDO validateTradePairExistsByCode(String code) {
        TradePairDO tradePairDO = getTradePairByCode(code, null);
        if (tradePairDO == null) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        return tradePairDO;
    }

    @Override
    @Slave
    public List<TradePairDO> getListCached() {
        // 从缓存中获取所有的系统交易对交易对列表
        List<TradePairDO> cachedTradePairList = tradePairRedisDAO.getAll();
        if (CollectionUtil.isNotEmpty(cachedTradePairList)) {
            return cachedTradePairList;
        }
        return getAll();
    }

    @Override
    @Slave
    public List<TradePairDO> getListEnableCached() {
        List<TradePairDO> all = getAll();
        return all.stream().filter(tradePairDO -> CommonStatusEnum.ENABLE.getStatus().equals(tradePairDO.getStatus())).collect(Collectors.toList());
    }

    @Override
    @Slave
    public List<TradePairDO> getAll() {
        List<TradePairDO> list = tradePairMapper.selectList();
        tradePairRedisDAO.setAll(list);
        return list;
    }

    @Override
    public List<TradePairDO> getAllCopyTradePairs() {
        LambdaQueryWrapperX<TradePairDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(TradePairDO::getIsCopy, true);
        wrapper.eq(TradePairDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        return tradePairMapper.selectList(wrapper);
    }

    @Override
    public List<TradePairDO> search(String keyword) {
        LambdaQueryWrapperX<TradePairDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TradePairDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        wrapperX.likeIfPresent(TradePairDO::getName, keyword);
        return tradePairMapper.selectList(wrapperX);
    }

    @Override
    @Slave
    public List<TradePairDO> getListByIds(Set<Long> idSet, Integer status) {
        LambdaQueryWrapperX<TradePairDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.inIfPresent(TradePairDO::getId, idSet);
        queryWrapper.eqIfPresent(TradePairDO::getStatus, status);
        return tradePairMapper.selectList(queryWrapper);
    }

    @Override
    @Slave
    public List<TradePairDO> getListByCodes(Set<String> codeSet, Integer status) {
        LambdaQueryWrapperX<TradePairDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.inIfPresent(TradePairDO::getCode, codeSet);
        queryWrapper.eqIfPresent(TradePairDO::getStatus, status);
        return tradePairMapper.selectList(queryWrapper);
    }

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public Long createTradePair(TradePairSaveReqVO createReqVO) {
        // 校验交易对是否存在
        TradePairDO existsTradePairDO = getTradePairByCode(createReqVO.getCode(), null);
        if (existsTradePairDO != null) {
            throw exception(TRADE_PAIR_EXISTS);
        }
        // 插入
        TradePairDO tradePair = BeanUtils.toBean(createReqVO, TradePairDO.class);
        tradePair.setId(null);
        if (tradePair.getCandleStartTime() == null) {
            tradePair.setCandleStartTime(0L);
        }
        tradePairMapper.insert(tradePair);
        // 删除缓存
        tradePairRedisDAO.deleteAll();
        // 创建交易对的表 code使用小写保证数据库中表名的命名一致性
        candleDataApi.createCandleTable(createReqVO.getCode().toLowerCase());

        // 如果是复制币则将复制币存储在redis中，方便快速检索复制币信息
        if (tradePair.getIsCopy()) {
            tradePairRedisDAO.saveCopyTradeOf(createReqVO.getReferenceCode(), tradePair);
        }
        return tradePair.getId();
    }

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public void updateTradePair(TradePairSaveReqVO updateReqVO) {
        // 校验存在
        final TradePairDO existsTradePair = validateTradePairExists(updateReqVO.getId());
        // 更新
        TradePairDO updateObj = BeanUtils.toBean(updateReqVO, TradePairDO.class);
        updateObj.setCandleStartTime(null);
        tradePairMapper.updateById(updateObj);
        // 删除缓存
        tradePairRedisDAO.deleteAll();
        // 如果修改的交易对是复制币的话，则更新redis中的引用交易对的绑定关系
        if (existsTradePair.getIsCopy()) {
            final TradePairDO updatedObj = tradePairMapper.selectById(existsTradePair.getId());
            tradePairRedisDAO.saveCopyTradeOf(existsTradePair.getReferenceCode(), updatedObj);
        }
    }

    @Override
    @Master
    @DSTransactional
    public void updateTradePairStatus(UpdateStatusReqVO reqVO) {
        // 校验存在
        final TradePairDO existsTradePair = validateTradePairExists(reqVO.getId());
        // 更新状态
        TradePairDO updateDO = new TradePairDO();
        updateDO.setId(reqVO.getId());
        updateDO.setStatus(reqVO.getStatus());
        tradePairMapper.updateById(updateDO);
        // 删除缓存
        tradePairRedisDAO.deleteAll();
        // 如果修改的交易对是复制币的话，则更新redis中的引用交易对的绑定关系
        if (existsTradePair.getIsCopy()) {
            final TradePairDO updatedObj = tradePairMapper.selectById(existsTradePair.getId());
            tradePairRedisDAO.saveCopyTradeOf(existsTradePair.getReferenceCode(), updatedObj);
        }
    }

    @Override
    @Master
    @DSTransactional
    public void updateTradePairSyncStatus(UpdateStatusReqVO reqVO) {
        // 校验存在
        final TradePairDO existsTradePair = validateTradePairExists(reqVO.getId());
        // 如果是复制币和自发币则不需要更新同步状态开关
        if (existsTradePair.getIsCopy() || existsTradePair.getIsCustom()) {
            return;
        }
        // 更新状态
        TradePairDO updateDO = new TradePairDO();
        updateDO.setId(reqVO.getId());
        updateDO.setStatus(reqVO.getStatus());
        tradePairMapper.updateById(updateDO);
        // 删除缓存
        tradePairRedisDAO.deleteAll();
    }

    @Override
    @Master
    @DSTransactional(rollbackFor = Exception.class)
    public void deleteTradePair(Long id) {
        // 校验存在
        final TradePairDO tradePairDO = validateTradePairExists(id);
        tradePairMapper.updateToDelete(id, System.currentTimeMillis());
        // 删除缓存
        tradePairRedisDAO.deleteAll();
        // 删除复制币的引用交易对缓存
        tradePairRedisDAO.deleteReferenceCopy(tradePairDO.getReferenceCode(), tradePairDO.getCode());
    }

    @Override
    @Slave
    public TradePairDO getTradePair(Long id, Integer status) {
        LambdaQueryWrapperX<TradePairDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(TradePairDO::getId, id);
        queryWrapper.eqIfPresent(TradePairDO::getStatus, status);
        queryWrapper.last("limit 1");
        return tradePairMapper.selectOne(queryWrapper);
    }

    @Override
    @Slave
    public TradePairDO getTradePairByCode(String code, Integer status) {
        LambdaQueryWrapperX<TradePairDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(TradePairDO::getCode, code);
        queryWrapper.eqIfPresent(TradePairDO::getStatus, status);
        queryWrapper.last("limit 1");
        return tradePairMapper.selectOne(queryWrapper);
    }

    @Override
    @Slave
    public PageResult<TradePairDO> getTradePairPage(TradePairPageReqVO pageReqVO) {
        return tradePairMapper.selectPage(pageReqVO);
    }

    @Override
    public String getNameByCode(String code) {
        return tradePairMapper.getNameByCode(code);
    }

    @Override
    public List<TradePairDO> getCustomAndCopyTradePair() {
        LambdaQueryWrapperX<TradePairDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX
                .eq(TradePairDO::getIsCustom, true)
                .eq(TradePairDO::getIsCopy, true)
                .isNotNull(TradePairDO::getReferenceCode);
        return tradePairMapper.selectList(queryWrapperX);
    }
}