package com.rf.exchange.module.exc.controller.admin.tradepair.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 系统交易对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradePairSimpleRespVO {
    @Schema(description = "交易对ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long id;

    @Schema(description = "交易对名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("交易对名称")
    private String name;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易对代码")
    private String code;

}