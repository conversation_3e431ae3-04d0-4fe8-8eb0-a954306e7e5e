package com.rf.exchange.module.exc.dal.mysql.tradepair;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

/**
 * 系统交易对 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradePairMapper extends BaseMapperX<TradePairDO> {

    default PageResult<TradePairDO> selectPage(TradePairPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TradePairDO>()
                .likeIfPresent(TradePairDO::getName, reqVO.getName())
                .eqIfPresent(TradePairDO::getCode, reqVO.getCode())
                .eqIfPresent(TradePairDO::getTradeType, reqVO.getTradeType())
                .eqIfPresent(TradePairDO::getAssetType, reqVO.getAssetType())
                .eqIfPresent(TradePairDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TradePairDO::getSource, reqVO.getSource())
                .betweenIfPresent(TradePairDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TradePairDO::getId));
    }

    default TradePairDO selectByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<TradePairDO>().eqIfPresent(TradePairDO::getCode, code));
    }

    @Update("UPDATE exchange_trade_pair SET deleted = 1, deleted_time = #{deletedTime} WHERE id = #{id}")
    void updateToDelete(@Param("id") Long id, @Param("deletedTime") Long deletedTime);

    @Select("select name from exchange_trade_pair where code=#{code}")
    String getNameByCode(@Param("code") String code);


    @Select("select code from exchange_trade_pair where id=#{id}")
    String getCodeById(@Param("id") Long id);
}