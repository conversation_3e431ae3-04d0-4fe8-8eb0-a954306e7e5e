package com.rf.exchange.module.exc.convert;

import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-22
 */
public abstract class TradePairConvertDecorator implements TradePairConvert {
    private final TradePairConvert delegate;

    public TradePairConvertDecorator(TradePairConvert delegate) {
        this.delegate = delegate;
    }

    @Override
    public TradePairRespVO convert(TradePairDO tradePairDO) {
        if (tradePairDO == null) {
            return null;
        }
        TradePairRespVO respVO = delegate.convert(tradePairDO);
        respVO.setMarkPrice(DecimalFormatUtil.formatWithScale(tradePairDO.getMarkPrice(), tradePairDO.getScale()));
        return respVO;
    }

    @Override
    public TradePairRespDTO convert2(TradePairDO tradePairDO) {
        if (tradePairDO == null) {
            return null;
        }
        TradePairRespDTO dto = delegate.convert2(tradePairDO);
        if (tradePairDO.getMarkPrice() != null) {
            dto.setMarkPrice(DecimalFormatUtil.formatWithScale(tradePairDO.getMarkPrice(), tradePairDO.getScale()));
        }
        if (tradePairDO.getPercentage() != null) {
            dto.setPercentage(NumberUtils.formatPercent(tradePairDO.getPercentage().doubleValue(), 2));
        }
        return dto;
    }

    @Override
    public List<TradePairRespVO> convertList(List<TradePairDO> tradePairDOList) {
        return delegate.convertList(tradePairDOList);
    }

    @Override
    public List<TradePairRespDTO> convertList2(List<TradePairDO> tradePairDOList) {
        return delegate.convertList2(tradePairDOList);
    }
}
