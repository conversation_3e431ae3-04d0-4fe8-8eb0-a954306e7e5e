package com.rf.exchange.module.exc.dal.dataobject.tradepairtenant;

import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import com.rf.exchange.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.io.Serial;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

/**
 * 租户交易对 DO
 *
 * <AUTHOR>
 */
@TableName("exchange_trade_pair_tenant")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradePairTenantDO extends BaseNoDeleteDO {
    @Serial
    private final static long serialVersionUID = 1L;
    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 多租户编号
     */
    private Long tenantId;
    /**
     * 交易对编号
     */
    private Long tradePairId;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 租户自定义的排序
     */
    private Integer sort;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;
    /**
     * 熱門
     */
    private Boolean hot;
    /**
     * 是否默认交易对
     */
    private Boolean isDefault;
}