package com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo;

import com.rf.exchange.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Deprecated
@Schema(description = "管理后台 - 控盘分页")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradePairControlPageReqVO extends PageParam {

    @Schema(description = "交易对ID")
    private Long tradePairId;

    @Schema(description = "交易对代码")
    private String tradePair;

    @Schema(description = "租户id")
    private Long tenantId;
}