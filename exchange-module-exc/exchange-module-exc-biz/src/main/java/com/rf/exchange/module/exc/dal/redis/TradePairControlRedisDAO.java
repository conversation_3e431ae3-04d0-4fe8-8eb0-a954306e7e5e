package com.rf.exchange.module.exc.dal.redis;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;

@Deprecated
@Repository
public class TradePairControlRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void setControl(TradePairControlDO tradePairControlDO) {
        String redisKey = formatKey(tradePairControlDO.getTradePairCode());
        String jsonString = JsonUtils.toJsonString(tradePairControlDO);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString, 2, TimeUnit.DAYS);
    }

    public TradePairControlDO getTradePairControl(String code) {
        String redisKey = formatKey(code);
        String json = stringRedisTemplate.opsForValue().get(redisKey);
        if (json == null) {
            return null;
        }
        return JsonUtils.parseObject(stringRedisTemplate.opsForValue().get(redisKey), TradePairControlDO.class);
    }

    private String formatKey(String code) {
        return RedisKeyConstants.CUSTOM_TRADE_PAIR_CONTROL + code;
    }


}
