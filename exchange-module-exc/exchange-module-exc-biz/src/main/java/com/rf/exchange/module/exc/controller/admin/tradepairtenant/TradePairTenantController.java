package com.rf.exchange.module.exc.controller.admin.tradepairtenant;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairSimpleRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.service.tradepairtenant.TradePairTenantService;
import com.rf.exchange.module.system.api.permission.RoleApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

@Tag(name = "管理后台 - 租户交易对")
@RestController
@RequestMapping("/exchange/trade-pair-tenant")
@Validated
public class TradePairTenantController {

    @Resource
    private TradePairTenantService tradePairTenantService;
    @Resource
    private RoleApi roleApi;

    @PostMapping("/create")
    @Operation(summary = "创建租户交易对")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:create')")
    public CommonResult<Long> createTradePairTenant(@Valid @RequestBody TradePairTenantSaveReqVO createReqVO) {
        TenantContextHolder.setTenantId(createReqVO.getTenantId());
        if (roleApi.hasAnySuperAdminOf(Objects.requireNonNull(getLoginUser()).getRoleIds())) {
            TenantContextHolder.setIgnore(true);
        }
        return success(tradePairTenantService.createTradePairTenant(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户交易对")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:update')")
    public CommonResult<Boolean> update(@Valid @RequestBody TradePairTenantSaveReqVO updateReqVO) {
        TenantContextHolder.setTenantId(updateReqVO.getTenantId());
        if (roleApi.hasAnySuperAdminOf(Objects.requireNonNull(getLoginUser()).getRoleIds())) {
            TenantContextHolder.setIgnore(true);
        }
        tradePairTenantService.updateTradePairTenant(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户交易对")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:delete')")
    public CommonResult<Boolean> deleteTradePairTenant(@RequestParam("id") Long id) {
        tradePairTenantService.deleteTradePairTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户交易对")
    @Parameter(name = "tradeId", description = "编号", required = true, example = "1024")
    @Parameter(name = "tenantId", description = "租户id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:query')")
    public CommonResult<TradePairRespVO> getTradePairTenant(@RequestParam("tradeId") Long tradeId, @RequestParam("tenantId") Long tenantId) {
        TenantContextHolder.setTenantId(tenantId);
        if (roleApi.hasAnySuperAdminOf(Objects.requireNonNull(getLoginUser()).getRoleIds())) {
            TenantContextHolder.setIgnore(true);
        }
        TradePairDO tradePairDo = tradePairTenantService.getTradePairByTenantId(tenantId, tradeId);
        TradePairRespVO tradePairRespVO = BeanUtils.toBean(tradePairDo, TradePairRespVO.class);
        return success(tradePairRespVO);
    }

    @PostMapping("/page")
    @Operation(summary = "获得租户交易对分页")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:query')")
    public CommonResult<PageResult<TradePairTenantRespVO>> getTradePairTenantPage(@Valid @RequestBody TradePairTenantPageReqVO pageReqVO) {
        TenantContextHolder.setTenantId(pageReqVO.getTenantId());
        if (roleApi.hasAnySuperAdminOf(Objects.requireNonNull(getLoginUser()).getRoleIds())) {
            TenantContextHolder.setIgnore(true);
        }
        return success(tradePairTenantService.getPage(pageReqVO));
    }

    @GetMapping("/list")
    @Operation(summary = "获得租户所有交易对列表")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:query')")
    public CommonResult<List<TradePairTenantRespVO>> getTradePairTenantPage() {
        return success(tradePairTenantService.getList(TenantContextHolder.getTenantId()));
    }

    @GetMapping("/copy-list")
    @Operation(summary = "获取租户的复制币列表")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-tenant:query')")
    public CommonResult<List<TradePairTenantRespVO>> getTradePairCopiedTenantPage() {
        return success(tradePairTenantService.getCopiedList(TenantContextHolder.getTenantId()));
    }

    @GetMapping(value = "/list-all-simple")
    @Operation(summary = "获取租户交易对全列表", description = "只包含名称和编码的所有列表")
    public CommonResult<List<TradePairSimpleRespVO>> getSimpleTradePairTenantList() {
        if (roleApi.hasAnySuperAdminOf(Objects.requireNonNull(getLoginUser()).getRoleIds())) {
            TenantContextHolder.setIgnore(true);
        }
        List<TradePairDO> list = tradePairTenantService.getListCachedByTenantId(TenantContextHolder.getTenantId());
        return success(BeanUtils.toBean(list, TradePairSimpleRespVO.class));
    }

}