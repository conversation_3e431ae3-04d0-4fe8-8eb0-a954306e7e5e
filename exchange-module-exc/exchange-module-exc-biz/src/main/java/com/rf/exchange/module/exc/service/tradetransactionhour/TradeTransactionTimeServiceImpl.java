package com.rf.exchange.module.exc.service.tradetransactionhour;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;
import com.rf.exchange.module.exc.dal.mysql.tradetransactionhour.TradeTransactionTimeMapper;
import com.rf.exchange.module.exc.dal.redis.TradeTransactionTimeRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-29
 */
@Service
public class TradeTransactionTimeServiceImpl implements TradeTransactionTimeService {

    @Resource
    private TradeTransactionTimeMapper hourMapper;
    @Resource
    private TradeTransactionTimeRedisDAO redisDAO;

    @Override
    public void createTransactionTime(TradeTransactionTimeDO hourDO) {
        hourMapper.insert(hourDO);
        redisDAO.deleteAll();
    }

    @Override
    public void updateTransactionTime(TradeTransactionTimeDO hourDO) {
        hourMapper.updateById(hourDO);
        redisDAO.deleteAll();
    }

    @Override
    public TradeTransactionTimeDO getTransactionTime(Long id) {
        final TradeTransactionTimeDO hourDO = redisDAO.get(id);
        if (null != hourDO) {
            return hourDO;
        }
        getTransactionTimes();
        return hourMapper.selectById(id);
    }

    @Override
    public List<TradeTransactionTimeDO> getTransactionTimes() {
        final List<TradeTransactionTimeDO> hours = redisDAO.getAll();
        if (CollUtil.isNotEmpty(hours)) {
            return hours;
        }
        LambdaQueryWrapperX<TradeTransactionTimeDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.orderByDesc(TradeTransactionTimeDO::getId);
        final List<TradeTransactionTimeDO> list = hourMapper.selectList(wrapper);
        redisDAO.updateAll(list);
        return list;
    }

    @Override
    public void updateTransactionTimeCache() {
        redisDAO.deleteAll();
        getTransactionTimes();
    }
}
