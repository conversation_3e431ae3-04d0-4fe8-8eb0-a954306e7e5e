package com.rf.exchange.module.exc.service.tradepairtenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.number.DecimalFormatUtil;
import com.rf.exchange.framework.common.util.number.NumberUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantSaveReqVO;
import com.rf.exchange.module.exc.controller.app.tradepair.vo.AppTradePairSearchReqVO;
import com.rf.exchange.module.exc.convert.TradePairConvert;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.dataobject.tradepairtenant.TradePairTenantDO;
import com.rf.exchange.module.exc.dal.mysql.tradepairtenant.TradePairTenantMapper;
import com.rf.exchange.module.exc.dal.redis.RedisKeyConstants;
import com.rf.exchange.module.exc.dal.redis.TradePairTenantRedisDAO;
import com.rf.exchange.module.exc.service.tradepair.TradePairService;
import com.rf.exchange.module.system.api.tenant.TenantApi;
import com.rf.exchange.module.system.api.tenant.dto.TenantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.*;

/**
 * 租户交易对 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradePairTenantServiceImpl implements TradePairTenantService {

    @Resource
    private TradePairService tradePairService;
    @Resource
    private TradePairTenantMapper tradePairTenantMapper;
    @Resource
    private TradePairTenantRedisDAO tradePairTenantRedisDAO;
    @Resource
    private CandleDataApi candleDataApi;
    @Resource
    private TenantApi tenantApi;

    @Override
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        if (sourceTenantId == null || targetTenantId == null) {
            throw exception(GlobalErrorCodeConstants.VALUE_ERROR);
        }
        List<TradePairDO> tradePairDOList = tradePairService.getAll();
        Set<Long> tradePairIdSet = tradePairDOList.stream().filter(t -> t.getIsCustom() == false).map(TradePairDO::getId).collect(Collectors.toSet());
        TenantContextHolder.setIgnore(true);
        LambdaQueryWrapperX<TradePairTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(TradePairTenantDO::getTenantId, sourceTenantId);
        wrapper.eq(TradePairTenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        wrapper.in(TradePairTenantDO::getTradePairId, tradePairIdSet);

        List<TradePairTenantDO> tradePairTenantDOList = tradePairTenantMapper.selectList(wrapper);
        for (TradePairTenantDO tradePairTenantDO : tradePairTenantDOList) {
            tradePairTenantDO.setId(null);
            tradePairTenantDO.setTenantId(targetTenantId);
        }
        tradePairTenantMapper.insertBatch(tradePairTenantDOList);

        TenantContextHolder.clearIgnore();
        // 删除redis缓存
        tradePairTenantRedisDAO.delete(targetTenantId);
    }

    @Override
    public TradePairDO getDefaultTradePair(Long tenantId) {
        List<TradePairDO> listCachedByTenantId = getListCachedByTenantId(tenantId);
        if (CollectionUtil.isEmpty(listCachedByTenantId)) {
            return null;
        }
        return listCachedByTenantId.stream().filter(TradePairDO::getIsDefault).findFirst().orElse(null);
    }

    @Override
    @Slave
    public TradePairDO validateTradePairExists(Long tenantId, Long tradePairId) {
        TradePairDO tradePairDo = getTradePairByTenantId(tenantId, tradePairId);
        if (tradePairDo == null) {
            throw exception(TRADE_PAIR_TENANT_NOT_EXISTS);
        }
        return tradePairDo;
    }

    @Override
    @Slave
    public TradePairDO validateTradePairExists(Long tenantId, String tradePairCode) {
        TradePairDO tradePairDo = getTradePairByTenantId(tenantId, tradePairCode);
        if (tradePairDo == null) {
            throw exception(TRADE_PAIR_TENANT_NOT_EXISTS);
        }
        return tradePairDo;
    }

    @Override
    @Master
    @DSTransactional
    public Long createTradePairTenant(TradePairTenantSaveReqVO createReqVO) {
        // 校验交易对是否存在
        TradePairDO tradePairDO = tradePairService.validateTradePairExists(createReqVO.getTradePairId());
        // 校验租户交易对是否存在
        TradePairDO tradePairByTenantId = getTradePairByTenantId(createReqVO.getTenantId(), createReqVO.getTradePairId());
        if (tradePairByTenantId != null) {
            throw exception(TRADE_PAIR_TENANT_EXISTS);
        }
        // 插入
        TradePairTenantDO newTradePairTenant = BeanUtils.toBean(createReqVO, TradePairTenantDO.class);
        newTradePairTenant.setId(null);
        // 如果租户没有默认交易对,则创建租户的交易对时先指定一个
        final TradePairTenantDO defaultTradePair = tradePairTenantMapper.selectDefaultTradePair(createReqVO.getTenantId());
        if (defaultTradePair == null) {
            newTradePairTenant.setIsDefault(true);
        } else if (newTradePairTenant.getIsDefault()) {
            throw exception(TRADE_TENANT_DUPLICATE_DEFAULT);
        }
        newTradePairTenant.setTradePairCode(tradePairDO.getCode());
        tradePairTenantMapper.insert(newTradePairTenant);
        // 清除请求租户的交易对id缓存
        tradePairTenantRedisDAO.delete(createReqVO.getTenantId());
        return newTradePairTenant.getId();
    }

    @Override
    @Master
    @DSTransactional
    public void updateTradePairTenant(TradePairTenantSaveReqVO updateReqVO) {
        // 校验存在
        TradePairTenantDO existsDO = validateTradePairTenantExists(updateReqVO.getId());
        // 更新
        TradePairTenantDO updateObj = BeanUtils.toBean(updateReqVO, TradePairTenantDO.class);
        // 如果更新信息中的是否为默认交易对的值改变了则需要判断，确保租户的默认交易对始终存在
        if (!existsDO.getIsDefault().equals(updateObj.getIsDefault())) {
            // 如果当前交易对需要不设置为默认交易对则抛出异常
            if (!updateObj.getIsDefault()) {
                throw exception(TRADE_TENANT_NEED_DEFAULT);
            } else {
                // 将之前设置的默认的租户交易对设置为非默认交易对
                TradePairTenantDO prevDefault = tradePairTenantMapper.selectDefaultTradePair(updateReqVO.getTenantId());
                prevDefault.setIsDefault(false);
                tradePairTenantMapper.updateById(prevDefault);
            }
        }
        tradePairTenantMapper.updateById(updateObj);
        // 清除请求租户的交易对id缓存
        tradePairTenantRedisDAO.delete(updateReqVO.getTenantId());
    }

    @Override
    @Master
    @DSTransactional
    public void deleteTradePairTenant(Long id) {
        // 校验存在
        TradePairTenantDO tradePairTenantDO = validateTradePairTenantExists(id);
        // 删除缓存和数据库中的记录
        tradePairTenantMapper.deleteById(tradePairTenantDO.getId());
        // 清除租户交易对id的缓存
        tradePairTenantRedisDAO.delete(tradePairTenantDO.getTenantId());
    }

    @Override
    @Master
    @DSTransactional
    public void deleteTradePairTenantByTenantId(Long tenantId) {
        LambdaUpdateWrapper<TradePairTenantDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(TradePairTenantDO::getTenantId, tenantId);
        // 删除缓存和数据库中的记录
        tradePairTenantMapper.delete(wrapper);
        // 清除租户交易对id的缓存
        tradePairTenantRedisDAO.delete(tenantId);
    }

    private TradePairTenantDO validateTradePairTenantExists(Long id) {
        TradePairTenantDO DO = tradePairTenantMapper.selectById(id);
        if (DO == null) {
            throw exception(TRADE_PAIR_TENANT_NOT_EXISTS);
        }
        return DO;
    }

    @Override
    @Slave
    public TradePairTenantDO getTradePairTenant(Long id) {
        return tradePairTenantMapper.selectById(id);
    }

    @Override
    @Slave
    public List<TradePairTenantDO> getTradePairTenantCachedList(Long tenantId) {
        List<TradePairTenantDO> list = tradePairTenantRedisDAO.get(tenantId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list;
        }
        // 最大获取500条交易对，如果超过500条需要换成分页接口
        list = tradePairTenantMapper.selectTradePairByTenant(tenantId, 500);
        tradePairTenantRedisDAO.set(list, tenantId);
        return list;
    }

    @Override
    @Slave
    public TradePairDO getTradePairByTenantId(Long tenantId, Long tradePId) {
        List<TradePairTenantDO> cachedList = getTradePairTenantCachedList(tenantId);
        Optional<TradePairTenantDO> first = cachedList.stream().filter(tradePairTenantDO -> tradePairTenantDO.getTradePairId().equals(tradePId)).findFirst();
        if (first.isPresent()) {
            final TradePairTenantDO tradePairTenantDO = first.get();
            TradePairDO tradePairDO = tradePairService.getTradePair(tradePId, CommonStatusEnum.ENABLE.getStatus());
            tradePairDO.setHot(tradePairTenantDO.getHot());
            tradePairDO.setIsDefault(tradePairTenantDO.getIsDefault());
            tradePairDO.setSort(tradePairTenantDO.getSort());
            return tradePairDO;
        }
        TradePairTenantDO tradePairTenantDO = tradePairTenantMapper.selectByTradePairId(tenantId, tradePId);
        if (tradePairTenantDO != null) {
            TradePairDO tradePairDO = tradePairService.getTradePair(tradePairTenantDO.getTradePairId(), CommonStatusEnum.ENABLE.getStatus());
            tradePairDO.setHot(tradePairTenantDO.getHot());
            tradePairDO.setIsDefault(tradePairTenantDO.getIsDefault());
            tradePairDO.setSort(tradePairTenantDO.getSort());
            return tradePairDO;
        }
        return null;
    }

    @Override
    @Slave
    public TradePairDO getTradePairByTenantId(Long tenantId, String tradePairCode) {
        List<TradePairDO> tradePairDOList = tradePairService.getListCached();
        if (CollectionUtil.isEmpty(tradePairDOList)) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        Optional<TradePairDO> tradePairDo = tradePairDOList.stream().filter(tradePairDO -> tradePairCode.equals(tradePairDO.getCode())).findFirst();
        if (tradePairDo.isEmpty()) {
            throw exception(TRADE_PAIR_NOT_EXISTS);
        }
        long tradePairId = tradePairDo.get().getId();
        return getTradePairByTenantId(tenantId, tradePairId);
    }

    @Override
    @Slave
    public List<TradePairDO> getListCachedByTenantId(Long tenantId) {
        return getListCachedByTenantTradeCodes(tenantId, null);
    }

    @Override
    @Slave
    public List<TradePairDO> getListCachedByTenantTradeCodes(Long tenantId, Set<String> tradeCodeSet) {
        // 获取缓存的租户交易对id列表 从列表中过滤出在tradeIdSet的交易对id
        List<TradePairTenantDO> cachedList = getTradePairTenantCachedList(tenantId);
        Set<String> currentTradeCodeSet = cachedList.stream().map(TradePairTenantDO::getTradePairCode).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(tradeCodeSet)) {
            currentTradeCodeSet = currentTradeCodeSet.stream().filter(tradeCodeSet::contains).collect(Collectors.toSet());
        }
        // 如果当前租户的交易对没有开启任何交易对则返回空
        if (CollectionUtil.isNotEmpty(currentTradeCodeSet)) {
            // 从系统所有启用的交易对列表中获取符合idSet的交易对, 注意不能查询系统已经关闭的交易对
            List<TradePairDO> tradePairList = tradePairService.getListByCodes(currentTradeCodeSet, CommonStatusEnum.ENABLE.getStatus());
            Map<Long, Boolean> hotMap = cachedList.stream().collect(Collectors.toMap(TradePairTenantDO::getTradePairId, TradePairTenantDO::getHot));
            Map<Long, Boolean> defaultMap = cachedList.stream().collect(Collectors.toMap(TradePairTenantDO::getTradePairId, TradePairTenantDO::getIsDefault));
            tradePairList.forEach(item -> {
                if (hotMap.containsKey(item.getId())) {
                    item.setHot(hotMap.get(item.getId()));
                }
                if (defaultMap.containsKey(item.getId())) {
                    item.setIsDefault(defaultMap.get(item.getId()));
                }
            });
            // 对交易对进行排序
            Map<Long, Integer> idSortMap = cachedList.stream().collect(Collectors.toMap(TradePairTenantDO::getTradePairId, TradePairTenantDO::getSort));
            tradePairList.sort(Comparator.comparingInt(tradePairDO -> idSortMap.getOrDefault(tradePairDO.getId(), Integer.MIN_VALUE)));
            return tradePairList;
        }
        return Collections.emptyList();
    }

    @Override
    @CachePut(cacheNames = RedisKeyConstants.TRADE_PAIR_TENANT, key = "#tenantId", unless = "#result == null || #result.isEmpty()")
    public List<TradePairRespVO> getHotTradePair(Long tenantId) {
        // 取出缓存的租户交易对列表
        List<TradePairDO> tradePairList = getListCachedByTenantId(tenantId);
        if (CollectionUtil.isEmpty(tradePairList)) {
            return List.of();
        }
        Set<String> hotCodeSet = tradePairList.stream().filter(TradePairDO::getHot).map(TradePairDO::getCode).collect(Collectors.toSet());

        // 从redis中获取交易对的实时价格
        Map<String, CurrentPriceRespDTO> currentPriceMap = candleDataApi.getCurrentPriceListByIdSet(hotCodeSet);
        // 从redis中获取今日k线
        Map<String, TodayKlinePriceDTO> todayKlineMap = candleDataApi.getTodayKlinePriceListByIdSet(hotCodeSet);

        List<TradePairRespVO> respVOList = new ArrayList<>();
        for (TradePairDO tradePairDO : tradePairList) {
            if (!hotCodeSet.contains(tradePairDO.getCode())) {
                continue;
            }
            TradePairRespVO respVO = TradePairConvert.INSTANCE.convert(tradePairDO);
            if (currentPriceMap.containsKey(tradePairDO.getCode())) {

                CurrentPriceRespDTO priceDto = currentPriceMap.get(tradePairDO.getCode());
                TodayKlinePriceDTO todayKline = todayKlineMap.get(tradePairDO.getCode());

                respVO.setMarkPrice(DecimalFormatUtil.formatWithScale(tradePairDO.getMarkPrice(), tradePairDO.getScale()));
                respVO.setCurrentPrice(DecimalFormatUtil.formatWithScale(priceDto.getCurrentPrice(), tradePairDO.getScale()));
                respVO.setPercentage(NumberUtils.formatPercent(tradePairDO.getPercentage().doubleValue(), 2));
                respVO.setVolume(todayKline.getVolume24H());
            }
            respVOList.add(respVO);
        }
        return respVOList;
    }

    @Override
    public List<TradePairRespVO> getListCachedByType(Long tenantId, Integer tradeType, Integer assetType) {
        // 取出缓存的租户交易对列表
        List<TradePairDO> tradePairList = getListCachedByTenantId(tenantId);
        if (CollectionUtil.isEmpty(tradePairList)) {
            return List.of();
        }
        tradePairList = tradePairList.stream()
                .filter(tradePairDO -> (assetType == null || Objects.equals(tradePairDO.getAssetType(), assetType)) && (tradeType == null || Objects.equals(tradePairDO.getTradeType(), tradeType)))
                .collect(Collectors.toList());
        Set<String> codeSet = tradePairList.stream().map(TradePairDO::getCode).collect(Collectors.toSet());
        // 从redis中获取交易对的实时价格
        Map<String, CurrentPriceRespDTO> currentPriceMap = candleDataApi.getCurrentPriceListByIdSet(codeSet);
        // 从redis中获取今日k线
        Map<String, TodayKlinePriceDTO> todayKlineMap = candleDataApi.getTodayKlinePriceListByIdSet(codeSet);

        List<TradePairRespVO> respVOList = new ArrayList<>();
        tradePairList.forEach(tradePairDO -> {
            TradePairRespVO respVO = TradePairConvert.INSTANCE.convert(tradePairDO);
            if (currentPriceMap.containsKey(tradePairDO.getCode())) {

                CurrentPriceRespDTO priceDto = currentPriceMap.get(tradePairDO.getCode());
                TodayKlinePriceDTO todayKline = todayKlineMap.get(tradePairDO.getCode());

                respVO.setMarkPrice(DecimalFormatUtil.formatWithScale(tradePairDO.getMarkPrice(), tradePairDO.getScale()));
                respVO.setCurrentPrice(DecimalFormatUtil.formatWithScale(priceDto.getCurrentPrice(), tradePairDO.getScale()));
                respVO.setPercentage(NumberUtils.formatPercent(priceDto.getPercentage().doubleValue(), 2));
                respVO.setScale(tradePairDO.getScale());
                if (todayKline != null) {
                    respVO.setVolume(todayKline.getVolume24H());
                }
            }
            respVOList.add(respVO);
        });
        return respVOList;
    }

    @Override
    @Slave
    public PageResult<TradePairTenantRespVO> getPage(TradePairTenantPageReqVO pageReqVO) {
        // 使用 TradePairTenantPageReqVO 中的分页参数构造 Page 对象
        Page<Map<String, Object>> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 调用 Mapper 方法进行分页查询
        IPage<Map<String, Object>> mapPage = tradePairTenantMapper.selectTradePairTenantPage(page, pageReqVO);
        // 将查询结果转换为目标对象列表
        List<TradePairTenantRespVO> records = mapPage.getRecords().stream().map(this::mapToTradePairTenantRespVO).collect(Collectors.toList());
        // 租户基础信息
        List<TenantRespDTO> tenantList = tenantApi.getTenantList();
        Map<Long, TenantRespDTO> tenantMap = tenantList.stream().collect(Collectors.toMap(TenantRespDTO::getId, tenantRespDTO -> tenantRespDTO));
        for (TradePairTenantRespVO record : records) {
            if (tenantMap.containsKey(record.getTenantId())) {
                record.setTenantName(tenantMap.get(record.getTenantId()).getName());
            }
        }
        // 构造返回的分页结果对象
        IPage<TradePairTenantRespVO> resultPage = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        resultPage.setRecords(records);
        resultPage.setCurrent(mapPage.getCurrent());
        resultPage.setSize(mapPage.getSize());
        resultPage.setTotal(mapPage.getTotal());
        return new PageResult<>(resultPage);
    }

    @Override
    public List<TradePairTenantRespVO> getList(Long tenantId) {
        //只找自发币的id
        List<TradePairDO> customTradePairList = tradePairService.getCustomAndCopyTradePair();
        Set<Long> customTradePairIdSet = customTradePairList.stream().filter(TradePairDO::getIsCustom).map(TradePairDO::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(customTradePairIdSet)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapperX<TradePairTenantDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(TradePairTenantDO::getTradePairId, customTradePairIdSet);
        queryWrapperX.eq(TradePairTenantDO::getTenantId, tenantId).eq(TradePairTenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        List<TradePairTenantDO> list = tradePairTenantMapper.selectList(queryWrapperX);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<Long> tradePairIds = list.stream().map(TradePairTenantDO::getTradePairId).collect(Collectors.toSet());
        List<TradePairDO> tradePairDOList = tradePairService.getListByIds(tradePairIds, CommonStatusEnum.ENABLE.getStatus());
        Map<Long, TradePairDO> tradePairMap = tradePairDOList.stream().collect(Collectors.toMap(TradePairDO::getId, Function.identity()));
        List<TradePairTenantRespVO> result = BeanUtils.toBean(list, TradePairTenantRespVO.class);
        result.forEach(c -> {
            if (tradePairMap.containsKey(c.getTradePairId())) {
                TradePairDO tradePairDO = tradePairMap.get(c.getTradePairId());
                TradePairTenantRespVO.TradePairInfo tradePairInfo = BeanUtils.toBean(tradePairDO, TradePairTenantRespVO.TradePairInfo.class);
                c.setTradePairInfo(tradePairInfo);
            }
        });
        return result;
    }

    @Override
    public List<TradePairTenantRespVO> getCopiedList(Long tenantId) {
        List<TradePairDO> customTradePairList = tradePairService.getCustomAndCopyTradePair();
        Set<Long> customTradePairIdSet = customTradePairList.stream().filter(TradePairDO::getIsCopy).map(TradePairDO::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(customTradePairIdSet)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapperX<TradePairTenantDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.in(TradePairTenantDO::getTradePairId, customTradePairIdSet);
        queryWrapperX.eq(TradePairTenantDO::getTenantId, tenantId).eq(TradePairTenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        List<TradePairTenantDO> list = tradePairTenantMapper.selectList(queryWrapperX);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<Long> tradePairIds = list.stream().map(TradePairTenantDO::getTradePairId).collect(Collectors.toSet());
        List<TradePairDO> tradePairDOList = tradePairService.getListByIds(tradePairIds, CommonStatusEnum.ENABLE.getStatus());
        Map<Long, TradePairDO> tradePairMap = tradePairDOList.stream().collect(Collectors.toMap(TradePairDO::getId, Function.identity()));
        List<TradePairTenantRespVO> result = BeanUtils.toBean(list, TradePairTenantRespVO.class);
        result.forEach(c -> {
            if (tradePairMap.containsKey(c.getTradePairId())) {
                TradePairDO tradePairDO = tradePairMap.get(c.getTradePairId());
                TradePairTenantRespVO.TradePairInfo tradePairInfo = BeanUtils.toBean(tradePairDO, TradePairTenantRespVO.TradePairInfo.class);
                c.setTradePairInfo(tradePairInfo);
            }
        });
        return result;
    }

    @Override
    public List<TradePairRespVO> appSearchTradePair(AppTradePairSearchReqVO reqVO, Long tenantId) {
        List<TradePairDO> tradePairList = tradePairService.search(reqVO.getKeyword());
        Set<String> codeSet = tradePairList.stream().map(TradePairDO::getCode).collect(Collectors.toSet());
        Map<String, CurrentPriceRespDTO> priceMap = candleDataApi.getCurrentPriceListByIdSet(codeSet);
        Map<String, TodayKlinePriceDTO> todayKlineMap = candleDataApi.getTodayKlinePriceListByIdSet(codeSet);

        // 获取缓存的租户交易对id列表
        List<TradePairTenantDO> tenantTradePairList = getTradePairTenantCachedList(tenantId);
        Set<Long> tenantTradePairIdSet = tenantTradePairList.stream().map(TradePairTenantDO::getTradePairId).collect(Collectors.toSet());

        List<TradePairRespVO> respVOList = new ArrayList<>();
        for (TradePairDO tradePairDO : tradePairList) {
            if (tenantTradePairIdSet.contains(tradePairDO.getId())) {
                TradePairRespVO tradePair = TradePairConvert.INSTANCE.convert(tradePairDO);
                CurrentPriceRespDTO currentPriceDTO = priceMap.get(tradePairDO.getCode());
                if (currentPriceDTO != null) {
                    tradePair.setCurrentPrice(DecimalFormatUtil.formatWithScale(currentPriceDTO.getCurrentPrice(), tradePairDO.getScale()));
                    tradePair.setVolume(DecimalFormatUtil.formatPlain(currentPriceDTO.getVolume()));
                    tradePair.setPercentage(NumberUtils.formatPercent(currentPriceDTO.getPercentage().doubleValue(), 2));
                }
                respVOList.add(tradePair);
            }
        }
        return respVOList;
    }

    private TradePairTenantRespVO mapToTradePairTenantRespVO(Map<String, Object> map) {
        TradePairTenantRespVO respVO = new TradePairTenantRespVO();
        long id = ((BigInteger) map.get("id")).longValue();
        long tradePairId = ((BigInteger) map.get("tradePairId")).longValue();
        long tenantId = (Long) map.get("tenantId");
        String tenantName = ((String) map.get("tenantName"));

        respVO.setId(id);
        respVO.setTenantId(tenantId);
        respVO.setTenantName(tenantName);
        respVO.setTradePairId(tradePairId);
        respVO.setSort((Integer) map.get("sort"));
        respVO.setStatus((Integer) map.get("status"));
        respVO.setCreator((String) map.get("creator"));
        respVO.setUpdater((String) map.get("updater"));
        respVO.setCreateTime((Long) map.get("createTime"));
        respVO.setUpdateTime((Long) map.get("createTime"));

        TradePairTenantRespVO.TradePairInfo tradePairInfo = new TradePairTenantRespVO.TradePairInfo();
        tradePairInfo.setId(tradePairId);
        tradePairInfo.setName((String) map.get("name"));
        tradePairInfo.setCode((String) map.get("code"));
        tradePairInfo.setBaseAsset((String) map.get("baseAsset"));
        tradePairInfo.setQuoteAsset((String) map.get("quoteAsset"));
        tradePairInfo.setTradeType((Integer) map.get("tradeType"));

        tradePairInfo.setAssetType((Integer) map.get("assetType"));
        tradePairInfo.setStatus((Integer) map.get("tradePairStatus"));
        tradePairInfo.setSource((Integer) map.get("source"));
        tradePairInfo.setScale((Integer) map.get("scale"));
        tradePairInfo.setHot((Boolean) map.get("hot"));
        tradePairInfo.setIsDefault((Boolean) map.get("isDefault"));

        BigDecimal markPrice = (BigDecimal) map.get("markPrice");
        BigDecimal changePercentage = (BigDecimal) map.get("percentage");
        tradePairInfo.setMarkPrice(DecimalFormatUtil.formatWithScale(markPrice, tradePairInfo.getScale()));
        tradePairInfo.setPercentage(NumberUtils.formatPercent(changePercentage.doubleValue(), 2));

        respVO.setTradePairInfo(tradePairInfo);
        return respVO;
    }

}