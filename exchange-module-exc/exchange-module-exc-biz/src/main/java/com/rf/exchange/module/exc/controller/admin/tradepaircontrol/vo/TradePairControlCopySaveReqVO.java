package com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 系统交易对控制新增/修改 Request VO")
@Data
public class TradePairControlCopySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long id;

    @Schema(description = "交易对id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28")
    @NotNull(message = "交易对不能为空")
    private Long tradePairId;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long minuteTime;

    @Schema(description = "结束时间收盘价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal closePrice;

    @Schema(description = "最高价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal highPrice;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lowPrice;

}