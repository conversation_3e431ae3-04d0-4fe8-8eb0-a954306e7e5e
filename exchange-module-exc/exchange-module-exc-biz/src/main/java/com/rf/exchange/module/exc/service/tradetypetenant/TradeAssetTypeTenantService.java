package com.rf.exchange.module.exc.service.tradetypetenant;

import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import jakarta.validation.*;
import com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo.*;
import com.rf.exchange.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 租户交易对类型配置 Service 接口
 *
 * <AUTHOR>
 */
public interface TradeAssetTypeTenantService {

    /**
     * 拷贝默认租户的数据
     *
     * @param sourceTenantId 源租户id
     * @param targetTenantId 目标租户id
     */
    void copyDataFromTenant(Long sourceTenantId, Long targetTenantId);

    /**
     * 创建租户交易对类型配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTradeAssetTypeTenant(@Valid TradeAssetTypeTenantSaveReqVO createReqVO);

    /**
     * 更新租户交易对类型配置
     *
     * @param updateReqVO 更新信息
     */
    void updateTradeAssetTypeTenant(@Valid TradeAssetTypeTenantSaveReqVO updateReqVO);

    /**
     * 删除租户交易对类型配置
     *
     * @param id 编号
     */
    void deleteTradeAssetTypeTenant(Long id);

    /**
     * 获得租户交易对类型配置
     *
     * @param id 编号
     * @return 租户交易对类型配置
     */
    TradeAssetTypeTenantDO getTradeAssetTypeTenant(Long id);

    /**
     * 获得租户交易对类型配置分页
     *
     * @param pageReqVO 分页查询
     * @return 租户交易对类型配置分页
     */
    PageResult<TradeAssetTypeTenantDO> getTradeAssetTypeTenantPage(TradeAssetTypeTenantPageReqVO pageReqVO);

    /**
     * 获取租户的交易对类型配置
     *
     * @param tenantId 租户id
     * @return 交易对类型配置
     */
    List<TradeAssetTypeTenantDO> getTradeAssetTypeTenantList(Long tenantId);

}