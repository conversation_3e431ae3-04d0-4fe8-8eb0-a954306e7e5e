package com.rf.exchange.module.exc.service.tradetransactionhour;

import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;

import java.util.List;

/**
 * 交易对的交易时间
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
public interface TradeTransactionTimeService {

    /**
     * 创建交易时间
     * @param hourDO 交易时间
     */
    void createTransactionTime(TradeTransactionTimeDO hourDO);

    /**
     * 更新交易时间
     *
     * @param hourDO 交易时间
     */
    void updateTransactionTime(TradeTransactionTimeDO hourDO);

    /**
     * 获取交易时间
     *
     * @param id 交易时间编号
     * @return 交易时间信息
     */
    TradeTransactionTimeDO getTransactionTime(Long id);

    /**
     * 获取所有的交易时间
     *
     * @return 交易时间信息列表
     */
    List<TradeTransactionTimeDO> getTransactionTimes();

    /**
     * 更新交易时间的缓存
     */
    void updateTransactionTimeCache();
}
