package com.rf.exchange.module.exc.service.tradepairtenant;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo.TradePairTenantSaveReqVO;
import com.rf.exchange.module.exc.controller.app.tradepair.vo.AppTradePairSearchReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import com.rf.exchange.module.exc.dal.dataobject.tradepairtenant.TradePairTenantDO;

import java.util.List;
import java.util.Set;

/**
 * 租户交易对 Service 接口
 *
 * <AUTHOR>
 */
public interface TradePairTenantService {

    /**
     * 拷贝租户的交易对数据
     *
     * @param sourceTenantId 源租户id
     * @param targetTenantId 目标租户id
     */
    void copyDataFromTenant(Long sourceTenantId, Long targetTenantId);

    /**
     * 获取租户的默认交易对
     *
     * @return 交易对信息
     */
    TradePairDO getDefaultTradePair(Long tenantId);

    TradePairDO validateTradePairExists(Long tenantId, Long tradePairId);

    TradePairDO validateTradePairExists(Long tenantId, String tradePairCode);

    /**
     * 创建租户交易对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTradePairTenant(TradePairTenantSaveReqVO createReqVO);

    /**
     * 更新租户交易对
     *
     * @param updateReqVO 更新信息
     */
    void updateTradePairTenant(TradePairTenantSaveReqVO updateReqVO);

    /**
     * 删除指定编号的租户交易对
     *
     * @param id 编号
     */
    void deleteTradePairTenant(Long id);

    /**
     * 删除租户所有的交易对
     *
     * @param tenantId 租户id
     */
    void deleteTradePairTenantByTenantId(Long tenantId);

    /**
     * 获得租户交易对
     *
     * @param id 编号
     * @return 租户交易对信息
     */
    TradePairTenantDO getTradePairTenant(Long id);

    /**
     * 通过租户id获取租户缓存的交易映射关系
     *
     * @param tenantId 租户id
     * @return 租户交易对列表
     */
    List<TradePairTenantDO> getTradePairTenantCachedList(Long tenantId);

    /**
     * 获取单个租户的交易对
     *
     * @param tenantId      租户id
     * @param tradePairCode 交易对代码
     * @return 租户交易对信息
     */
    TradePairDO getTradePairByTenantId(Long tenantId, String tradePairCode);

    TradePairDO getTradePairByTenantId(Long tenantId, Long tradePairId);

    /**
     * 通过租户id获取运营的交易对列表
     *
     * @param tenantId 租户id
     * @return 租户交易对列表
     */
    List<TradePairDO> getListCachedByTenantId(Long tenantId);

    /**
     * 通过租户id和交易对id集合获取交易对列表
     *
     * @param tenantId       租户id
     * @param tradeIdCodeSet 交易对id集合
     * @return 租户交易对列表
     */
    List<TradePairDO> getListCachedByTenantTradeCodes(Long tenantId, Set<String> tradeIdCodeSet);

    /**
     * 获取热门交易对列表
     *
     * @param tenantId 租户编号
     * @return 租户热门交易对列表
     */
    List<TradePairRespVO> getHotTradePair(Long tenantId);

    /**
     * 获取指定交易类型或者指定资产类型的交易对列表
     *
     * @param tenantId  租户id
     * @param tradeType 交易类型
     * @param assetType 资产类型
     * @return 交易对列表
     */
    List<TradePairRespVO> getListCachedByType(Long tenantId, Integer tradeType, Integer assetType);

    /**
     * 获得租户交易对分页
     *
     * @param pageReqVO 分页查询
     * @return 租户交易对分页
     */
    PageResult<TradePairTenantRespVO> getPage(TradePairTenantPageReqVO pageReqVO);

    List<TradePairTenantRespVO> getList(Long tenantId);

    /**
     * 复制币的列表
     *
     * @param tenantId 租户id
     * @return 交易对列表
     */
    List<TradePairTenantRespVO> getCopiedList(Long tenantId);

    /**
     * 搜索租户交易对
     *
     * @return 交易对列表
     */
    List<TradePairRespVO> appSearchTradePair(AppTradePairSearchReqVO reqVO, Long tenantId);
}