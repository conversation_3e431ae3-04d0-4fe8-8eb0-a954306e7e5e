package com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 系统交易对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradePairControlRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1670")
    private Long id;

    @Schema(description = "交易对ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long tradePairId;

    @Schema(description = "交易对代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String tradePairCode;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "结束时间收盘价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal endPrice;

    @Schema(description = "状态，0未完成，1完成", requiredMode = Schema.RequiredMode.REQUIRED)
    private int status;

    @Schema(description = "每分钟成交额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal oneMinuteTurnover;

    @Schema(description = "最高价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal highPrice;

    @Schema(description = "最低价", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lowPrice;
}