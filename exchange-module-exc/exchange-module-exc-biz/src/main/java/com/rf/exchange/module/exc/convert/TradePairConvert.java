package com.rf.exchange.module.exc.convert;

import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairRespVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-20
 */
@Mapper
@DecoratedWith(TradePairConvertDecorator.class)
public interface TradePairConvert {

    TradePairConvert INSTANCE = Mappers.getMapper(TradePairConvert.class);

    TradePairRespVO convert(TradePairDO tradePairDO);

    TradePairRespDTO convert2(TradePairDO tradePairDO);

    List<TradePairRespVO> convertList(List<TradePairDO> tradePairDOList);

    List<TradePairRespDTO> convertList2(List<TradePairDO> tradePairDOList);
}
