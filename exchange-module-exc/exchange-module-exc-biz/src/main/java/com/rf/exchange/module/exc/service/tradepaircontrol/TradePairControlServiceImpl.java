package com.rf.exchange.module.exc.service.tradepaircontrol;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import com.rf.exchange.module.exc.dal.mysql.tradepair.TradePairMapper;
import com.rf.exchange.module.exc.dal.mysql.tradepaircontrol.TradePairControlMapper;
import com.rf.exchange.module.exc.dal.mysql.tradepairtenant.TradePairTenantMapper;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TradePairControlServiceImpl implements TradePairControlService {
    @Resource
    private TradePairControlMapper tradePairControlMapper;
    @Resource
    private TradePairMapper tradePairMapper;
    @Resource
    private TradePairTenantMapper tradePairTenantMapper;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;

    @Override
    public PageResult<TradePairControlDO> getList(TradePairControlPageReqVO pageReqVO) {
        return tradePairControlMapper.selectPage(pageReqVO);
    }

    @Override
    public TradePairControlDO get(Long id) {
        return tradePairControlMapper.selectById(id);
    }

    @Override
    public Long create(TradePairControlSaveReqVO createReqVO) {
        // 插入
        TradePairControlDO tradePairControlDO = BeanUtils.toBean(createReqVO, TradePairControlDO.class);
        tradePairControlDO.setId(null);
//        TradePairTenantDO tradePairTenantDO = tradePairTenantMapper.selectByTradePairId(createReqVO.getTenantId(), createReqVO.getTradePairId());
//        if (tradePairTenantDO == null) {
//            throw exception(TRADE_PAIR_NOT_EXISTS);
//        }
        String code = tradePairMapper.getCodeById(createReqVO.getTradePairId());
        tradePairControlDO.setTradePairCode(code);
        tradePairControlMapper.insert(tradePairControlDO);
        return tradePairControlDO.getId();
    }

    @Override
    public void update(TradePairControlSaveReqVO updateReqVO) {
        // 更新
        TradePairControlDO updateObj = BeanUtils.toBean(updateReqVO, TradePairControlDO.class);
//        TradePairTenantDO tradePairTenantDO = tradePairTenantMapper.selectByTradePairId(updateObj.getTenantId(), updateReqVO.getTradePairId());
//        if (tradePairTenantDO == null) {
//            throw exception(TRADE_PAIR_NOT_EXISTS);
//        }
        String code = tradePairMapper.getCodeById(updateReqVO.getTradePairId());
        updateObj.setTradePairCode(code);
        tradePairControlMapper.updateById(updateObj);
    }

    @Override
    public List<TradePairControlDO> getTradePairControlToGen() {
        TenantContextHolder.setIgnore(true);
        LambdaQueryWrapperX<TradePairControlDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TradePairControlDO::getStatus, false);
        List<TradePairControlDO> list = tradePairControlMapper.selectList(queryWrapperX);
        TenantContextHolder.clear();
        return list;
    }

    @Override
    public void setFinish(long id) {
        TenantContextHolder.setIgnore(true);
        LambdaUpdateWrapper<TradePairControlDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TradePairControlDO::getId, id).set(TradePairControlDO::getStatus, 1);
        tradePairControlMapper.update(updateWrapper);
        TenantContextHolder.clear();
    }
}
