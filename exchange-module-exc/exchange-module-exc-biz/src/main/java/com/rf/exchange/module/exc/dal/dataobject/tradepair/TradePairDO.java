package com.rf.exchange.module.exc.dal.dataobject.tradepair;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 系统交易对 DO
 *
 * <AUTHOR>
 */
@TableName("exchange_trade_pair")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradePairDO extends BaseDO {

    @Serial
    private final static long serialVersionUID = 1L;
    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 交易对名称
     */
    private String name;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 基础资产
     */
    private String baseAsset;
    /**
     * 报价资产
     */
    private String quoteAsset;
    /**
     * 交易类型 0:现货 1:合约期货 2:杠杆保证金
     */
    private Integer tradeType;
    /**
     * 资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇
     */
    private Integer assetType;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;
    /**
     * 数据同步状态(0正常 1停止同步数据)
     */
    private Integer syncStatus;
    /**
     * 三方数据源 0:polygon 1:alltick
     */
    private Integer source;
    /**
     * 小数部分显示多少位
     */
    private Integer scale;
    /**
     * 标记价格(数据库中只存储一个标记价格，最新价格从redis中获取)
     */
    private BigDecimal markPrice;
    /**
     * 删除时间
     */
    private Long deletedTime;
    /**
     * 涨跌幅百分比
     */
    private BigDecimal percentage;
    /**
     * 当前价格
     */
    @TableField(exist = false)
    private BigDecimal currentPrice;
    /**
     * 熱門，只有在租戶的交易對有
     */
    @TableField(exist = false)
    private Boolean hot;
    /**
     * 是否默认交易对，只有在租戶的交易對有
     */
    @TableField(exist = false)
    private Boolean isDefault;
    /**
     * 显示排序
     */
    @TableField(exist = false)
    private Integer sort;

    /**
     * 是否自发币
     */
    private Boolean isCustom;

    /**
     * 是否复制参考币
     */
    private Boolean isCopy;
    /**
     * k线开始时间
     */
    private Long candleStartTime;

    /**
     * 参考币种
     */
    private String referenceCode;

    /**
     * 发行价
     */
    private BigDecimal issuedPrice;

    /**
     * 每分钟成交额基数
     */
    private BigDecimal oneMinuteTurnover;

    /**
     * k线时间
     */
    private long candleTime;

    /**
     * 交易对图标
     */
    private String icon;

    /**
     * 是否是全天24小时交易对的交易对
     * 加密货币主要是全天24小时交易的交易对，
     * 其他类型的交易对则不是全天交易
     */
    private Boolean is24Hour;

    /**
     * 夏令时的交易时间
     */
    private Long transactionTimeDstId;

    /**
     * 冬令时的交易时间
     */
    private Long transactionTimeWinterId;
}