package com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 租户交易对资产类型配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradeAssetTypeTenantRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10448")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "交易对类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("交易对类型")
    private Long tradeType;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("开启状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

}