package com.rf.exchange.module.exc.controller.admin.tradepair.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 系统交易对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradePairPageReqVO extends PageParam {

    @Schema(description = "交易对名称", example = "张三")
    private String name;

    @Schema(description = "交易对代码")
    private String code;

    @Schema(description = "交易类型 0:现货 1:合约期货 2:杠杆保证金", example = "1")
    private Integer tradeType;

    @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇", example = "1")
    private Integer assetType;

    @Schema(description = "开启状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "三方数据源 0:polygon 1:alltick")
    private Integer source;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}