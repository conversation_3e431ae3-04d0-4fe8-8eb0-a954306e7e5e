package com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant;

import com.rf.exchange.framework.mybatis.core.dataobject.BaseNoDeleteDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 租户交易对类型配置 DO
 *
 * <AUTHOR>
 */
@TableName("exchange_trade_asset_type_tenant")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeAssetTypeTenantDO extends BaseNoDeleteDO {
    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 交易对资产类型
     */
    private Long assetType;
    /**
     * 资产名称code 国际化需要
     */
    private String nameI18n;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态（0正常 1停用）
     */
    private Integer status;

}