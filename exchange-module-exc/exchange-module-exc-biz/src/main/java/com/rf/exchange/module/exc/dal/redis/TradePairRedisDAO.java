package com.rf.exchange.module.exc.dal.redis;

import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.rf.exchange.module.exc.dal.redis.RedisKeyConstants.TRADE_PAIR;
import static com.rf.exchange.module.exc.dal.redis.RedisKeyConstants.TRADE_PAIR_COPY_OF_REFERENCE;

/**
 * 系统交易对的缓存
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Repository
public class TradePairRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 获取系统支持的所有交易对(不区分租户)
     *
     * @return 交易对列表
     */
    public List<TradePairDO> getAll() {
        String redisKey = formatKey();
        return JsonUtils.parseArray(stringRedisTemplate.opsForValue().get(redisKey), TradePairDO.class);
    }

    /**
     * 更新系统支持的所有交易对(不区分租户)
     *
     * @param list 交易对列表
     */
    public void setAll(List<TradePairDO> list) {
        String redisKey = formatKey();
        String jsonString = JsonUtils.toJsonString(list);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString, 30, TimeUnit.DAYS);
    }

    /**
     * 删除系统支持的所有交易对(不区分租户)
     */
    public void deleteAll() {
        String redisKey = formatKey();
        stringRedisTemplate.delete(redisKey);
    }

    private static String formatKey() {
        return TRADE_PAIR + ":all";
    }


    @CacheEvict(cacheNames = RedisKeyConstants.CUSTOM_TRADE_PAIR_LIST)
    public void clearCustomTradePair() {
        stringRedisTemplate.delete(RedisKeyConstants.CUSTOM_TRADE_PAIR_LIST);
    }

    // ------------ 复制币 --------------

    /**
     * 保存引用交易对的复制币信息
     *
     * @param referenceCode 引用交易对
     * @param tradePairDO   复制币交易对
     */
    public void saveCopyTradeOf(String referenceCode, TradePairDO tradePairDO) {
        final String key = formatCopyKey(referenceCode);
        final String json = JsonUtils.toJsonString(tradePairDO);
        stringRedisTemplate.opsForHash().put(key, tradePairDO.getCode(), json);
    }

    /**
     * 获取引用交易对的复制币信息
     *
     * @param referenceCode 引用交易对
     * @return 复制币信息列表
     */
    public List<TradePairDO> getAllCopyTradesOf(String referenceCode) {
        final String key = formatCopyKey(referenceCode);
        final Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        List<TradePairDO> list = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            TradePairDO tradePairDO = JsonUtils.parseObject(entry.getValue().toString(), TradePairDO.class);
            list.add(tradePairDO);
        }
        return list;
    }

    /**
     * 获取指定引用交易对的指定复制币信息
     *
     * @param referenceCode 引用的交易对代码
     * @param copyTradeCode 复制币代码
     * @return 复制币的交易对信息
     */
    public TradePairDO getCopyTradeOf(String referenceCode, String copyTradeCode) {
        final String key = formatCopyKey(referenceCode);
        final Object value = stringRedisTemplate.opsForHash().get(key, copyTradeCode);
        assert value != null;
        return JsonUtils.parseObject(value.toString(), TradePairDO.class);
    }

    /**
     * 删除复制币的引用交易对缓存
     *
     * @param referenceCode 引用交易对代码
     * @param tradePairCode 复制币代码
     */
    public void deleteReferenceCopy(String referenceCode, String tradePairCode) {
        final String key = formatCopyKey(referenceCode);
        stringRedisTemplate.opsForHash().delete(key, tradePairCode);
    }

    public void clearCopyTradePair(String referCode) {
        stringRedisTemplate.delete(formatCopyKey(referCode));
    }

    private String formatCopyKey(String referenceCode) {
        return TRADE_PAIR_COPY_OF_REFERENCE + ":" + referenceCode;
    }
}
