package com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.rf.exchange.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 租户交易对资产类型配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradeAssetTypeTenantPageReqVO extends PageParam {

    @Schema(description = "租户id", example = "1")
    private Long tenantId;

    @Schema(description = "交易对资产类型", example = "1")
    private Long assetType;

    @Schema(description = "资产名称code")
    private String nameI18n;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "开启状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    // @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Long[] createTime;

}