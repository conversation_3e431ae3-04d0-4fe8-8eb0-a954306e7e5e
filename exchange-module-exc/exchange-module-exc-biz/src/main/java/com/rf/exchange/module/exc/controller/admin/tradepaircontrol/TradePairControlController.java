package com.rf.exchange.module.exc.controller.admin.tradepaircontrol;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlCopySaveReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlRespVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import com.rf.exchange.module.exc.service.tradepaircontrol.TradePairControlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 交易对控制")
@RestController
@RequestMapping("/exchange/trade-pair-control")
@Validated
public class TradePairControlController {
    @Resource
    private TradePairControlService tradePairControlService;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;


    @PostMapping("/createcopycontrol")
    @Operation(summary = "创建复制币控盘")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-control:query')")
    public CommonResult<Long> createCopyControl(@Valid @RequestBody TradePairControlCopySaveReqVO createReqVO) {
     TradePairControlSaveReqVO saveReqVO=new TradePairControlSaveReqVO();
        saveReqVO.setTradePairId(createReqVO.getTradePairId());
        saveReqVO.setStartTime(createReqVO.getMinuteTime());
        saveReqVO.setEndPrice(createReqVO.getClosePrice());
        saveReqVO.setEndTime(0L);
        saveReqVO.setHighPrice(createReqVO.getHighPrice());
        saveReqVO.setLowPrice(createReqVO.getLowPrice());
        saveReqVO.setOneMinuteTurnover(BigDecimal.ZERO);

        long id = tradePairControlService.create(saveReqVO);
        TradePairControlDO tradePairControlDO=tradePairControlService.get(id);
        tradePairControlRedisDAO.setControl(tradePairControlDO);
        return success(id);
    }

    @PostMapping("/create")
    @Operation(summary = "创建系统交易对控制")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-control:query')")
    public CommonResult<Long> create(@Valid @RequestBody TradePairControlSaveReqVO createReqVO) {
        long id = tradePairControlService.create(createReqVO);
        TradePairControlDO tradePairControlDO = tradePairControlService.get(id);
        tradePairControlRedisDAO.setControl(tradePairControlDO);
        return success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新系统交易对控制")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-control:query')")
    public CommonResult<Boolean> update(@Valid @RequestBody TradePairControlSaveReqVO updateReqVO) {
        tradePairControlService.update(updateReqVO);
        TradePairControlDO tradePairControlDO = tradePairControlService.get(updateReqVO.getId());
        tradePairControlRedisDAO.setControl(tradePairControlDO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统交易对控制")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-control:query')")
    public CommonResult<TradePairControlRespVO> get(@RequestParam("id") Long id) {
        TradePairControlDO tradePair = tradePairControlService.get(id);
        return success(BeanUtils.toBean(tradePair, TradePairControlRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统交易对控制分页")
    @PreAuthorize("@ss.hasPermission('exchange:trade-pair-control:query')")
    public CommonResult<PageResult<TradePairControlRespVO>> getPage(@Valid TradePairControlPageReqVO pageReqVO) {
        PageResult<TradePairControlDO> pageResult = tradePairControlService.getList(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradePairControlRespVO.class));
    }


}
