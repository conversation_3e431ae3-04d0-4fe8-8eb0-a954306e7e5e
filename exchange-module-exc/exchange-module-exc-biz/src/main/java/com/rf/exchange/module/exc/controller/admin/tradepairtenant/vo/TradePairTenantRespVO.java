package com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.module.exc.enums.TradeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 租户交易对 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradePairTenantRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12288")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12288")
    @ExcelProperty("租户编号")
    private Long tenantId;

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "12288")
    @ExcelProperty("租户名称")
    private String tenantName;

    //@Schema(description = "交易对编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1543")
    //@ExcelProperty("交易对编号")
    @JsonIgnore
    private Long tradePairId;

    @Schema(description = "排序规则 倒序排列", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("开启状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "更新人")
    @ExcelProperty("更新人")
    private String updater;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long createTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private Long updateTime;


    @Schema(description = "交易对信息")
    private TradePairInfo tradePairInfo;

    @Schema(description = "交易对信息")
    @Data
    public static class TradePairInfo {

        @Schema(description = "交易对信息")
        private Long id;

        @Schema(description = "交易对名称")
        @ExcelProperty("交易对名称")
        private String name;

        @Schema(description = "交易对代码")
        @ExcelProperty("交易对代码")
        private String code;

        @Schema(description = "基础资产")
        @ExcelProperty("基础资产")
        private String baseAsset;

        @Schema(description = "报价资产")
        @ExcelProperty("报价资产")
        private String quoteAsset;

        @Schema(description = "标记价格 (非实时价格)")
        @ExcelIgnore
        private String markPrice = "0.00"; // 默认为0.00

        @Schema(description = "涨跌幅百分比")
        @ExcelIgnore
        private String percentage = "0.00"; // 默认为0.00%

        /**
         * {@link TradeTypeEnum}
         */
        @Schema(description = "交易类型 0:现货 1:合约期货 2:杠杆保证金")
        @ExcelProperty("交易类型 0:现货 1:合约期货 2:杠杆保证金")
        private Integer tradeType;

        /**
         * {@link TradeAssetTypeEnum}
         */
        @Schema(description = "资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇")
        @ExcelProperty("资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇")
        private Integer assetType;

        //@ExcelProperty("开启状态（0正常 1停用）")
        @JsonIgnore
        private Integer status;

        @Schema(description = "三方数据源 0:polygon 1:alltick")
        @ExcelProperty("三方数据源 0:polygon 1:alltick")
        private Integer source;

        @Schema(description = "显示位数")
        @ExcelProperty("显示位数")
        private Integer scale;

        @Schema(description = "热门", requiredMode = Schema.RequiredMode.REQUIRED)
        private Boolean hot;

        @Schema(description = "热门", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "isDefault")
        private Boolean isDefault;

        @Schema(description = "是否自发币", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "isCustom")
        private Boolean isCustom;

        @Schema(description = "是否复制币")
        private Boolean isCopy;
    }
}