package com.rf.exchange.module.exc.api.tradeassettype;

import com.rf.exchange.module.exc.api.tradeassettype.dto.TenantTradeAssetTypeRespDTO;
import com.rf.exchange.module.exc.convert.TradeAssetTypeConvert;
import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import com.rf.exchange.module.exc.service.tradetypetenant.TradeAssetTypeTenantService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
public class TradeAssetAssetTypeApiImpl implements TradeAssetTypeApi {

    @Resource
    private TradeAssetTypeTenantService typeTenantService;

    @Override
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        typeTenantService.copyDataFromTenant(sourceTenantId, targetTenantId);
    }

    @Override
    public List<TenantTradeAssetTypeRespDTO> getTradeTypes(Long tenantId) {
        List<TradeAssetTypeTenantDO> doList = typeTenantService.getTradeAssetTypeTenantList(tenantId);
        return TradeAssetTypeConvert.INSTANCE.convertList(doList);
    }
}
