package com.rf.exchange.module.exc.dal.mysql.tradetypetenant;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import org.apache.ibatis.annotations.Mapper;
import com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo.*;

/**
 * 租户交易对类型配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradeTypeTenantMapper extends BaseMapperX<TradeAssetTypeTenantDO> {

    default PageResult<TradeAssetTypeTenantDO> selectPage(TradeAssetTypeTenantPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TradeAssetTypeTenantDO>()
                .eqIfPresent(TradeAssetTypeTenantDO::getAssetType, reqVO.getAssetType())
                .eqIfPresent(TradeAssetTypeTenantDO::getSort, reqVO.getSort())
                .eqIfPresent(TradeAssetTypeTenantDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TradeAssetTypeTenantDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TradeAssetTypeTenantDO::getId));
    }

}