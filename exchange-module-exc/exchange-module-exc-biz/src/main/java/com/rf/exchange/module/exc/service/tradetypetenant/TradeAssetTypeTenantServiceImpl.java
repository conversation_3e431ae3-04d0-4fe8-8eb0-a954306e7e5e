package com.rf.exchange.module.exc.service.tradetypetenant;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.exc.dal.dataobject.tradeassettypetenant.TradeAssetTypeTenantDO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo.*;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;

import com.rf.exchange.module.exc.dal.mysql.tradetypetenant.TradeTypeTenantMapper;

import static com.rf.exchange.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.rf.exchange.module.exc.enums.ErrorCodeConstants.*;

/**
 * 租户交易对类型配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradeAssetTypeTenantServiceImpl implements TradeAssetTypeTenantService {

    @Resource
    private TradeTypeTenantMapper tradeTypeTenantMapper;

    @Override
    @Master
    @DSTransactional
    public void copyDataFromTenant(Long sourceTenantId, Long targetTenantId) {
        if (sourceTenantId == null || targetTenantId == null) {
            throw exception(GlobalErrorCodeConstants.VALUE_ERROR);
        }
        TenantContextHolder.setIgnore(true);

        LambdaQueryWrapperX<TradeAssetTypeTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(TradeAssetTypeTenantDO::getTenantId, sourceTenantId);
        wrapper.eq(TradeAssetTypeTenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus());

        List<TradeAssetTypeTenantDO> list = tradeTypeTenantMapper.selectList(wrapper);
        for (TradeAssetTypeTenantDO tradeAssetTypeTenantDO : list) {
            tradeAssetTypeTenantDO.setId(null);
            tradeAssetTypeTenantDO.setTenantId(targetTenantId);
        }
        tradeTypeTenantMapper.insertBatch(list);

        TenantContextHolder.clearIgnore();
    }

    @Override
    @Master
    @DSTransactional
    public Long createTradeAssetTypeTenant(TradeAssetTypeTenantSaveReqVO createReqVO) {
        // 插入
        TradeAssetTypeTenantDO tradeTypeTenant = BeanUtils.toBean(createReqVO, TradeAssetTypeTenantDO.class);
        tradeTypeTenantMapper.insert(tradeTypeTenant);
        // 返回
        return tradeTypeTenant.getId();
    }

    @Override
    @Master
    @DSTransactional
    public void updateTradeAssetTypeTenant(TradeAssetTypeTenantSaveReqVO updateReqVO) {
        // 校验存在
        validateTradeTypeTenantExists(updateReqVO.getId());
        // 更新
        TradeAssetTypeTenantDO updateObj = BeanUtils.toBean(updateReqVO, TradeAssetTypeTenantDO.class);
        tradeTypeTenantMapper.updateById(updateObj);
    }

    @Override
    @Master
    @DSTransactional
    public void deleteTradeAssetTypeTenant(Long id) {
        // 校验存在
        validateTradeTypeTenantExists(id);
        // 删除
        tradeTypeTenantMapper.deleteById(id);
    }

    private void validateTradeTypeTenantExists(Long id) {
        if (tradeTypeTenantMapper.selectById(id) == null) {
            throw exception(TRADE_TENANT_ASSET_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public TradeAssetTypeTenantDO getTradeAssetTypeTenant(Long id) {
        return tradeTypeTenantMapper.selectById(id);
    }

    @Override
    public PageResult<TradeAssetTypeTenantDO> getTradeAssetTypeTenantPage(TradeAssetTypeTenantPageReqVO pageReqVO) {
        return tradeTypeTenantMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TradeAssetTypeTenantDO> getTradeAssetTypeTenantList(Long tenantId) {
        LambdaQueryWrapperX<TradeAssetTypeTenantDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(TradeAssetTypeTenantDO::getTenantId, tenantId);
        wrapper.orderByAsc(TradeAssetTypeTenantDO::getSort);
        return tradeTypeTenantMapper.selectList(wrapper);
    }
}