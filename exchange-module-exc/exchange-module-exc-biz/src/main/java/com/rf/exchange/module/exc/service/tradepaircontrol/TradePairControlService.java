package com.rf.exchange.module.exc.service.tradepaircontrol;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlSaveReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import jakarta.validation.Valid;

import java.util.List;

public interface TradePairControlService {

    /**
     * 获得系统交易对控盘分页
     *
     * @param pageReqVO 分页查询
     * @return 系统交易对分页
     */
    PageResult<TradePairControlDO> getList(TradePairControlPageReqVO pageReqVO);

    /**
     * 获得系统交易对控盘明细
     * @param id
     * @return
     */
    TradePairControlDO get(Long id);


    /**
     * 创建系统交易对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid TradePairControlSaveReqVO createReqVO);

    /**
     * 更新系统交易对
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid TradePairControlSaveReqVO updateReqVO);

    /**
     * 获取需要控制的交易对
     * @return
     */
    List<TradePairControlDO> getTradePairControlToGen();

    /**
     * 设置控制设置完成
     * @param id
     */
    void setFinish(long id);
}
