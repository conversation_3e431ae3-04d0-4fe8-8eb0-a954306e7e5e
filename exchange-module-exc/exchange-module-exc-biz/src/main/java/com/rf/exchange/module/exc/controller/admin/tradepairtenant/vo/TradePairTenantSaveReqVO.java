package com.rf.exchange.module.exc.controller.admin.tradepairtenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 租户交易对新增/修改 Request VO")
@Data
public class TradePairTenantSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;

    @Schema(description = "交易对ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "交易对ID不能为空")
    private Long tradePairId;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "排序 倒序值越大排在越前面")
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "熱門", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否為熱門")
    private Boolean hot;

    @Schema(description = "是否默认交易对", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    @NotNull(message = "isDefault不能为空")
    private Boolean isDefault = false;
}