package com.rf.exchange.module.exc.dal.mysql.tradetransactionhour;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.module.exc.dal.dataobject.tradetransactionhour.TradeTransactionTimeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @since 2024-09-29
 */
@Mapper
public interface TradeTransactionTimeMapper extends BaseMapperX<TradeTransactionTimeDO> {
}
