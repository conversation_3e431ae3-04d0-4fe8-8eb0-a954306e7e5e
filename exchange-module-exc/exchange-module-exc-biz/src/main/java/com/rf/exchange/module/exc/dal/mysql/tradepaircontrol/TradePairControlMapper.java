package com.rf.exchange.module.exc.dal.mysql.tradepaircontrol;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.exc.controller.admin.tradepair.vo.TradePairPageReqVO;
import com.rf.exchange.module.exc.controller.admin.tradepaircontrol.vo.TradePairControlPageReqVO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairControlDO;
import com.rf.exchange.module.exc.dal.dataobject.tradepair.TradePairDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 系统交易对 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradePairControlMapper extends BaseMapperX<TradePairControlDO> {

    default PageResult<TradePairControlDO> selectPage( TradePairControlPageReqVO reqVO) {
        LambdaQueryWrapperX<TradePairControlDO> queryWrapperX = new LambdaQueryWrapperX<TradePairControlDO>()
                .likeIfPresent(TradePairControlDO::getTradePairCode, reqVO.getTradePair())
                .eqIfPresent(TradePairControlDO::getTenantId,reqVO.getTenantId())
                .orderByDesc(TradePairControlDO::getId);
        return selectPage(reqVO, queryWrapperX);
    }
}