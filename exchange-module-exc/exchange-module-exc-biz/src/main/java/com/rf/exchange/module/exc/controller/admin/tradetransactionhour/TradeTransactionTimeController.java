package com.rf.exchange.module.exc.controller.admin.tradetransactionhour;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.module.exc.service.tradetransactionhour.TradeTransactionTimeService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-10-03
 */
@RestController
@RequestMapping("/exchange/trade-transaction-time")
public class TradeTransactionTimeController {

    @Resource
    private TradeTransactionTimeService tradeTransactionTimeService;

    @PostMapping("/update-cache")
    @Operation(summary = "更新缓存")
    public CommonResult<Boolean> updateCache() {
        tradeTransactionTimeService.updateTransactionTimeCache();
        return CommonResult.success(true);
    }
}
