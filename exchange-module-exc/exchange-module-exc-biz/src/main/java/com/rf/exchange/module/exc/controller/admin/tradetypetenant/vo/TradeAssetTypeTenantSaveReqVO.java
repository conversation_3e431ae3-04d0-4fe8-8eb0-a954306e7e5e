package com.rf.exchange.module.exc.controller.admin.tradetypetenant.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 租户交易对类型配置新增/修改 Request VO")
@Data
public class TradeAssetTypeTenantSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10448")
    private Long id;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long tenantId;

    @Schema(description = "交易对类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "交易对类型不能为空")
    private Long tradeType;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "开启状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "开启状态（0正常 1停用）不能为空")
    private Integer status;

}