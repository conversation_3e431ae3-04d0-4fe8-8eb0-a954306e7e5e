COUNTRY_TH=Th\u00E1i Lan
COUNTRY_JP=Nh\u1EADt B\u1EA3n
COUNTRY_KR=H\u00E0n Qu\u1ED1c
COUNTRY_PH=Philippines
COUNTRY_VN=Vi\u1EC7t Nam
COUNTRY_CN=Trung Qu\u1ED1c
COUNTRY_GB=V\u01B0\u01A1ng qu\u1ED1c Anh
COUNTRY_FR=Ph\u00E1p
COUNTRY_US=Hoa K\u1EF3
COUNTRY_ES=T\u00E2y Ban Nha
COUNTRY_DE=\u0110\u1EE9c
COUNTRY_RU=Nga
COUNTRY_CA=Canada
COUNTRY_TR=Th\u1ED5 Nh\u0129 K\u1EF3
COUNTRY_PT=B\u1ED3 \u0110\u00E0o Nha
COUNTRY_MA=Ma-r\u1ED1c
COUNTRY_DZ=An-gi\u00EA-ri
COUNTRY_IT=\u00DD
COUNTRY_CO=Colombia
COUNTRY_MX=Mexico
COUNTRY_CH=Th\u1EE5y S\u0129
COUNTRY_BE=B\u1EC9
COUNTRY_AR=Argentina
COUNTRY_NO=Na Uy
COUNTRY_HK=H\u1ED3ng K\u00F4ng
BANK=Th\u1EBB ng\u00E2n h\u00E0ng
CRYPTO=ti\u1EC1n \u0111i\u1EC7n t\u1EED
USER_CERTIFIED_STATUS_NOT=Ch\u01B0a x\u00E1c minh
USER_CERTIFIED_STATUS_HANDLING=\u0110ang x\u00E1c minh
USER_CERTIFIED_STATUS_SUCCESS=\u0110\u00E3 x\u00E1c minh
USER_CERTIFIED_STATUS_FAIL=Qu\u00E1 tr\u00ECnh x\u00E1c th\u1EF1c \u0111\u00E3 th\u1EA5t b\u1EA1i
ACCOUNT_NOT_EMPTY=T\u00E0i kho\u1EA3n kh\u00F4ng th\u1EC3 tr\u1ED1ng
PASSWORD_NOT_EMPTY=m\u1EADt kh\u1EA9u kh\u00F4ng th\u1EC3 \u0111\u1EC3 tr\u1ED1ng
PASSWORD_NOT_LENGTH_6_16=\u0110\u1ED9 d\u00E0i m\u1EADt kh\u1EA9u l\u00E0 6-16 k\u00FD t\u1EF1
PASSWORD_FORMATTER_ERROR=\u0110\u1ECBnh d\u1EA1ng m\u1EADt kh\u1EA9u l\u00E0 s\u1ED1 v\u00E0 ch\u1EEF c\u00E1i
ACCOUNT_TYPE_ERROR=Lo\u1EA1i t\u00E0i kho\u1EA3n sai
MAIL_FORMATTER_ERROR=L\u1ED7i \u0111\u1ECBnh d\u1EA1ng email
MOBILE_FORMATTER_ERROR=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i kh\u00F4ng \u0111\u00FAng \u0111\u1ECBnh d\u1EA1ng
AREA_NOT_EMPTY=Khu v\u1EF1c kh\u00F4ng th\u1EC3 tr\u1ED1ng
MAIL_SCENE_ERROR=L\u1ED7i k\u1ECBch b\u1EA3n g\u1EEDi SMS
SMS_SCENE_ERROR=L\u1ED7i k\u1ECBch b\u1EA3n g\u1EEDi SMS
REAL_NAME_NOT_EMPTY=\u0110\u1EEBng \u0111\u1EC3 tr\u1ED1ng t\u00EAn th\u1EADt
CERTIFICATION_TYPE_ERROR=Lo\u1EA1i x\u00E1c th\u1EF1c sai
CERTIFICATION_CODE_NOT_EMPTY=S\u1ED1 ID kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
CERTIFICATION_FRONT_NOT_EMPTY=\u1EA2nh m\u1EB7t tr\u01B0\u1EDBc c\u1EE7a CMND kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
CERTIFICATION_BACK_NOT_EMPTY=\u1EA2nh m\u1EB7t sau CMND kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
TRADE_PAIR_NOT_EMPTY=Vui l\u00F2ng ch\u1ECDn m\u1ED9t c\u1EB7p giao d\u1ECBch
FUNDS_RECORD_OP_TYPE=Sai lo\u1EA1i qu\u1EF9
CURRENCY_NOT_EMPTY=Vui l\u00F2ng ch\u1ECDn lo\u1EA1i ti\u1EC1n t\u1EC7
PAY_METHOD_ERROR=Vui l\u00F2ng ch\u1ECDn ph\u01B0\u01A1ng th\u1EE9c thanh to\u00E1n
RECHARGE_AMOUNT_ERROR=Vui l\u00F2ng nh\u1EADp s\u1ED1 ti\u1EC1n n\u1EA1p
WITHDRAW_AMOUNT_ERROR=Nh\u1EADp \u0111\u1EA7y \u0111\u1EE7 s\u1ED1 ti\u1EC1n r\u00FAt
FUNDS_PASSWORD_ERROR=M\u1EADt kh\u1EA9u giao d\u1ECBch kh\u00F4ng \u0111\u00FAng
WALLET_NOT_EMPTY=Vui l\u00F2ng ch\u1ECDn v\u00ED
AUTH_CODE_NOT_EMPTY=vui l\u00F2ng nh\u1EADp m\u00E3 x\u00E1c nh\u1EADn
AVATAR_FORMATTER_ERROR=Vui l\u00F2ng ch\u1ECDn m\u1ED9t h\u00ECnh \u0111\u1EA1i di\u1EC7n
WALLET_TYPE_ERROR=Lo\u1EA1i v\u00ED sai
WALLET_NAME_NOT_EMPTY=Vui l\u00F2ng nh\u1EADp t\u00EAn v\u00ED
WALLET_ACCOUNT_NOT_EMPTY=Vui l\u00F2ng nh\u1EADp t\u00E0i kho\u1EA3n v\u00ED
WALLET_TYPE_NAME_NOT_EMPTY=Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u00E2n h\u00E0ng
ASSET_Type_ERROR=Lo\u1EA1i t\u00E0i s\u1EA3n sai
KEY_NOT_EMPTY=T\u1EEB kh\u00F3a kh\u00F4ng th\u1EC3 tr\u1ED1ng
AMOUNT_NOT_EMPTY=S\u1ED1 ti\u1EC1n sai
TRADE_DIRECT_ERROR=Giao d\u1ECBch sai h\u01B0\u1EDBng
TRADE_DURATION_ERROR=L\u1ED7i gi\u1EDBi h\u1EA1n th\u1EDDi gian giao d\u1ECBch
TRADE_SEND_TIME_ERROR=Giao d\u1ECBch g\u1EEDi kh\u00F4ng \u0111\u00FAng th\u1EDDi \u0111i\u1EC3m
TRADE_PRICE_TIME_ERROR=L\u1ED7i th\u1EDDi gian gi\u00E1 giao d\u1ECBch
TRADE_PAGE_TYPE_ERROR=L\u1ED7i ph\u00E2n lo\u1EA1i
SUCCESS=X\u1EED l\u00FD th\u00E0nh c\u00F4ng
WAITHANDLE=\u0110ang x\u1EED l\u00FD
FAILURE=X\u1EED l\u00FD kh\u00F4ng th\u00E0nh c\u00F4ng
PENDING=\u0110ang x\u1EED l\u00FD
BAD_REQUEST=Y\u00EAu c\u1EA7u x\u1EA5u
UNKNOW_AUTHORIZED=Lo\u1EA1i \u1EE7y quy\u1EC1n kh\u00F4ng x\u00E1c \u0111\u1ECBnh[{}]
TOKEN_NOT_SUPPORT_MODE=Giao di\u1EC7n m\u00E3 th\u00F4ng b\u00E1o kh\u00F4ng h\u1ED7 tr\u1EE3 ch\u1EBF \u0111\u1ED9 \u1EE7y quy\u1EC1n [{}]
CLIENT_ERROR=client_id ho\u1EB7c client_secret kh\u00F4ng \u0111\u01B0\u1EE3c chuy\u1EC3n ch\u00EDnh x\u00E1c
TOKEN_REFRESH_INVALID=M\u00E3 th\u00F4ng b\u00E1o l\u00E0m m\u1EDBi kh\u00F4ng h\u1EE3p l\u1EC7
TOKEN_REFRESH_CLIENT_ERROR=S\u1ED1 kh\u00E1ch h\u00E0ng kh\u00F4ng ch\u00EDnh x\u00E1c cho m\u00E3 th\u00F4ng b\u00E1o l\u00E0m m\u1EDBi
TOKEN_REFRESH_EXPIRE=M\u00E3 th\u00F4ng b\u00E1o l\u00E0m m\u1EDBi \u0111\u00E3 h\u1EBFt h\u1EA1n
TOKEN_NOT_EXISTS=M\u00E3 th\u00F4ng b\u00E1o truy c\u1EADp kh\u00F4ng t\u1ED3n t\u1EA1i
TOKEN_EXPIRE=M\u00E3 th\u00F4ng b\u00E1o truy c\u1EADp \u0111\u00E3 h\u1EBFt h\u1EA1n
GRANT_RESPONSE_TYPE_ERROR=Gi\u00E1 tr\u1ECB tham s\u1ED1 reply_type ch\u1EC9 cho ph\u00E9p m\u00E3 v\u00E0 m\u00E3 th\u00F4ng b\u00E1o
UNAUTHORIZED=T\u00E0i kho\u1EA3n ch\u01B0a \u0111\u0103ng nh\u1EADp
FORBIDDEN=Kh\u00F4ng c\u00F3 s\u1EF1 cho ph\u00E9p cho ho\u1EA1t \u0111\u1ED9ng n\u00E0y
NOT_FOUND=T\u00E0i nguy\u00EAn \u0111\u01B0\u1EE3c y\u00EAu c\u1EA7u kh\u00F4ng t\u1ED3n t\u1EA1i
METHOD_NOT_ALLOWED=Ph\u01B0\u01A1ng th\u1EE9c y\u00EAu c\u1EA7u kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p
LOCKED=\u0110\u00E3 kh\u00F3a
TOO_MANY_REQUESTS=Y\u00EAu c\u1EA7u qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn, vui l\u00F2ng th\u1EED l\u1EA1i sau.
INTERNAL_SERVER_ERROR=Ngo\u1EA1i l\u1EC7 h\u1EC7 th\u1ED1ng
NOT_IMPLEMENTED=Ch\u1EE9c n\u0103ng kh\u00F4ng \u0111\u01B0\u1EE3c tri\u1EC3n khai/kh\u00F4ng \u0111\u01B0\u1EE3c k\u00EDch ho\u1EA1t
REPEATED_REQUESTS=L\u1EB7p l\u1EA1i y\u00EAu c\u1EA7u, vui l\u00F2ng th\u1EED l\u1EA1i sau
DEMO_DENY=Ch\u1EBF \u0111\u1ED9 tr\u00ECnh di\u1EC5n, thao t\u00E1c ghi b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
VALUE_ERROR=Gi\u00E1 tr\u1ECB sai
GOOGLE_AUTH_NOT_BIND=Google Authenticator kh\u00F4ng b\u1ECB r\u00E0ng bu\u1ED9c
GOOGLE_AUTH_CODE_ERROR=L\u1ED7i m\u00E3 x\u00E1c minh Google
SCHEDULER_JOB_STOP=[L\u00EAn l\u1ECBch t\u00E1c v\u1EE5 - \u0110\u00E3 t\u1EAFt] [Tham kh\u1EA3o https://doc.iocode.cn/job/ \u0111\u1EC3 b\u1EADt]
CANDLE_TABLE_NAME_NOT_AVAILABLE=T\u00EAn b\u1EA3ng k-line kh\u00F4ng h\u1EE3p l\u1EC7
CANDLE_BAR_VALUE_ERROR=tham s\u1ED1 thanh kh\u00F4ng h\u1EE3p l\u1EC7
CANDLE_PRICE_ERROR=Nh\u1EADn l\u1ED7i gi\u00E1
TRADE_PAIR_NOT_EXISTS=C\u1EB7p giao d\u1ECBch kh\u00F4ng t\u1ED3n t\u1EA1i
TRADE_PAIR_EXISTS=C\u1EB7p giao d\u1ECBch \u0111\u00E3 t\u1ED3n t\u1EA1i
TRADE_PAIR_TENANT_NOT_EXISTS=Ng\u01B0\u1EDDi thu\u00EA kh\u00F4ng c\u00F3 c\u1EB7p giao d\u1ECBch
TRADE_PAIR_TENANT_EXISTS=C\u1EB7p giao d\u1ECBch thu\u00EA \u0111\u00E3 t\u1ED3n t\u1EA1i
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=C\u1EA5u h\u00ECnh lo\u1EA1i t\u00E0i s\u1EA3n c\u1EB7p giao d\u1ECBch \u0111\u1ED1i t\u01B0\u1EE3ng thu\u00EA kh\u00F4ng t\u1ED3n t\u1EA1i
TRADE_TENANT_NEED_DEFAULT=Ph\u1EA3i c\u00F3 m\u1ED9t c\u1EB7p giao d\u1ECBch thu\u00EA m\u1EB7c \u0111\u1ECBnh
TRADE_TENANT_DUPLICATE_DEFAULT=C\u1EB7p giao d\u1ECBch m\u1EB7c \u0111\u1ECBnh c\u1EE7a ng\u01B0\u1EDDi thu\u00EA \u0111\u00E3 t\u1ED3n t\u1EA1i
CONFIG_NOT_EXISTS=C\u1EA5u h\u00ECnh tham s\u1ED1 kh\u00F4ng t\u1ED3n t\u1EA1i
CONFIG_KEY_DUPLICATE=Ph\u00EDm c\u1EA5u h\u00ECnh tham s\u1ED1 \u0111\u01B0\u1EE3c l\u1EB7p l\u1EA1i
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Kh\u00F4ng th\u1EC3 x\u00F3a c\u00E1c c\u1EA5u h\u00ECnh tham s\u1ED1 c\u00F3 lo\u1EA1i \u0111\u01B0\u1EE3c t\u00EDch h\u1EE3p s\u1EB5n trong h\u1EC7 th\u1ED1ng.
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Kh\u00F4ng l\u1EA5y \u0111\u01B0\u1EE3c c\u1EA5u h\u00ECnh tham s\u1ED1 L\u00FD do: Kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EA5y c\u1EA5u h\u00ECnh \u1EA9n.
JOB_NOT_EXISTS=T\u00E1c v\u1EE5 \u0111\u00E3 l\u00EAn l\u1ECBch kh\u00F4ng t\u1ED3n t\u1EA1i
JOB_HANDLER_EXISTS=B\u1ED9 x\u1EED l\u00FD cho t\u00E1c v\u1EE5 theo l\u1ECBch tr\u00ECnh \u0111\u00E3 t\u1ED3n t\u1EA1i
JOB_CHANGE_STATUS_INVALID=Ch\u1EC9 \u0111\u01B0\u1EE3c ph\u00E9p s\u1EEDa \u0111\u1ED5i th\u00E0nh b\u1EADt ho\u1EB7c t\u1EAFt
JOB_CHANGE_STATUS_EQUALS=T\u00E1c v\u1EE5 \u0111\u00E3 l\u00EAn l\u1ECBch \u0111\u00E3 \u1EDF tr\u1EA1ng th\u00E1i n\u00E0y v\u00E0 kh\u00F4ng c\u1EA7n ph\u1EA3i s\u1EEDa \u0111\u1ED5i.
JOB_UPDATE_ONLY_NORMAL_STATUS=Ch\u1EC9 c\u00F3 th\u1EC3 s\u1EEDa \u0111\u1ED5i c\u00E1c t\u00E1c v\u1EE5 \u0111ang m\u1EDF.
JOB_CRON_EXPRESSION_VALID=Bi\u1EC3u th\u1EE9c CRON kh\u00F4ng ch\u00EDnh x\u00E1c
JOB_HANDLER_BEAN_NOT_EXISTS=Bean x\u1EED l\u00FD c\u1EE7a t\u00E1c v\u1EE5 \u0111\u00E3 l\u00EAn l\u1ECBch kh\u00F4ng t\u1ED3n t\u1EA1i
JOB_HANDLER_BEAN_TYPE_ERROR=Lo\u1EA1i b\u1ED9 x\u1EED l\u00FD c\u1EE7a t\u00E1c v\u1EE5 \u0111\u00E3 l\u00EAn l\u1ECBch kh\u00F4ng ch\u00EDnh x\u00E1c v\u00E0 giao di\u1EC7n JobHandler kh\u00F4ng \u0111\u01B0\u1EE3c tri\u1EC3n khai.
API_ERROR_LOG_NOT_FOUND=Nh\u1EADt k\u00FD l\u1ED7i API kh\u00F4ng t\u1ED3n t\u1EA1i
API_ERROR_LOG_PROCESSED=Nh\u1EADt k\u00FD l\u1ED7i API \u0111\u00E3 \u0111\u01B0\u1EE3c x\u1EED l\u00FD
FILE_PATH_EXISTS=\u0110\u01B0\u1EDDng d\u1EABn t\u1EC7p \u0111\u00E3 t\u1ED3n t\u1EA1i
FILE_NOT_EXISTS=t\u1EADp tin kh\u00F4ng t\u1ED3n t\u1EA1i
FILE_IS_EMPTY=T\u1EC7p tr\u1ED1ng
CODEGEN_TABLE_EXISTS=\u0111\u1ECBnh ngh\u0129a b\u1EA3ng \u0111\u00E3 t\u1ED3n t\u1EA1i
CODEGEN_IMPORT_TABLE_NULL=B\u1EA3ng \u0111\u00E3 nh\u1EADp kh\u00F4ng t\u1ED3n t\u1EA1i
CODEGEN_IMPORT_COLUMNS_NULL=Tr\u01B0\u1EDDng \u0111\u00E3 nh\u1EADp kh\u00F4ng t\u1ED3n t\u1EA1i
CODEGEN_TABLE_NOT_EXISTS=\u0111\u1ECBnh ngh\u0129a b\u1EA3ng kh\u00F4ng t\u1ED3n t\u1EA1i
CODEGEN_COLUMN_NOT_EXISTS=\u0110\u1ECBnh ngh\u0129a tr\u01B0\u1EDDng kh\u00F4ng t\u1ED3n t\u1EA1i
CODEGEN_SYNC_COLUMNS_NULL=Tr\u01B0\u1EDDng \u0111\u01B0\u1EE3c \u0111\u1ED3ng b\u1ED9 h\u00F3a kh\u00F4ng t\u1ED3n t\u1EA1i
CODEGEN_SYNC_NONE_CHANGE=\u0110\u1ED3ng b\u1ED9 h\u00F3a kh\u00F4ng th\u00E0nh c\u00F4ng, kh\u00F4ng c\u00F3 thay \u0111\u1ED5i n\u00E0o t\u1ED3n t\u1EA1i
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Nh\u1EADn x\u00E9t b\u1EA3ng c\u01A1 s\u1EDF d\u1EEF li\u1EC7u kh\u00F4ng \u0111\u01B0\u1EE3c \u0111i\u1EC1n v\u00E0o
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Nh\u1EADn x\u00E9t tr\u01B0\u1EDDng b\u1EA3ng c\u01A1 s\u1EDF d\u1EEF li\u1EC7u ({}) kh\u00F4ng \u0111\u01B0\u1EE3c \u0111i\u1EC1n v\u00E0o
CODEGEN_MASTER_TABLE_NOT_EXISTS=\u0110\u1ECBnh ngh\u0129a b\u1EA3ng ch\u00EDnh (id={}) kh\u00F4ng t\u1ED3n t\u1EA1i, vui l\u00F2ng ki\u1EC3m tra
CODEGEN_SUB_COLUMN_NOT_EXISTS=Tr\u01B0\u1EDDng (id={}) c\u1EE7a b\u1EA3ng ph\u1EE5 kh\u00F4ng t\u1ED3n t\u1EA1i, vui l\u00F2ng ki\u1EC3m tra
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=M\u00E3 t\u1EA1o b\u1EA3ng ch\u00EDnh kh\u00F4ng th\u00E0nh c\u00F4ng, l\u00FD do: n\u00F3 kh\u00F4ng c\u00F3 b\u1EA3ng ph\u1EE5
FILE_CONFIG_NOT_EXISTS=C\u1EA5u h\u00ECnh t\u1EADp tin kh\u00F4ng t\u1ED3n t\u1EA1i
FILE_CONFIG_DELETE_FAIL_MASTER=C\u1EA5u h\u00ECnh t\u1EC7p n\u00E0y kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p x\u00F3a L\u00FD do: \u0110\u00E2y l\u00E0 c\u1EA5u h\u00ECnh ch\u00EDnh. Vi\u1EC7c x\u00F3a s\u1EBD d\u1EABn \u0111\u1EBFn kh\u00F4ng th\u1EC3 t\u1EA3i t\u1EC7p l\u00EAn.
DATA_SOURCE_CONFIG_NOT_EXISTS=C\u1EA5u h\u00ECnh ngu\u1ED3n d\u1EEF li\u1EC7u kh\u00F4ng t\u1ED3n t\u1EA1i
DATA_SOURCE_CONFIG_NOT_OK=Ngu\u1ED3n d\u1EEF li\u1EC7u \u0111\u01B0\u1EE3c c\u1EA5u h\u00ECnh kh\u00F4ng ch\u00EDnh x\u00E1c v\u00E0 kh\u00F4ng th\u1EC3 k\u1EBFt n\u1ED1i \u0111\u01B0\u1EE3c.
DEMO01_CONTACT_NOT_EXISTS=\u0110\u1ECBa ch\u1EC9 li\u00EAn h\u1EC7 m\u1EABu kh\u00F4ng t\u1ED3n t\u1EA1i
DEMO02_CATEGORY_NOT_EXISTS=Danh m\u1EE5c v\u00ED d\u1EE5 kh\u00F4ng t\u1ED3n t\u1EA1i
DEMO02_CATEGORY_EXITS_CHILDREN=C\u00F3 m\u1ED9t danh m\u1EE5c v\u00ED d\u1EE5 ph\u1EE5 v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c.
DEMO02_CATEGORY_PARENT_NOT_EXITS=Danh m\u1EE5c v\u00ED d\u1EE5 g\u1ED1c kh\u00F4ng t\u1ED3n t\u1EA1i
DEMO02_CATEGORY_PARENT_ERROR=B\u1EA1n kh\u00F4ng th\u1EC3 \u0111\u1EB7t m\u00ECnh l\u00E0m danh m\u1EE5c v\u00ED d\u1EE5 g\u1ED1c
DEMO02_CATEGORY_NAME_DUPLICATE=M\u1ED9t danh m\u1EE5c v\u00ED d\u1EE5 c\u00F3 t\u00EAn n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
DEMO02_CATEGORY_PARENT_IS_CHILD=B\u1EA1n kh\u00F4ng th\u1EC3 \u0111\u1EB7t danh m\u1EE5c v\u00ED d\u1EE5 con c\u1EE7a ri\u00EAng m\u00ECnh l\u00E0m danh m\u1EE5c v\u00ED d\u1EE5 g\u1ED1c
DEMO03_STUDENT_NOT_EXISTS=sinh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
DEMO03_GRADE_NOT_EXISTS=L\u1EDBp sinh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
DEMO03_GRADE_EXISTS=L\u1EDBp sinh vi\u00EAn \u0111\u00E3 t\u1ED3n t\u1EA1i
AREA_NOT_EXISTS=Khu v\u1EF1c n\u00E0y kh\u00F4ng t\u1ED3n t\u1EA1i
USER_NOT_EXISTS=ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
USER_MOBILE_NOT_EXISTS=Ng\u01B0\u1EDDi d\u00F9ng ch\u01B0a \u0111\u0103ng k\u00FD v\u1EDBi s\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng
USER_MOBILE_USED=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng n\u00E0y \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
USER_ACCOUNT_USED=T\u00E0i kho\u1EA3n \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
USER_EMAIL_USED=Email \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
USER_POINT_NOT_ENOUGH=S\u1ED1 d\u01B0 \u0111i\u1EC3m ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng \u0111\u1EE7
USER_OLD_PASSWORD_NOT_MATCH=Sai m\u1EADt kh\u1EA9u c\u0169
USER_BALANCE_ERROR=L\u1ED7i c\u00E2n b\u1EB1ng ng\u01B0\u1EDDi d\u00F9ng
USER_CONFIG_NOT_SUPPORTED=C\u00E1c m\u1EE5c c\u1EA5u h\u00ECnh ng\u01B0\u1EDDi d\u00F9ng hi\u1EC7n kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3
AUTH_LOGIN_BAD_CREDENTIALS=\u0110\u0103ng nh\u1EADp kh\u00F4ng th\u00E0nh c\u00F4ng, m\u1EADt kh\u1EA9u t\u00E0i kho\u1EA3n kh\u00F4ng \u0111\u00FAng
AUTH_LOGIN_USER_DISABLED=\u0110\u0103ng nh\u1EADp kh\u00F4ng th\u00E0nh c\u00F4ng, t\u00E0i kho\u1EA3n b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
AUTH_ACCOUNT_FORMAT_ERROR=L\u1ED7i \u0111\u1ECBnh d\u1EA1ng t\u00E0i kho\u1EA3n x\u00E1c th\u1EF1c
TAG_NOT_EXISTS=Th\u1EBB kh\u00F4ng t\u1ED3n t\u1EA1i
TAG_NAME_EXISTS=Th\u1EBB \u0111\u00E3 t\u1ED3n t\u1EA1i
TAG_HAS_USER=C\u00F3 m\u1ED9t ng\u01B0\u1EDDi d\u00F9ng d\u01B0\u1EDBi nh\u00E3n ng\u01B0\u1EDDi d\u00F9ng v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c.
POINT_RECORD_BIZ_NOT_SUPPORT=Lo\u1EA1i h\u00ECnh kinh doanh b\u1EA3n ghi \u0111i\u1EC3m ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3
SIGN_IN_CONFIG_NOT_EXISTS=Quy t\u1EAFc \u0111\u0103ng nh\u1EADp kh\u00F4ng t\u1ED3n t\u1EA1i
SIGN_IN_CONFIG_EXISTS=Quy t\u1EAFc ng\u00E0y nh\u1EADn ph\u00F2ng \u0111\u00E3 t\u1ED3n t\u1EA1i
SIGN_IN_RECORD_TODAY_EXISTS=H\u00F4m nay b\u1EA1n \u0111\u00E3 \u0111\u0103ng nh\u1EADp, vui l\u00F2ng kh\u00F4ng \u0111\u0103ng nh\u1EADp l\u1EA1i
LEVEL_NOT_EXISTS=C\u1EA5p \u0111\u1ED9 ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
LEVEL_NAME_EXISTS=T\u00EAn c\u1EA5p \u0111\u1ED9 ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
LEVEL_VALUE_EXISTS=Gi\u00E1 tr\u1ECB c\u1EA5p \u0111\u1ED9 ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
LEVEL_EXPERIENCE_MIN=Tr\u1EA3i nghi\u1EC7m n\u00E2ng c\u1EA5p ph\u1EA3i l\u1EDBn h\u01A1n tr\u1EA3i nghi\u1EC7m n\u00E2ng c\u1EA5p \u0111\u01B0\u1EE3c \u0111\u1EB7t cho c\u1EA5p \u0111\u1ED9 tr\u01B0\u1EDBc \u0111\u00F3.
LEVEL_EXPERIENCE_MAX=Kinh nghi\u1EC7m n\u00E2ng c\u1EA5p ph\u1EA3i \u00EDt h\u01A1n kinh nghi\u1EC7m n\u00E2ng c\u1EA5p \u0111\u01B0\u1EE3c \u0111\u1EB7t cho c\u1EA5p \u0111\u1ED9 ti\u1EBFp theo
LEVEL_HAS_USER=Ng\u01B0\u1EDDi d\u00F9ng t\u1ED3n t\u1EA1i \u1EDF c\u1EA5p \u0111\u1ED9 ng\u01B0\u1EDDi d\u00F9ng v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c.
EXPERIENCE_BIZ_NOT_SUPPORT=Lo\u1EA1i h\u00ECnh kinh doanh tr\u1EA3i nghi\u1EC7m ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3
GROUP_NOT_EXISTS=Nh\u00F3m ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
GROUP_HAS_USER=Ng\u01B0\u1EDDi d\u00F9ng t\u1ED3n t\u1EA1i trong nh\u00F3m ng\u01B0\u1EDDi d\u00F9ng v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c
USER_WITHDRAW_NOT_EXISTS=Vi\u1EC7c r\u00FAt th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_BALANCE_NOT_ENOUGH=Thi\u1EBFu c\u00E2n b\u1EB1ng
USER_WITHDRAW_HAS_HANDLE=Vi\u1EC7c r\u00FAt ti\u1EC1n \u0111\u00E3 \u0111\u01B0\u1EE3c x\u1EED l\u00FD
USER_WITHDRAW_LESS_MIN_AMOUNT=S\u1ED1 ti\u1EC1n r\u00FAt ph\u1EA3i l\u1EDBn h\u01A1n [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=S\u1ED1 ti\u1EC1n r\u00FAt ph\u1EA3i \u00EDt h\u01A1n [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=B\u1EA1n c\u00F3 m\u1ED9t \u0111\u01A1n \u0111\u1EB7t h\u00E0ng \u0111ang \u0111\u01B0\u1EE3c r\u00FAt l\u1EA1i
USER_FROZEN_BALANCE_NOT_ENOUGH=Thi\u1EBFu c\u00E2n b\u1EB1ng
USER_ASSETS_SPOT_NOT_EXISTS=T\u00E0i s\u1EA3n c\u1EE7a ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng t\u1ED3n t\u1EA1i
USER_FAVORITE_TRADE_PAIR_EXISTS=C\u1EB7p giao d\u1ECBch y\u00EAu th\u00EDch \u0111\u00E3 t\u1ED3n t\u1EA1i
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=C\u1EB7p giao d\u1ECBch y\u00EAu th\u00EDch kh\u00F4ng t\u1ED3n t\u1EA1i
USER_RECHARGE_NOT_EXISTS=B\u1EA3n ghi gi\u00E1 tr\u1ECB \u0111\u01B0\u1EE3c l\u01B0u tr\u1EEF kh\u00F4ng t\u1ED3n t\u1EA1i
USER_RECHARGE_HAS_HANDLE=B\u1EA3n ghi gi\u00E1 tr\u1ECB \u0111\u01B0\u1EE3c l\u01B0u tr\u1EEF \u0111\u00E3 \u0111\u01B0\u1EE3c x\u1EED l\u00FD
USER_WALLET_NOT_EXISTS=V\u00ED th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_SPOT_ORDER_NOT_EXISTS=Th\u1EE9 t\u1EF1 giao ngay c\u1EE7a th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_TRANSACTIONS_NOT_EXISTS=H\u00F3a \u0111\u01A1n th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_MARGIN_ORDER_NOT_EXISTS=\u0110\u01A1n \u0111\u1EB7t h\u00E0ng h\u1EE3p \u0111\u1ED3ng th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_MARGIN_CONFIG_NOT_EXISTS=C\u1EA5u h\u00ECnh h\u1EE3p \u0111\u1ED3ng th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_CERTIFICATION_NOT_EXISTS=Th\u00F4ng tin ch\u1EE9ng nh\u1EADn th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
USER_CERTIFICATION_BEEN_HANDLE=Th\u00F4ng tin x\u00E1c th\u1EF1c th\u00E0nh vi\u00EAn \u0111\u00E3 \u0111\u01B0\u1EE3c x\u1EED l\u00FD
USER_CERTIFICATION_STATUS_SUCCESS=Th\u00F4ng tin ch\u1EE9ng nh\u1EADn th\u00E0nh vi\u00EAn \u0111\u00E3 \u0111\u01B0\u1EE3c ch\u1EE9ng nh\u1EADn
USER_CERTIFICATION_STATUS_HANDLING=Th\u00F4ng tin ch\u1EE9ng nh\u1EADn th\u00E0nh vi\u00EAn \u0111ang \u0111\u01B0\u1EE3c xem x\u00E9t
USER_CERTIFICATION_NOT_VERIFY=Danh t\u00EDnh [{}], thao t\u00E1c kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p
USER_CERTIFICATION_VERIFYING=Trong qu\u00E1 tr\u00ECnh x\u00E1c th\u1EF1c danh t\u00EDnh, thao t\u00E1c kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p
USER_CERTIFICATION_VERIFY_FAILURE=X\u00E1c th\u1EF1c danh t\u00EDnh kh\u00F4ng th\u00E0nh c\u00F4ng, thao t\u00E1c kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p
LEVEL_CONFIG_NOT_EXISTS=C\u1EA5u h\u00ECnh c\u1EA5p th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Kh\u00F4ng th\u1EC3 x\u00F3a c\u1EA5u h\u00ECnh c\u1EA5p th\u00E0nh vi\u00EAn m\u1EB7c \u0111\u1ECBnh
LEVEL_CONFIG_NAME_EXISTS=T\u00EAn c\u1EA5p \u0111\u1ED9 \u0111\u00E3 t\u1ED3n t\u1EA1i
USER_FUND_PASSWORD_NOT_EXISTS=B\u1EA1n ch\u01B0a \u0111\u1EB7t m\u1EADt kh\u1EA9u qu\u1EF9 v\u00E0 kh\u00F4ng th\u1EC3 s\u1EEDa \u0111\u1ED5i n\u00F3.
USER_FUND_PASSWORD_ERROR=M\u1EADt kh\u1EA9u giao d\u1ECBch kh\u00F4ng \u0111\u00FAng
FUNDS_RECORD_NOT_EXISTS=H\u1ED3 s\u01A1 qu\u1EF9 kh\u00F4ng t\u1ED3n t\u1EA1i
USER_RECHARGE_LESS_MAX_PROCESS=B\u1EA1n c\u00F3 [{}] \u0111\u01A1n h\u00E0ng \u0111ang \u0111\u01B0\u1EE3c r\u00FAt
AUTH_LOGIN_CAPTCHA_CODE_ERROR=M\u00E3 x\u00E1c minh kh\u00F4ng ch\u00EDnh x\u00E1c, l\u00FD do: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=M\u00E3 th\u00F4ng b\u00E1o \u0111\u00E3 h\u1EBFt h\u1EA1n
AUTH_MOBILE_NOT_EXISTS=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng kh\u00F4ng t\u1ED3n t\u1EA1i
MENU_NAME_DUPLICATE=\u0110\u00E3 t\u1ED3n t\u1EA1i m\u1ED9t menu c\u00F3 t\u00EAn n\u00E0y
MENU_PARENT_NOT_EXISTS=Menu g\u1ED1c kh\u00F4ng t\u1ED3n t\u1EA1i
MENU_PARENT_ERROR=Kh\u00F4ng th\u1EC3 t\u1EF1 \u0111\u1EB7t ch\u00EDnh n\u00F3 l\u00E0m menu ch\u00EDnh
MENU_NOT_EXISTS=Th\u1EF1c \u0111\u01A1n kh\u00F4ng t\u1ED3n t\u1EA1i
MENU_EXISTS_CHILDREN=Menu con t\u1ED3n t\u1EA1i v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c
MENU_PARENT_NOT_DIR_OR_MENU=Lo\u1EA1i menu g\u1ED1c ph\u1EA3i l\u00E0 Catalog ho\u1EB7c Menu
ROLE_NOT_EXISTS=vai tr\u00F2 kh\u00F4ng t\u1ED3n t\u1EA1i
ROLE_NAME_DUPLICATE=M\u1ED9t nh\u00E2n v\u1EADt c\u00F3 t\u00EAn [{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
ROLE_CODE_DUPLICATE=Vai tr\u00F2 c\u00F3 m\u00E3 [{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Kh\u00F4ng th\u1EC3 v\u1EADn h\u00E0nh c\u00E1c vai tr\u00F2 \u0111\u01B0\u1EE3c t\u00EDch h\u1EE3p trong h\u1EC7 th\u1ED1ng
ROLE_IS_DISABLE=Nh\u00E2n v\u1EADt c\u00F3 t\u00EAn [{}] b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
ROLE_ADMIN_CODE_ERROR=Kh\u00F4ng th\u1EC3 s\u1EED d\u1EE5ng m\u00E3 h\u00F3a [{}]
USER_USERNAME_EXISTS=T\u00E0i kho\u1EA3n kh\u00F4ng c\u00F3 s\u1EB5n
USER_MOBILE_EXISTS=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng \u0111\u00E3 t\u1ED3n t\u1EA1i
USER_EMAIL_EXISTS=Email \u0111\u00E3 t\u1ED3n t\u1EA1i
USER_IMPORT_LIST_IS_EMPTY=Th\u00F4ng tin ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 nh\u1EADp kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
USER_PASSWORD_FAILED=X\u00E1c minh m\u1EADt kh\u1EA9u ng\u01B0\u1EDDi d\u00F9ng kh\u00F4ng th\u00E0nh c\u00F4ng
USER_IS_DISABLE=Ng\u01B0\u1EDDi d\u00F9ng c\u00F3 t\u00EAn [{}] b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
USER_COUNT_MAX=Kh\u00F4ng t\u1EA1o \u0111\u01B0\u1EE3c ng\u01B0\u1EDDi d\u00F9ng, l\u00FD do: v\u01B0\u1EE3t qu\u00E1 h\u1EA1n m\u1EE9c \u0111\u1ED1i t\u01B0\u1EE3ng thu\u00EA t\u1ED1i \u0111a c\u1EE7a \u0111\u1ED1i t\u01B0\u1EE3ng thu\u00EA [{}]
DEPT_NAME_DUPLICATE=M\u1ED9t b\u1ED9 ph\u1EADn c\u00F3 t\u00EAn n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
DEPT_PARENT_NOT_EXITS=B\u1ED9 ph\u1EADn ph\u1EE5 huynh kh\u00F4ng t\u1ED3n t\u1EA1i
DEPT_NOT_FOUND=Khoa hi\u1EC7n t\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
DEPT_EXITS_CHILDREN=Ph\u00E2n ng\u00E0nh t\u1ED3n t\u1EA1i v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c
DEPT_PARENT_ERROR=B\u1EA1n kh\u00F4ng th\u1EC3 t\u1EF1 \u0111\u1EB7t m\u00ECnh l\u00E0 b\u1ED9 ph\u1EADn ch\u00EDnh
DEPT_EXISTS_USER=C\u00F3 nh\u00E2n vi\u00EAn trong b\u1ED9 ph\u1EADn v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a.
DEPT_NOT_ENABLE=Khoa [{}] kh\u00F4ng m\u1EDF v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EF1a ch\u1ECDn
DEPT_PARENT_IS_CHILD=B\u1EA1n kh\u00F4ng th\u1EC3 \u0111\u1EB7t ph\u00F2ng ban ph\u1EE5 c\u1EE7a m\u00ECnh l\u00E0m ph\u00F2ng ban ch\u00EDnh
POST_NOT_FOUND=V\u1ECB tr\u00ED hi\u1EC7n t\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
POST_NOT_ENABLE=V\u1ECB tr\u00ED [{}] kh\u00F4ng m\u1EDF v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EF1a ch\u1ECDn
POST_NAME_DUPLICATE=\u0110\u00E3 t\u1ED3n t\u1EA1i m\u1ED9t v\u1ECB tr\u00ED c\u00F3 t\u00EAn n\u00E0y
POST_CODE_DUPLICATE=M\u1ED9t v\u1ECB tr\u00ED c\u00F3 logo n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
DICT_TYPE_NOT_EXISTS=Lo\u1EA1i t\u1EEB \u0111i\u1EC3n hi\u1EC7n t\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
DICT_TYPE_NOT_ENABLE=Lo\u1EA1i t\u1EEB \u0111i\u1EC3n kh\u00F4ng m\u1EDF v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EF1a ch\u1ECDn
DICT_TYPE_NAME_DUPLICATE=Lo\u1EA1i t\u1EEB \u0111i\u1EC3n c\u00F3 t\u00EAn n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
DICT_TYPE_TYPE_DUPLICATE=Lo\u1EA1i t\u1EEB \u0111i\u1EC3n n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
DICT_TYPE_HAS_CHILDREN=Kh\u00F4ng th\u1EC3 x\u00F3a \u0111\u01B0\u1EE3c, lo\u1EA1i t\u1EEB \u0111i\u1EC3n n\u00E0y c\u0169ng c\u00F3 d\u1EEF li\u1EC7u t\u1EEB \u0111i\u1EC3n
DICT_DATA_NOT_EXISTS=D\u1EEF li\u1EC7u t\u1EEB \u0111i\u1EC3n hi\u1EC7n t\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
DICT_DATA_NOT_ENABLE=D\u1EEF li\u1EC7u t\u1EEB \u0111i\u1EC3n [{}] kh\u00F4ng m\u1EDF v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EF1a ch\u1ECDn
DICT_DATA_VALUE_DUPLICATE=D\u1EEF li\u1EC7u t\u1EEB \u0111i\u1EC3n cho gi\u00E1 tr\u1ECB n\u00E0y \u0111\u00E3 t\u1ED3n t\u1EA1i
NOTICE_NOT_FOUND=Th\u00F4ng b\u00E1o th\u00F4ng b\u00E1o hi\u1EC7n kh\u00F4ng t\u1ED3n t\u1EA1i
SMS_CHANNEL_NOT_EXISTS=K\u00EAnh SMS kh\u00F4ng t\u1ED3n t\u1EA1i
SMS_CHANNEL_DISABLE=K\u00EAnh SMS kh\u00F4ng m\u1EDF v\u00E0 kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p l\u1EF1a ch\u1ECDn
SMS_CHANNEL_HAS_CHILDREN=Kh\u00F4ng th\u1EC3 x\u00F3a k\u00EAnh SMS n\u00E0y c\u0169ng c\u00F3 m\u1EABu SMS.
SMS_TEMPLATE_NOT_EXISTS=M\u1EABu SMS kh\u00F4ng t\u1ED3n t\u1EA1i
SMS_TEMPLATE_CODE_DUPLICATE=M\u1EABu tin nh\u1EAFn v\u0103n b\u1EA3n c\u00F3 m\u00E3 [{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
SMS_TEMPLATE_API_ERROR=Cu\u1ED9c g\u1ECDi m\u1EABu API SMS kh\u00F4ng th\u00E0nh c\u00F4ng do: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=Kh\u00F4ng th\u1EC3 s\u1EED d\u1EE5ng m\u1EABu API SMS, l\u00FD do: \u0111ang \u0111\u01B0\u1EE3c xem x\u00E9t
SMS_TEMPLATE_API_AUDIT_FAIL=Kh\u00F4ng th\u1EC3 s\u1EED d\u1EE5ng m\u1EABu API SMS, l\u00FD do: ph\u00EA duy\u1EC7t kh\u00F4ng th\u00E0nh c\u00F4ng, {}
SMS_TEMPLATE_API_NOT_FOUND=Kh\u00F4ng th\u1EC3 s\u1EED d\u1EE5ng m\u1EABu API SMS, l\u00FD do: m\u1EABu kh\u00F4ng t\u1ED3n t\u1EA1i
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Tham s\u1ED1 m\u1EABu [{}] b\u1ECB thi\u1EBFu
SMS_CODE_NOT_FOUND=M\u00E3 x\u00E1c minh kh\u00F4ng t\u1ED3n t\u1EA1i
SMS_CODE_EXPIRED=M\u00E3 x\u00E1c minh \u0111\u00E3 h\u1EBFt h\u1EA1n
SMS_CODE_USED=M\u00E3 x\u00E1c minh \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
SMS_CODE_NOT_CORRECT=M\u00E3 x\u00E1c minh kh\u00F4ng ch\u00EDnh x\u00E1c
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=\u0110\u00E3 v\u01B0\u1EE3t qu\u00E1 s\u1ED1 l\u01B0\u1EE3ng tin nh\u1EAFn v\u0103n b\u1EA3n \u0111\u01B0\u1EE3c g\u1EEDi h\u00E0ng ng\u00E0y
SMS_CODE_SEND_TOO_FAST=G\u1EEDi tin nh\u1EAFn v\u0103n b\u1EA3n qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn
TENANT_NOT_EXISTS=Ng\u01B0\u1EDDi thu\u00EA nh\u00E0 kh\u00F4ng t\u1ED3n t\u1EA1i
TENANT_DISABLE=Ng\u01B0\u1EDDi thu\u00EA c\u00F3 t\u00EAn [{}] \u0111\u00E3 b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
TENANT_EXPIRE=Ng\u01B0\u1EDDi thu\u00EA c\u00F3 t\u00EAn [{}] \u0111\u00E3 h\u1EBFt h\u1EA1n
TENANT_CAN_NOT_UPDATE_SYSTEM=Ng\u01B0\u1EDDi thu\u00EA h\u1EC7 th\u1ED1ng kh\u00F4ng th\u1EC3 th\u1EF1c hi\u1EC7n c\u00E1c thao t\u00E1c s\u1EEDa \u0111\u1ED5i, x\u00F3a, v.v.!
TENANT_CODE_DUPLICATE=\u0110\u1ED1i t\u01B0\u1EE3ng thu\u00EA c\u00F3 m\u00E3 \u0111\u1ED1i t\u01B0\u1EE3ng thu\u00EA [{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
TENANT_WEBSITE_DUPLICATE=Ng\u01B0\u1EDDi thu\u00EA c\u00F3 t\u00EAn mi\u1EC1n [{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
TENANT_PACKAGE_NOT_EXISTS=G\u00F3i thu\u00EA kh\u00F4ng t\u1ED3n t\u1EA1i
TENANT_PACKAGE_USED=Ng\u01B0\u1EDDi thu\u00EA \u0111ang s\u1EED d\u1EE5ng g\u00F3i n\u00E0y. Vui l\u00F2ng \u0111\u1EB7t l\u1EA1i g\u00F3i cho ng\u01B0\u1EDDi thu\u00EA v\u00E0 sau \u0111\u00F3 th\u1EED x\u00F3a n\u00F3.
TENANT_PACKAGE_DISABLE=G\u00F3i thu\u00EA c\u00F3 t\u00EAn [{}] \u0111\u00E3 b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
OAUTH2_CLIENT_NOT_EXISTS=\u1EE8ng d\u1EE5ng kh\u00E1ch OAuth2 kh\u00F4ng t\u1ED3n t\u1EA1i
OAUTH2_CLIENT_EXISTS=S\u1ED1 m\u00E1y kh\u00E1ch OAuth2 \u0111\u00E3 t\u1ED3n t\u1EA1i
OAUTH2_CLIENT_DISABLE=\u1EE8ng d\u1EE5ng kh\u00E1ch OAuth2 b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Lo\u1EA1i \u1EE7y quy\u1EC1n n\u00E0y kh\u00F4ng \u0111\u01B0\u1EE3c h\u1ED7 tr\u1EE3
OAUTH2_CLIENT_SCOPE_OVER=Ph\u1EA1m vi \u1EE7y quy\u1EC1n qu\u00E1 l\u1EDBn
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=redirect_uri kh\u00F4ng h\u1EE3p l\u1EC7:[{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=client_secret kh\u00F4ng h\u1EE3p l\u1EC7:{}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id kh\u00F4ng kh\u1EDBp
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri kh\u00F4ng kh\u1EDBp
OAUTH2_GRANT_STATE_MISMATCH=tr\u1EA1ng th\u00E1i kh\u00F4ng kh\u1EDBp
OAUTH2_GRANT_CODE_NOT_EXISTS=m\u00E3 kh\u00F4ng t\u1ED3n t\u1EA1i
OAUTH2_CODE_NOT_EXISTS=m\u00E3 kh\u00F4ng t\u1ED3n t\u1EA1i
OAUTH2_CODE_EXPIRE=M\u00E3 x\u00E1c minh \u0111\u00E3 h\u1EBFt h\u1EA1n
MAIL_ACCOUNT_NOT_EXISTS=T\u00E0i kho\u1EA3n email kh\u00F4ng t\u1ED3n t\u1EA1i
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Kh\u00F4ng th\u1EC3 x\u00F3a t\u00E0i kho\u1EA3n email n\u00E0y c\u0169ng c\u00F3 m\u1EABu email.
MAIL_TEMPLATE_NOT_EXISTS=M\u1EABu email kh\u00F4ng t\u1ED3n t\u1EA1i
MAIL_TEMPLATE_CODE_EXISTS=M\u00E3 m\u1EABu email[{}] \u0111\u00E3 t\u1ED3n t\u1EA1i
MAIL_SEND_TEMPLATE_PARAM_MISS=Tham s\u1ED1 m\u1EABu [{}] b\u1ECB thi\u1EBFu
MAIL_SEND_MAIL_NOT_EXISTS=Email kh\u00F4ng t\u1ED3n t\u1EA1i
MAIL_CODE_SEND_TOO_FAST=H\u1ED9p th\u01B0 \u0111\u01B0\u1EE3c g\u1EEDi qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=\u0110\u00E3 v\u01B0\u1EE3t qu\u00E1 s\u1ED1 l\u01B0\u1EE3ng email \u0111\u01B0\u1EE3c g\u1EEDi m\u1ED7i ng\u00E0y
MAIL_CODE_NOT_FOUND=M\u00E3 x\u00E1c minh kh\u00F4ng t\u1ED3n t\u1EA1i
MAIL_CODE_EXPIRED=M\u00E3 x\u00E1c minh \u0111\u00E3 h\u1EBFt h\u1EA1n
MAIL_CODE_USED=M\u00E3 x\u00E1c minh \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
MAIL_CODE_NOT_CORRECT=M\u00E3 x\u00E1c minh kh\u00F4ng ch\u00EDnh x\u00E1c
MAIL_IS_EXISTS=S\u1ED1 email \u0111\u00E3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng
NOTIFY_TEMPLATE_NOT_EXISTS=M\u1EABu th\u01B0 trang web kh\u00F4ng t\u1ED3n t\u1EA1i
NOTIFY_TEMPLATE_CODE_DUPLICATE=\u0110\u00E3 t\u1ED3n t\u1EA1i m\u1ED9t m\u1EABu th\u00F4ng b\u00E1o trang web \u0111\u01B0\u1EE3c m\u00E3 h\u00F3a l\u00E0 [{}]
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Tham s\u1ED1 m\u1EABu [{}] b\u1ECB thi\u1EBFu
AGENT_NOT_EXISTS=\u0110\u1EA1i l\u00FD kh\u00F4ng t\u1ED3n t\u1EA1i
AGENT_INVITE_CODE_EXISTS=M\u00E3 m\u1EDDi \u0111\u00E3 t\u1ED3n t\u1EA1i
AGENT_HAS_DESCENDANT=C\u00F3 \u0111\u1EA1i l\u00FD c\u1EA5p d\u01B0\u1EDBi d\u01B0\u1EDBi \u0111\u1EA1i l\u00FD v\u00E0 kh\u00F4ng th\u1EC3 x\u00F3a
AGENT_ANCESTOR_NOT_AVAILABLE=\u0110\u1EA1i l\u00FD g\u1ED1c kh\u00F4ng c\u00F3 s\u1EB5n
AUTH_NOT_EXISTS=X\u00E1c th\u1EF1c th\u00E0nh vi\u00EAn kh\u00F4ng t\u1ED3n t\u1EA1i
CURRENCY_NOT_EXISTS=Ti\u1EC1n t\u1EC7 kh\u00F4ng t\u1ED3n t\u1EA1i
CURRENCYNOTRATE=Hi\u1EC7n t\u1EA1i kh\u00F4ng c\u00F3 t\u1EF7 gi\u00E1 h\u1ED1i \u0111o\u00E1i cho lo\u1EA1i ti\u1EC1n n\u00E0y
BANNER_NOT_EXISTS=BANNER kh\u00F4ng t\u1ED3n t\u1EA1i
TENANT_SERVER_NAME_NOT_EXISTS=T\u00EAn mi\u1EC1n c\u1EE7a ng\u01B0\u1EDDi thu\u00EA h\u1EC7 th\u1ED1ng kh\u00F4ng t\u1ED3n t\u1EA1i
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Th\u00F4ng tin t\u1EEB \u0111i\u1EC3n ng\u01B0\u1EDDi thu\u00EA nh\u00E0 kh\u00F4ng t\u1ED3n t\u1EA1i
TIME_CONTRACT_AMOUNT_LESS=S\u1ED1 ti\u1EC1n mua \u00EDt h\u01A1n s\u1ED1 ti\u1EC1n t\u1ED1i thi\u1EC3u
TIME_CONTRACT_RECORD_NOT_EXISTS=H\u1ED3 s\u01A1 giao d\u1ECBch h\u1EE3p \u0111\u1ED3ng c\u00F3 th\u1EDDi h\u1EA1n kh\u00F4ng t\u1ED3n t\u1EA1i
TRADE_DURATION_NOT_EXISTS=C\u1EA5u h\u00ECnh th\u1EDDi h\u1EA1n \u0111\u1EB7t h\u00E0ng kh\u00F4ng t\u1ED3n t\u1EA1i
TRADE_MULTIPLE_NOT_EXISTS=C\u1EA5u h\u00ECnh h\u1EC7 s\u1ED1 \u0111\u01A1n h\u00E0ng kh\u00F4ng t\u1ED3n t\u1EA1i
USER_ACCOUNT_NOT_EMPTY=T\u00E0i kho\u1EA3n kh\u00F4ng th\u1EC3 tr\u1ED1ng
AGENT_HAS_NOT_ANCESTOR=\u0110\u1EA1i l\u00FD \u0111\u01B0\u1EE3c chuy\u1EC3n \u0111i kh\u00F4ng c\u00F3 b\u1EA5t k\u1EF3 \u0111\u1EA1i l\u00FD m\u1EB9 n\u00E0o
USER_EMAIL_NOT_EXISTS=Email kh\u00F4ng t\u1ED3n t\u1EA1i
Mail_CODE_SEND_FAIL=M\u00E3 x\u00E1c minh email kh\u00F4ng \u0111\u01B0\u1EE3c g\u1EEDi
#******** \u8C37\u6B4C\u9A8C\u8BC1\u76F8\u5173
GOOGLE_SECRET_BINDING_EXPIRED=Kh\u00F3a Google li\u00EAn k\u1EBFt \u0111\u00E3 h\u1EBFt h\u1EA1n, vui l\u00F2ng l\u1EA5y l\u1EA1i
GOOGLE_CODE_ERROR=M\u00E3 x\u00E1c minh c\u1EE7a Google kh\u00F4ng ch\u00EDnh x\u00E1c
ACCOUNT_IS_ERROR=\u0110\u0103ng nh\u1EADp th\u1EA5t b\u1EA1i, t\u00E0i kho\u1EA3n kh\u00F4ng t\u1ED3n t\u1EA1i
GOOGLE_SECRET_IS_NOT_BINDING=Kh\u00F3a Google kh\u00F4ng b\u1ECB r\u00E0ng bu\u1ED9c, vui l\u00F2ng li\u00EAn k\u1EBFt n\u00F3 tr\u01B0\u1EDBc
TRADE_CONTRACT_RECORD_NOT_EXISTS=L\u1EC7nh h\u1EE3p \u0111\u1ED3ng kh\u00F4ng t\u1ED3n t\u1EA1i
TRADE_CONTRACT_CONFIG_ERROR=L\u1ED7i c\u1EA5u h\u00ECnh h\u1EE3p \u0111\u1ED3ng
ARG_ORDER_STATUS_IS_EMPTY=Tr\u1EA1ng th\u00E1i \u0111\u01A1n h\u00E0ng kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
ARG_LEVERAGE_IS_EMPTY=\u0110\u1ED9 ph\u00F3ng \u0111\u1EA1i tham s\u1ED1 kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
ARG_ORDER_TYPE_IS_EMPTY=Lo\u1EA1i th\u1EE9 t\u1EF1 th\u00F4ng s\u1ED1 kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng
ARG_VALUE_ERROR=L\u1ED7i gi\u00E1 tr\u1ECB tham s\u1ED1
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=\u0110\u01A1n \u0111\u1EB7t h\u00E0ng h\u1EE3p \u0111\u1ED3ng \u0111\u00E3 b\u1ECB h\u1EE7y
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=\u0110\u01A1n h\u00E0ng h\u1EE3p \u0111\u1ED3ng \u0111\u00E3 \u0111\u01B0\u1EE3c ho\u00E0n th\u00E0nh
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=H\u1EE3p \u0111\u1ED3ng \u0111\u00E3 \u0111\u01B0\u1EE3c \u0111\u00F3ng
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=L\u1ED7i t\u1EF7 gi\u00E1 h\u1ED1i \u0111o\u00E1i l\u1EC7nh
USER_FORBIDDEN_FUNC=T\u00EDnh n\u0103ng n\u00E0y b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a
IMAGE_URL_ERROR=L\u1ED7i li\u00EAn k\u1EBFt h\u00ECnh \u1EA3nh
MINER_HAS_ORDER=\u0110\u01A1n \u0111\u1EB7t h\u00E0ng s\u1EA3n ph\u1EA9m n\u00E0y c\u1EE7a b\u1EA1n ch\u01B0a ho\u00E0n t\u1EA5t
MINER_NOT_EXISTS=D\u1EEF li\u1EC7u kh\u00F4ng kh\u1EA3 d\u1EE5ng
MINER_CYCLE_ERR=Th\u1EDDi gian khai th\u00E1c s\u1EA3n ph\u1EA9m n\u00E0y \u00EDt h\u01A1n 1 ng\u00E0y
MINER_AMOUNT_LIMIT=S\u1ED1 ti\u1EC1n \u0111\u1EA7u t\u01B0 c\u1EE7a b\u1EA1n \u00EDt h\u01A1n s\u1ED1 ti\u1EC1n \u0111\u1EA7u t\u01B0 t\u1ED1i thi\u1EC3u
MINER_AMOUNT_MAX_LIMIT=S\u1ED1 ti\u1EC1n \u0111\u1EA7u t\u01B0 c\u1EE7a b\u1EA1n l\u1EDBn h\u01A1n s\u1ED1 ti\u1EC1n \u0111\u1EA7u t\u01B0 t\u1ED1i \u0111a
MINER_ORDER_STATUS_ERROR=Tr\u1EA1ng th\u00E1i \u0111\u01A1n h\u00E0ng c\u1EE7a b\u1EA1n kh\u00F4ng h\u1EE3p l\u1EC7
